# Unreport Deadline Recalculation Implementation

## Overview

This implementation adds deadline recalculation functionality to the management portal when unreporting companies. Previously, when a company was unreported, the deadline was not recalculated, requiring clients to wait for the function app to run to get updated deadlines.

## Changes Made

### 1. Added Helper Functions

**File:** `controllers/financial-report-management/financialReportController.js`

Added two helper functions copied from the function app:

- `getAccountingDeadline(accountingModule, financialReports)` - Calculates new accounting and filing deadlines based on company's financial reports
- `getDeadlinePenalty(overdueDays, penaltyRanges)` - Calculates penalty amounts based on overdue days (already existed, kept for consistency)

### 2. Modified Single Company Unreporting

**Function:** `reportCompany`

When unreporting a single company (`req.body.isReported = false`), the system now:

1. Sets the company as unreported and compliant
2. **NEW:** Fetches all financial reports for the company
3. **NEW:** Calculates new deadline using `getAccountingDeadline()`
4. **NEW:** Updates `currentDeadline` and `currentFilingDeadline` if different
5. **NEW:** Recalculates penalty amount based on new deadline
6. **NEW:** Logs the deadline update process
7. Continues with existing penalty report cleanup logic

### 3. Modified Bulk Company Unreporting

**Function:** `bulkReportCompanies`

When bulk unreporting companies, the system now:

1. Sets each company as unreported and compliant
2. **NEW:** Fetches financial reports for each company
3. **NEW:** Calculates and updates deadlines for each company
4. **NEW:** Logs deadline updates in the Excel result file
5. **NEW:** Handles errors gracefully and logs them in the result file
6. Continues with existing penalty report cleanup logic

## Deadline Calculation Logic

The deadline calculation follows the same logic as the function app:

### No Financial Reports
- **Pre-2024 companies:** Period end + 18 months
- **2024+ companies:** Period end + 9 months

### One Financial Report
- **Pending reports (SAVED/IN PROGRESS/IN PENALTY):** Report period end + 9/18 months (based on start date)
- **Confirmed reports:** Next period end (report end + 1 year) + 18 months

### Multiple Financial Reports
- Uses the last submitted report's period end + 1 year + 18 months

## Error Handling

- Deadline recalculation errors don't prevent unreporting from completing
- Errors are logged to console and (for bulk operations) to the result Excel file
- If deadline calculation fails, the company is still unreported successfully

## Testing

A test script `test-unreport-deadline-recalculation.js` has been created to verify the deadline calculation logic works correctly with various scenarios.

## Benefits

1. **Immediate deadline updates:** Companies get updated deadlines immediately when unreported
2. **No waiting for function app:** Clients don't need to wait for the scheduled function app to run
3. **Consistent logic:** Uses the same calculation logic as the function app
4. **Robust error handling:** Unreporting continues even if deadline calculation fails
5. **Audit trail:** All deadline updates are logged for tracking

## Files Modified

- `controllers/financial-report-management/financialReportController.js` - Main implementation
- `test-unreport-deadline-recalculation.js` - Test script (new)
- `UNREPORT_DEADLINE_RECALCULATION.md` - This documentation (new)

## Usage

The functionality is automatically enabled and requires no additional configuration. When users unreport companies through the management portal (either single or bulk operations), deadlines will be recalculated immediately.

## Monitoring

Check the console logs for deadline recalculation messages:
- `Recalculating deadline for company {code} after unreporting`
- `Found {count} financial reports for company {code}`
- `Updating deadline from {old} to {new}`
- `New deadline: {date}, Days to deadline: {days}, Penalty: {amount}`

For bulk operations, check the Excel result file for deadline update entries.
