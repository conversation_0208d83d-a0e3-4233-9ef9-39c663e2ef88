const CompanyIncorporationModel = require('../../models/clientIncorporation');
const excel = require("node-excel-export");
const moment = require("moment");

exports.doClientIncorporationExportXls = async function (req, res) {
  try {
    let incorporationIds = req.body["incorporationIds"].split(";");
    let records = await CompanyIncorporationModel.find({"_id": {"$in": incorporationIds} });

    const styles = {
      headerGreen: {
        fill: {
          fgColor: {
            rgb: "99ffeb",
          },
        },
        font: {
          color: {
            rgb: "006600",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerBluePastel: {
        fill: {
          fgColor: {
            rgb: "6f94dc",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerBlueKing: {
        fill: {
          fgColor: {
            rgb: "3333ff",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerBlueGrey: {
        fill: {
          fgColor: {
            rgb: "19334d",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerOrange: {
        fill: {
          fgColor: {
            rgb: "ff5500",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerPurple: {
        fill: {
          fgColor: {
            rgb: "6600ff",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerPurpleOutsource: {
        fill: {
          fgColor: {
            rgb: "cc0099",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerGrey: {
        fill: {
          fgColor: {
            rgb: "808080",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerGreenDark: {
        fill: {
          fgColor: {
            rgb: "009933",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerRedDark: {
        fill: {
          fgColor: {
            rgb: "cc0000",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerYellowMustard: {
        fill: {
          fgColor: {
            rgb: "cc7a00",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
    };

    const specificationGeneral = {
      entityUniqueID: {
        displayName: "Entity Unique ID",
        headerStyle: styles.headerGreen,
        width: 120,
      },
      type: {
        displayName: "Entity Type",
        headerStyle: styles.headerGreen,
        width: 150,
      },
      status: {
        displayName: "Status",
        headerStyle: styles.headerGreen,
        width: 150,
      },
      createdBy: {
        displayName: "Created By",
        headerStyle: styles.headerGreen,
        width: 150,
      },
      submittedBy: {
        displayName: "Submitted By",
        headerStyle: styles.headerGreen,
        width: 150,
      },
      partOfGroup: {
        displayName: "Is the entity part of a group/structure?",
        headerStyle: styles.headerGreen,
        width: 260,
      },
      principalBusinessActivity: {
        displayName: "Principal business activities",
        headerStyle: styles.headerBlueKing,
        width: 200,
      },
      activityJurisdiction: {
        displayName: "Jurisdiction place",
        headerStyle: styles.headerBlueKing,
        width: 120,
      },
      taxResidence: {
        displayName: "Tax Residence",
        headerStyle: styles.headerBlueKing,
        width: 120,
      },
      taxResidencyJurisdiction: {
        displayName: "Jurisdiction of Tax Residency",
        headerStyle: styles.headerBlueKing,
        width: 200,
      },
      ownAssets: {
        displayName: "Will the company own any assets legally or benefically?",
        headerStyle: styles.headerOrange,
        width: 260,
      },
      recordHolder: {
        displayName: "Record Holder",
        headerStyle: styles.headerBluePastel,
        width: 200,
      },
      primaryAddress: {
        displayName: "Address Line1",
        headerStyle: styles.headerBluePastel,
        width: 200,
      },
      secondaryAddress: {
        displayName: "Address Line2",
        headerStyle: styles.headerBluePastel,
        width: 200,
      },
      email: {
        displayName: "E-mail",
        headerStyle: styles.headerBluePastel,
        width: 200,
      },
      state: {
        displayName: "State",
        headerStyle: styles.headerBluePastel,
        width: 200
      },
      city: {
        displayName: "City",
        headerStyle: styles.headerBluePastel,
        width: 200,
      },
      postalCode: {
        displayName: "Postal Code",
        headerStyle: styles.headerBluePastel,
        width: 200,
      },
      operationCountry: {
        displayName: "Country of Operation",
        headerStyle: styles.headerBluePastel,
        width: 200,
      },
      requestAdditionalServices: {
        displayName: "Would you like to request additional services?",
        headerStyle: styles.headerPurpleOutsource,
        width: 260,
      },
      additionalServices: {
        displayName: "Additional Services",
        headerStyle: styles.headerPurpleOutsource,
        width: 260,
      },
      createdAt: {
        displayName: "Created At",
        headerStyle: styles.headerGreen,
        width: 150,
      },
      updatedAt: {
        displayName: "Updated At",
        headerStyle: styles.headerGreen,
        width: 150,
      },
      submittedAt: {
        displayName: "Submitted At",
        headerStyle: styles.headerGreen,
        width: 150,
      },
    };
    const datasetGeneral = [];

    for (let i = 0; i < records.length; i++) {
      const incorporation = records[i];
      let additionalServices = [];
      if (incorporation.additionalServices.length > 0) {
        additionalServices = incorporation.additionalServices;
      }

      if (incorporation.otherServices){
        additionalServices.push(incorporation.otherServices);
      }

      let exportIncorporation = {
        entityUniqueID: incorporation.name,
        type: incorporation.type === "Special Instructions" ?
          incorporation.typeSpecialInstructions : incorporation.type,
        status: incorporation.status,
        createdBy: incorporation.createdBy,
        submittedBy: incorporation.submittedBy,
        partOfGroup: incorporation.partOfGroup ? "Yes": "No",
        principalBusinessActivity: incorporation.principalBusinessActivity === "Other" ?
          incorporation.principalBusinessActivityOther : incorporation.principalBusinessActivity,
        activityJurisdiction: incorporation.activityJurisdiction,
        taxResidence: incorporation.taxResidence,
        taxResidencyJurisdiction: incorporation.taxResidence === "Foreign"
          ? incorporation.taxResidencyJurisdiction : '',
        ownAssets: incorporation.ownAssets ? "Yes": "No",
        recordHolder: incorporation.records && incorporation.records.recordHolder ?
          incorporation.records.recordHolder : '',
        primaryAddress: incorporation.records && incorporation.records.primaryAddress ?
          incorporation.records.primaryAddress : '',
        secondaryAddress: incorporation.records && incorporation.records.secondaryAddress ?
          incorporation.records.secondaryAddress : '',
        email: incorporation.records && incorporation.records.email ?
          incorporation.records.email : '',
        state: incorporation.records && incorporation.records.state ?
          incorporation.records.state : '',
        city: incorporation.records && incorporation.records.city ?
          incorporation.records.city : '',
        postalCode: incorporation.records && incorporation.records.postalCode ?
          incorporation.records.postalCode : '',
        operationCountry: incorporation.records && incorporation.records.operationCountry ?
          incorporation.records.operationCountry : '',
        requestAdditionalServices: incorporation.requestAdditionalServices ? "Yes": "No",
        additionalServices: incorporation.requestAdditionalServices ? additionalServices.join(', ') : '',
        createdAt: moment(incorporation.createdAt).format('YYYY-MM-DD HH:mm'),
        updatedAt: moment(incorporation.updatedAt).format('YYYY-MM-DD HH:mm'),
        submittedAt: moment(incorporation.submittedAt).format('YYYY-MM-DD HH:mm'),
      };

      datasetGeneral.push(exportIncorporation);
    }

    const report = excel.buildExport([
      {
        name: "General",
        specification: specificationGeneral,
        data: datasetGeneral,
      },
    ]);
    let countRecords = 0;
    records.forEach(function (documentToUpdate) {
      CompanyIncorporationModel.findOneAndUpdate(
        {_id: documentToUpdate._id, company: documentToUpdate.company},
        {
          exportedAt: new Date(),
          exportedBy: req.session.user.username,
        },
        function () {
          countRecords++;
          if (countRecords === records.length) {
            res.attachment("Report.xlsx");
            return res.send(report);
          }
        }
      );
    });
  } catch (err) {
    console.log("error");
    console.log(err);
    res.send(err);
  }
};

exports.doSearchIncorporationsExportXls = async function (req, res) {
  try {
    console.log("query ", req.query);

    let searchQuery = [];
    let records;
    if (req.query.search && req.query.search.length > 2) {
      const filter = req.query.search;
      searchQuery.push({
        $or: [
          {'name': {$regex: filter, $options: 'i'}},
          {'code': {$regex: filter, $options: 'i'}},
          {'masterClientCode': {$regex:filter, $options: 'i'}}
        ],
      });

    }

    if (req.query.showIncomplete === "true") {
      searchQuery.push({"status": {"$ne": "COMPLETED"}});
    }

    if (searchQuery.length > 0){
      records = await CompanyIncorporationModel.find({ "$and": searchQuery});
    }
    else{
      records = await CompanyIncorporationModel.find({});
    }

    const styles = {
      headerGreen: {
        fill: {
          fgColor: {
            rgb: "99ffeb",
          },
        },
        font: {
          color: {
            rgb: "006600",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerBluePastel: {
        fill: {
          fgColor: {
            rgb: "6f94dc",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerBlueKing: {
        fill: {
          fgColor: {
            rgb: "3333ff",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerBlueGrey: {
        fill: {
          fgColor: {
            rgb: "19334d",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerOrange: {
        fill: {
          fgColor: {
            rgb: "ff5500",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerPurple: {
        fill: {
          fgColor: {
            rgb: "6600ff",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerPurpleOutsource: {
        fill: {
          fgColor: {
            rgb: "cc0099",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerGrey: {
        fill: {
          fgColor: {
            rgb: "808080",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerGreenDark: {
        fill: {
          fgColor: {
            rgb: "009933",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerRedDark: {
        fill: {
          fgColor: {
            rgb: "cc0000",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerYellowMustard: {
        fill: {
          fgColor: {
            rgb: "cc7a00",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
    };

    const specificationGeneral = {
      companyName: {
        displayName: "Company Name",
        headerStyle: styles.headerGreen,
        width: 150,
      },
      masterClientCode: {
        displayName: "MCC",
        headerStyle: styles.headerGreen,
        width: 100,
      },
      type: {
        displayName: "Company Type",
        headerStyle: styles.headerGreen,
        width: 300,
      },
      createdBy: {
        displayName: "Created By",
        headerStyle: styles.headerGreen,
        width: 250,
      },
      submittedBy: {
        displayName: "Submitted By",
        headerStyle: styles.headerGreen,
        width: 250,
      },
      submittedAt: {
        displayName: "Submitted At",
        headerStyle: styles.headerGreen,
        width: 150,
      },
      assignedTo: {
        displayName: "Assigned to",
        headerStyle: styles.headerGreen,
        width: 250,
      },
      incorporationStatus: {
        displayName: "Incorporation Status",
        headerStyle: styles.headerGreen,
        width: 200,
      },
      submissionStatus: {
        displayName: "Submission Status",
        headerStyle: styles.headerGreen,
        width: 120,
      },
      paid: {
        displayName: "Paid",
        headerStyle: styles.headerGreen,
        width: 120,
      },
      partOfGroup: {
        displayName: "Is the entity part of a group/structure?",
        headerStyle: styles.headerGreen,
        width: 260,
      },
      principalBusinessActivity: {
        displayName: "Principal business activities",
        headerStyle: styles.headerBlueKing,
        width: 200,
      },
      activityJurisdiction: {
        displayName: "Jurisdiction place",
        headerStyle: styles.headerBlueKing,
        width: 120,
      },
      taxResidence: {
        displayName: "Tax Residence",
        headerStyle: styles.headerBlueKing,
        width: 120,
      },
      taxResidencyJurisdiction: {
        displayName: "Jurisdiction of Tax Residency",
        headerStyle: styles.headerBlueKing,
        width: 200,
      },
      ownAssets: {
        displayName: "Will the company own any assets legally or benefically?",
        headerStyle: styles.headerOrange,
        width: 260,
      },
      recordHolder: {
        displayName: "Record Holder",
        headerStyle: styles.headerBluePastel,
        width: 200,
      },
      primaryAddress: {
        displayName: "Address Line1",
        headerStyle: styles.headerBluePastel,
        width: 200,
      },
      secondaryAddress: {
        displayName: "Address Line2",
        headerStyle: styles.headerBluePastel,
        width: 200,
      },
      email: {
        displayName: "E-mail",
        headerStyle: styles.headerBluePastel,
        width: 200,
      },
      state: {
        displayName: "State",
        headerStyle: styles.headerBluePastel,
        width: 200
      },
      city: {
        displayName: "City",
        headerStyle: styles.headerBluePastel,
        width: 200,
      },
      postalCode: {
        displayName: "Postal Code",
        headerStyle: styles.headerBluePastel,
        width: 200,
      },
      operationCountry: {
        displayName: "Country of Operation",
        headerStyle: styles.headerBluePastel,
        width: 200,
      },
      requestAdditionalServices: {
        displayName: "Would you like to request additional services?",
        headerStyle: styles.headerPurpleOutsource,
        width: 260,
      },
      additionalServices: {
        displayName: "Additional Services",
        headerStyle: styles.headerPurpleOutsource,
        width: 260,
      },
      createdAt: {
        displayName: "Created At",
        headerStyle: styles.headerGreen,
        width: 150,
      },
      updatedAt: {
        displayName: "Updated At",
        headerStyle: styles.headerGreen,
        width: 150,
      },
    };
    const datasetGeneral = [];

    for (let i = 0; i < records.length; i++) {
      const incorporation = records[i];
      let additionalServices = [];
      if (incorporation.additionalServices.length > 0) {
        additionalServices = incorporation.additionalServices;
      }

      if (incorporation.otherServices){
        additionalServices.push(incorporation.otherServices);
      }

      let exportIncorporation = {
        companyName: incorporation.name,
        masterClientCode: incorporation.masterClientCode,
        type: incorporation.type === "Special Instructions" ?
          incorporation.typeSpecialInstructions : incorporation.type,
        createdBy: incorporation.createdBy,
        submittedBy: incorporation.submittedBy,
        submittedAt: incorporation.submittedAt ? moment(incorporation.submittedAt).format('YYYY-MM-DD HH:mm') : '',
        incorporationStatus: incorporation.incorporationStatus,
        submissionStatus: incorporation.status,
        assignedTo: incorporation.user && incorporation.user.username ? incorporation.user.username.toLowerCase() : '',
        paid: incorporation.payment && incorporation.payment.paidAt ? moment(incorporation.payment.paidAt ).format('YYYY-MM-DD HH:mm') : '',
        partOfGroup: incorporation.partOfGroup ? "Yes": "No",
        principalBusinessActivity: incorporation.principalBusinessActivity === "Other" ?
          incorporation.principalBusinessActivityOther : incorporation.principalBusinessActivity,
        activityJurisdiction: incorporation.activityJurisdiction,
        taxResidence: incorporation.taxResidence,
        taxResidencyJurisdiction: incorporation.taxResidence === "Foreign"
          ? incorporation.taxResidencyJurisdiction : '',
        ownAssets: incorporation.ownAssets ? "Yes": "No",
        recordHolder: incorporation.records && incorporation.records.recordHolder ?
          incorporation.records.recordHolder : '',
        primaryAddress: incorporation.records && incorporation.records.primaryAddress ?
          incorporation.records.primaryAddress : '',
        secondaryAddress: incorporation.records && incorporation.records.secondaryAddress ?
          incorporation.records.secondaryAddress : '',
        email: incorporation.records && incorporation.records.email ?
          incorporation.records.email : '',
        state: incorporation.records && incorporation.records.state ?
          incorporation.records.state : '',
        city: incorporation.records && incorporation.records.city ?
          incorporation.records.city : '',
        postalCode: incorporation.records && incorporation.records.postalCode ?
          incorporation.records.postalCode : '',
        operationCountry: incorporation.records && incorporation.records.operationCountry ?
          incorporation.records.operationCountry : '',
        requestAdditionalServices: incorporation.requestAdditionalServices ? "Yes": "No",
        additionalServices: incorporation.requestAdditionalServices ? additionalServices.join(', ') : '',
        createdAt: moment(incorporation.createdAt).format('YYYY-MM-DD HH:mm'),
        updatedAt: moment(incorporation.updatedAt).format('YYYY-MM-DD HH:mm'),
      };

      datasetGeneral.push(exportIncorporation);
    }

    const report = excel.buildExport([
      {
        name: "General",
        specification: specificationGeneral,
        data: datasetGeneral,
      },
    ]);
    let countRecords = 0;
    records.forEach(function (documentToUpdate) {
      CompanyIncorporationModel.findOneAndUpdate(
        {_id: documentToUpdate._id, company: documentToUpdate.company},
        {
          exportedAt: new Date(),
          exportedBy: req.session.user.username,
        },
        function () {
          countRecords++;
          if (countRecords === records.length) {
            res.attachment("Report.xlsx");
            return res.send(report);
          }
        }
      );
    });
  } catch (err) {
    console.log("error");
    console.log(err);
    res.send(err);
  }
};
