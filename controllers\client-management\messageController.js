const MasterClientCodeModel = require("../../models/masterClientCode");
const MasterClientMessageModel = require("../../models/masterClientMessage");
const MessageModel = require("../../models/message");
const {timeHours} = require('../../utils/constants');
const moment = require("moment");
const {v4: uuidv4} = require('uuid');
const uploadController = require('../client-management/uploadMessageFilesController');
const ObjectId = require('mongoose').Types.ObjectId;

exports.getMessageDashboard = async function (req, res) {
  try {
    let announcements = await MessageModel.find({}).sort({'scheduledAt': -1}).limit(1000);

    if (announcements.length > 0) {
      announcements = announcements.map((announcement) => {

        if (announcement.subject.length > 70) {
          announcement.subject = announcement.subject.substring(0, 50) + '...';
        }

        announcement.masterClientCodes = announcement.masterClientCodes.length > 5  ?
          announcement.masterClientCodes.slice(0, 5).join(', ') + '...' :
          announcement.masterClientCodes.join(', ');
        return announcement;
      })
    }

    res.render("client-management/announcement-list", {
      user: req.session.user,
      title: "Announcements",
      announcements: announcements,
      newAnnouncementAlowed: ((req.session.authentication.isSubsManagers || req.session.authentication.isSubsSuperUser) && req.session.authentication.isAnnouncementManager)
    });

  } catch (e) {
    console.log("Error ", e);
    res.redirect("/");
  }

};

exports.getMessageView = async function (req, res, next) {
  try {
    let announcement;
    let message;
    let selectedMasterClients;
    req.session.messageFiles = {};
    if (req.params.messageId) {
      announcement = await MessageModel.findOne({_id: req.params.messageId});

      if (!announcement) {
        let err = new Error('Announcement not found');
        err.status = 404;
        return next(err);
      }
    }
    let currentDate = new Date().toISOString();

    if (announcement) {
      message = {
        _id: announcement._id,
        masterClientCodes: announcement.sendToAll ? [] : announcement.masterClientCodes,
        subject: announcement.subject,
        emailSubject: announcement.emailSubject ?announcement.emailSubject : announcement.subject ,
        content: announcement.content,
        scheduledDate: moment(announcement.scheduledAt).format('YYYY-MM-DD'),
        scheduledTime: new Date(announcement.scheduledAt).getUTCHours().toString(),
        scheduleMessage: announcement.scheduleMessage,
        sendToAll: announcement.sendToAll,
        isPartialEdit: announcement.status !== "SCHEDULED",
        files: announcement.files ? announcement.files : []
      };

      if (!announcement.sendToAll) {
        selectedMasterClients = await MasterClientCodeModel.find({"code": {"$in": message.masterClientCodes}}, {
          code: 1,
          owners: 1
        });
      }
    }

    res.render("client-management/open-announcement", {
      user: req.session.user,
      title: message ? "Edit Announcement" : 'New Announcement',
      isEdit: !!message,
      message: message,
      timeHours: timeHours,
      selectedMasterClients: selectedMasterClients,
      messageId: message ? message._id : ObjectId().toString(),
      currentDate: moment(currentDate).format('YYYY-MM-DD')
    });

  } catch (e) {
    console.log("Error ", e);
    res.redirect("/");
  }

};

exports.createMessage = async function (req, res) {
  try {

    console.log("req.body ", req.body);

    if (!req.body.content || !req.body.subject || !req.body.sendToAll || !req.body.sendNow) {
      return res.status(400).json({
        status: 400,
        error: "Missing required fields"
      })
    }
    if (req.body.sendToAll === "NO" && !req.body.masterClientCodes) {
      return res.status(400).json({
        status: 400,
        error: "Add a least one Master client."
      })
    }

    let masterClients;
    if (req.body.sendToAll === 'YES') {
      masterClients = [];
    } else {
      masterClients = await MasterClientCodeModel.find({code: {"$in": req.body.masterClientCodes}}, {code: 1});
      masterClients = masterClients.map((mcc) => {
        return mcc.code
      });
    }



    let scheduledAt;
    if (req.body.sendNow !== 'YES') {
      if (!req.body.scheduleDate || !req.body.scheduleTime) {
        return res.status(400).json({
          status: 400,
          error: "Missing scheduled date or scheduled time"
        })
      }
      scheduledAt = req.body.scheduledAt;
    } else if (req.body.sendNow === 'YES') {
      let hoursPeriod = 60 * 60 * 1000;
      let currentDate = new Date();
      scheduledAt = new Date(Math.ceil(currentDate.getTime() / hoursPeriod) * hoursPeriod);
    }
    let newMessage = new MessageModel({
      _id: req.body.mid ? ObjectId(req.body.mid) : ObjectId(),
      subject: req.body.subject,
      emailSubject: req.body.emailSubject ? req.body.emailSubject :req.body.subject,
      content: req.body.content,
      sendToAll: req.body.sendToAll === 'YES',
      masterClientCodes: masterClients,
      scheduleMessage: req.body.sendNow !== 'YES',
      scheduledAt: scheduledAt,
      status: 'SCHEDULED',
    });

    let sessData = req.session;

    if (sessData.messageFiles){
      let newFiles = sessData.messageFiles[newMessage._id.toString()]
      newMessage.files = newFiles ? newFiles : []
    }

    await newMessage.save();

    return res.status(200).json({
      status: 200,
      message: "Announcement created successfully"
    })

  } catch (e) {
    return res.status(500).json({
      status: 500,
      error: "Internal Server Error"
    })
  }

};

exports.updateMessage = async function (req, res, next) {
  try {

    let message = await MessageModel.findById(req.params.messageId);

    if (!message) {
      let err = new Error('Announcement not found');
      err.status = 404;
      return next(err);
    }

    if (!req.body.content || !req.body.subject) {
      return res.status(400).json({
        status: 400,
        error: "Missing required fields"
      })
    }

    message.subject = req.body.subject;
    message.emailSubject = req.body.emailSubject ? req.body.emailSubject :req.body.subject;
    message.content = req.body.content;
    if(!message.files){
      message.files = [];
    }

    if (message.status === "SCHEDULED") {
      if (!req.body.sendToAll || !req.body.sendNow) {
        return res.status(400).json({
          status: 400,
          error: "Missing required fields"
        })
      }

      if (req.body.sendToAll === "NO" && !req.body.masterClientCodes) {
        return res.status(400).json({
          status: 400,
          error: "Add a least one Master client."
        })
      }

      message.sendToAll = req.body.sendToAll === 'YES';
      message.scheduleMessage = req.body.sendNow !== 'YES';

      if (req.body.sendNow !== 'YES') {
        if (!req.body.scheduleDate || !req.body.scheduleTime) {
          return res.status(400).json({status: 400, error: "Missing scheduled date or scheduled time"})
        }
        message.scheduledAt = req.body.scheduledAt;
      } else {
        let hoursPeriod = 60 * 60 * 1000;
        let currentDate = new Date();
        message.scheduledAt = new Date(Math.ceil(currentDate.getTime() / hoursPeriod) * hoursPeriod);
      }

      let masterClientCodes = [];

      if (message.sendToAll === true) {
        masterClientCodes = [];
      } else {
        for (let i = 0; i < req.body.masterClientCodes.length; i++) {
          let mcc = await MasterClientCodeModel.findOne({code: req.body.masterClientCodes[i]});

          if (!mcc) {
            return res.status(400).json({
              status: 400,
              error: "Master client with code " + req.body.masterClientCodes[i] + " not found"
            })
          }
          masterClientCodes.push(mcc.code);
        }
      }
      message.masterClientCodes = masterClientCodes;
    }

    let sessData = req.session;
    if (sessData.messageFiles){
      let newFiles = sessData.messageFiles[message._id.toString()];

      message.files = newFiles ? [...message.files, ...newFiles] : message.files;
    }

    message.updatedAt = new Date();
    message.markModified('masterClientCodes');
    message.markModified('files');
    await message.save();
    return res.status(200).json({
      status: 200,
      error: "Announcement updated successfully"
    })
  } catch (e) {
    console.log(e);
    return res.status(500).json({
      status: 500,
      error: "Internal Server Error"
    })
  }

};

exports.getMessageDetails = async function (req, res) {
  try {
    let message = await MessageModel.findById(req.params.messageId);

    if (!message) {
      return res.status(404).json({
        status: 404,
        error: "Announcement not found"
      })
    }

    let announcement = {
      id: message._id,
      subject: message.subject,
      emailSubject: message.emailSubject ? message.emailSubject : message.subject,
      content: message.content,
      sendToAll: message.sendToAll,
      scheduleMessage: message.scheduleMessage,
      masterClientCodes: message.sendToAll ? 'ALL MASTER CLIENTS' : message.masterClientCodes.join(', '),
      scheduledAt: moment(message.scheduledAt).utc().format('MM/DD/YYYY HH:mm:ss'),
      files: message.files ? message.files.map((f) => {return {originalName: f.originalName, fileId: f.fileId}}) : []
    };

    return res.status(200).json({
      status: 200,
      announcement: announcement
    })
  } catch (e) {
    console.log(e);
    return res.status(500).json({
      status: 500,
      error: "Internal Server Error"
    })
  }

};


exports.deleteMessage = async function (req, res) {
  try {
    let message = await MessageModel.findById(req.params.messageId);
    if (!message) {
      return res.status(404).json({status: 404, error: "Announcement not found"});
    }


    for (let i = 0; i < message.masterClientCodes.length; i++) {
      await MasterClientMessageModel.findOneAndDelete({
        messageId: message._id, masterClientCode: message.masterClientCodes[i]
      });
    }

    await MessageModel.findByIdAndDelete(req.params.messageId);
    return res.status(200).json({
      status: 200,
      message: "Announcement has been deleted successfully"
    });
  } catch (error) {
    return res.status(500).json({status: 500, error: "Error deleting the announcement"});
  }
};


exports.storeTempMessageFiles = function (req, res) {
  try {
    let sessData = req.session;
    let messageId = req.body.mid;
    if (!sessData.messageFiles) {
      sessData.messageFiles = {}
    }

    const fileName = req.body.filename.replace(/[^a-zA-Z0-9]/g, "");

    req.files.fileUploaded.forEach((file) => {
      uploadController.moveMessageUpload(messageId, file, fileName).catch((reason => {
        if (reason) {
          console.log(reason);
        }
      }));
      file = {
        fileId: uuidv4(),
        fileTypeId: '',
        fieldName: file.fieldname.replace(/fileUploaded/i, fileName),
        originalName: file.originalname,
        encoding: file.encoding,
        mimeType: file.mimetype,
        blobName: file.blobName.replace(/fileUploaded/i, fileName),
        container: file.container,
        blob: file.blob.replace(/fileUploaded/i, fileName),
        blobType: file.blobType,
        size: file.size,
        etag: file.etag,
        url: file.url.replace(/fileUploaded/i, messageId + "/" + fileName),
      };

      if (sessData.messageFiles[req.body.mid]){
        sessData.messageFiles[req.body.mid].push(file);
      }else{
        sessData.messageFiles[req.body.mid] = [file];
      }

    });
    return res.status(200).end();
  } catch (e) {
    console.log(e);
    return res.status(500).end();
  }

};

exports.getMessageFiles = async function (req, res) {
  try {
    let files = [];
    let sessData = req.session;

    let message = await MessageModel.findById(req.params.messageId);

    if (message && message.files && message.files.length > 0) {
      files = message.files;
    }

    if (sessData && sessData.messageFiles && sessData.messageFiles[req.params.messageId]){
      let sessFiles = sessData.messageFiles[req.params.messageId] ? sessData.messageFiles[req.params.messageId] : [];
      files = [...files, ...sessFiles]
    }

    return res.status(200).json({
      status: 200,
      files: files
    })
  } catch (e) {
    console.log(e);
    return res.status(500).json({
      status: 500,
      error: "Internal Server Error"
    })
  }

};


exports.deleteMessageFile = async function (req, res) {
  try {
    let message = await MessageModel.findById(req.params.messageId);
    let deleted = false;
    if (message && message.files && message.files.length > 0) {

      let fileIndex = message.files.findIndex((f) => f.fileId === req.body.fileId);
      if (fileIndex > -1) {
        message.files.splice(fileIndex, 1);

        message.markModified('files');
        deleted = true;
        await message.save();
      }
    }

    if(!deleted){
      let sessData = req.session;

      if (sessData && sessData.messageFiles && sessData.messageFiles[req.params.messageId]){
        let fileIndex = sessData.messageFiles[req.params.messageId].findIndex((f) => f.fileId === req.body.fileId);
        if (fileIndex > -1) {
          sessData.messageFiles[req.params.messageId].splice(fileIndex, 1);
          deleted = true
        }

      }
    }

    return res.status(200).json({
      status: 200,
      isDeleted: deleted
    })
  } catch (e) {
    console.log(e);
    return res.status(500).json({
      status: 500,
      error: "Internal Server Error"
    })
  }

};


exports.generateRequestFileByFRMessage = async function (message) {
  try {
    let hoursPeriod = 60 * 60 * 1000;
    let currentDate = new Date();

    let mcc = await MasterClientCodeModel.findOne({code: message.mcc});

    if (!mcc){
      return {
        status: 404,
        message: "Master Client not found "
      }
    }

    let content = "Dear Client, \n\n" +
      "For company "+ message.companyName + " we have noticed there are some files missing. \n" +
      "Please upload those files.\n\n" +
      "if you have any questions please contact your representative, \n\n " +
      "Kind regards";

    let emailSubject =  message.companyName + ": \n\n" +
      "Upon conducting a thorough review of the company's record, we've identified a few missing items to complete our records for the captioned company.\n" +
      "We ask that you please access the portal and upload these items to satisfy the requirement.";

    let newMessage = new MessageModel({
      _id: ObjectId(),
      subject :message.subject,
      emailSubject:emailSubject,
      content: content,
      sendToAll: false,
      masterClientCodes: mcc.code,
      scheduleMessage: false,
      scheduledAt: new Date(Math.ceil(currentDate.getTime() / hoursPeriod) * hoursPeriod),
      status: 'SCHEDULED',
    });

    await newMessage.save();

    return {
      status: 200,
      message: "Announcement created successfully",
      mid: newMessage._id
    }
  }catch (e) {
    return {
      status: 500,
      message: "Error generating message"
    }
  }
};


/**
 * Create Message object in db for a new annoucement of MCC.
 * @function createAnnouncementByMCC
 * @param {string}  mcc Master client code to send new announcement.
 * @param {string}  subject Subject of the announcement in client portal.
 * @param {string}  emailSubject Subject of the announcement email.
 * @param {string}  content Body message of the announcement.
 * @param {array}  urls Url-Name object array to attach to the announcement.
 * @param {array}  files attachments object array to attach to the announcement.
 * @return MessageModel object with the new announcement data.
 */
exports.createAnnouncementByMCC = async function(mcc, subject, emailSubject, content, urls = [], files) {
  try {
    // create announcement
    const hoursPeriod = 60 * 60 * 1000;
    const currentDate = new Date();

    let newMessage = new MessageModel({
      _id: ObjectId(),
      subject: subject,
      emailSubject: emailSubject ? emailSubject : subject,
      content: content,
      sendToAll: false,
      masterClientCodes: mcc,
      scheduleMessage: false,
      scheduledAt: new Date(Math.ceil(currentDate.getTime() / hoursPeriod) * hoursPeriod),
      status: 'SCHEDULED',
      urls,
      files: files || []
    });

    await newMessage.save();
    return newMessage;
  } catch (e) {
    console.log(e);
    return null;
  }

}