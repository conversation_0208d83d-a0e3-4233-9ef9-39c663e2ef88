const sqlDb = require('../../models-sql');
const mem_ShareholdersModel = sqlDb.mem_Shareholders;
const mem_ShareholdersHistoryModel = sqlDb.mem_ShareholdersHistory;
const mem_MemberProfilesModel = sqlDb.mem_MemberProfiles;
const mem_EntitiesModel = sqlDb.mem_Entities;
const mem_MemberProfilesHistoryModel = sqlDb.mem_MemberProfilesHistory;
const mem_EntitiesStockHistory = sqlDb.mem_EntitiesStockHistory;
const mem_EntitiesMutualFundHistory = sqlDb.mem_EntitiesMutualFundHistory;
const MailController = require('../../controllers/mailController');
const MailFormatter = require('../../controllers/mailFormatController');
const moment = require('moment');
const {
  DIRMEMBER_STATUS,
  TYPE_OF_MEMBER,
} = require('../../utils/directorAndMemberConstants');
const { getCompanyData, getListFieldsToValidate } = require('../../utils/companyInformationUtils');
const { Op } = require('sequelize');

exports.getMemberEntries = async function (req, res, next) {
  try {
    if (process.env.HIDE_MEMBERS === 'true') {
      const err = new Error('Members are not available');
      err.status = 404;
      return next(err);
    }

    const company = await getCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

    if (company?.error) {
      const err = new Error(company.error);
      err.status = company.status;
      return next(err);
    }

    let membersWithMissingValues = [];
    let members = await mem_ShareholdersModel.findAll({
      where: {
        EntityLegacyID: company.code,
      },
      include: [{
        model: mem_MemberProfilesModel,
        as: 'shareholderProfile',
        required: false
      }, {
        model: mem_MemberProfilesModel,
        as: 'nominator',
        required: false
      }],
      raw: false
    });

    const listOfRequiredFieldsCorporate = getListFieldsToValidate(TYPE_OF_MEMBER, "Corporate");
    const listOfRequiredFieldsIndividual = getListFieldsToValidate(TYPE_OF_MEMBER, "Individual");

    let entityName = company.name;
    const individualMembers = [];
    const corporateMembers = [];
    const jointMembers = {};
    const shareholderCodes = [];

    if (members && members.length > 0) {
      entityName = members[0].EntityName;

      // Filter out members with populated MemberDateEnd (Cessation Date)
      members = members.filter(member => !member.MemberDateEnd);

      const memberCodes = [...new Set(members.map((e) => e.UniqueRelationID))];

      const historyLogs = await mem_ShareholdersHistoryModel.findAll({
        where: {
          EntityLegacyID: company.code,
          UniqueRelationID: memberCodes,
        },
        raw: true
      });

      // Get all profile history records for members
      const shareholderHistoryIds = historyLogs
        .map(h => h.Id);

      const profilesHistory = shareholderHistoryIds.length > 0 ?
        await mem_MemberProfilesHistoryModel.findAll({
          where: {
            ShareholderHistoryId: shareholderHistoryIds
          },
          raw: true
        }) : [];

      // Normalize empty values (treat empty string, null, undefined as equivalent)
      const normalizeValue = (val) => (val === '' || val === null || val === undefined) ? null : val;

      members.forEach((m) => {
        if (shareholderCodes.includes(m.SHUniqueNr) && m.MemberTypeCode !== "JSH" && m.MemberTypeCode !== "JST") {
          // Skip duplicate entries, not required for shareholder list unless joint or has member
          if (m.MemberTypeCode === "NOM") {
            // If shareholder is a nominee, check for missing nominator information
            const nominator = m.nominator;
            if (!nominator) {
              return;
            }

            // Add fields that are not in the profile
            nominator.MemberName = m.MemberName;
            nominator.MemberDateStart = m.MemberDateStart;

            const nomEntryLogs = historyLogs.filter((r) => r.UniqueRelationID === m.UniqueRelationID && r.RelationType === m.RelationType);
            const lastNomLogCreated = nomEntryLogs[0];
            if (lastNomLogCreated && lastNomLogCreated.Status === DIRMEMBER_STATUS.PENDING) {
              return;
            }

            const existingEntry = membersWithMissingValues.find(entry => entry.id === m.SHUniqueNr);
            if (existingEntry?.missingValues?.includes("Nominator Details") ?? false) {
              // No need to check again nominator
              return;
            }

            const requiredFieldsNominator = getListFieldsToValidate(TYPE_OF_MEMBER, m.MemberFileTypeCode === "I" ? "IndividualNominator" : "CorporateNominator");

            const missingNominatorValues = requiredFieldsNominator.filter((item) => {
              const fieldValue = nominator[item.field];
              return !fieldValue; // Checks for null, undefined, empty string
            });

            if (!missingNominatorValues.length) {
              return;
            }

            if (existingEntry) {
              existingEntry.missingValues += ', Nominator Details';
            } else {
              const isCorporate = ["C", "O", "P", "T", "N", "0"].includes(m.SHFileTypeCode);
              (isCorporate ? corporateMembers : individualMembers).forEach(entry => {
                if (entry.SHUniqueNr === m.SHUniqueNr) {
                  entry.hasMissingValues = true;
                  entry.showInfoQuestion = false;
                  entry.canConfirm = false;
                }
              });
              membersWithMissingValues.push({
                id: m.SHUniqueNr,
                name: m.SHName,
                missingValues: "Nominator Details"
              });
            }
          }
          return;
        }

        const member = { ...m.dataValues };
        member.canUpdate = true;
        member.canConfirm = true;
        member.showHistory = false;
        member.showInfoQuestion = true;

        const entryLogs = historyLogs.filter((r) => r.UniqueRelationID === member.UniqueRelationID && r.RelationType === member.RelationType);

        if (entryLogs.length > 0) {
          entryLogs.sort((a, b) => b.Id - a.Id);
          const lastLogCreated = entryLogs[0];

          if (lastLogCreated.Status !== DIRMEMBER_STATUS.RECEIVED && lastLogCreated.Status !== DIRMEMBER_STATUS.INITIAL && lastLogCreated.Status !== DIRMEMBER_STATUS.REFRESHED) {
            member.showInfoQuestion = false;
            member.canConfirm = false;
          } else if (lastLogCreated.Status == DIRMEMBER_STATUS.RECEIVED || lastLogCreated.Status == DIRMEMBER_STATUS.REFRESHED) {
            const confirmedOrUpdateRequestLogs = entryLogs.filter((r) => r.Status == DIRMEMBER_STATUS.CONFIRMED || r.Status == DIRMEMBER_STATUS.PENDING || r.Status == DIRMEMBER_STATUS.INITIAL);
            if (confirmedOrUpdateRequestLogs && confirmedOrUpdateRequestLogs.length > 0) {
              confirmedOrUpdateRequestLogs.sort((a, b) => b.Id - a.Id);
              const lastConfirmedOrUpdateRequestLog = confirmedOrUpdateRequestLogs[0];
              member.oldData = lastConfirmedOrUpdateRequestLog;
            }
          }

          member.showHistory = lastLogCreated.Status === DIRMEMBER_STATUS.PENDING;
          member.canUpdate = lastLogCreated.Status !== DIRMEMBER_STATUS.PENDING;

          if (lastLogCreated.Status === DIRMEMBER_STATUS.PENDING && lastLogCreated.UpdateRequestDate === null) {
            const userRequestUpdateLogs = entryLogs.find(r => r.UpdateRequestDate !== null);

            member.lastChange = {
              changeType: userRequestUpdateLogs ? userRequestUpdateLogs.TypeOfUpdateRequest : "",
              changeReason: userRequestUpdateLogs ? userRequestUpdateLogs.UpdateRequestComments : "",
              status: lastLogCreated.Status
            }
          } else {
            member.lastChange = {
              changeType: lastLogCreated.TypeOfUpdateRequest,
              changeReason: lastLogCreated.UpdateRequestComments,
              status: lastLogCreated.Status
            }
          }
        }

        // Check if member has confirmed status in history records
        member.isConfirmed = entryLogs.some(record => record.Status === DIRMEMBER_STATUS.CONFIRMED);

        // Check if record was updated from last history record for confirmedAndUpdated logic
        if (member.isConfirmed && entryLogs.length > 0) {
          // Find the latest record to compare against current data
          if (entryLogs.length > 0) {
            entryLogs.sort((a, b) => b.Id - a.Id);
            const latestRecord = entryLogs[0];

            // Fields from mem_Shareholders to compare (only fields displayed in templates)
            const memberFieldsToCompare = [
              'SHName'
            ];

            let isUpdated = memberFieldsToCompare.some(field => {
              const currentValue = normalizeValue(member[field]);
              const historyValue = normalizeValue(latestRecord[field]);


              return currentValue !== historyValue;
            });

            const lastMemberProfile = profilesHistory.find(
              profile => profile.ShareholderHistoryId === latestRecord.Id && profile.MFUniqueNr === member.SHUniqueNr
            );

            // Also check member profile fields if they exist (only fields displayed in templates)
            if (!isUpdated && member.shareholderProfile) {
              const isCorporate = ["C", "O", "P", "T", "N", "0"].includes(member.SHFileTypeCode);
              const profileFieldsToCompare = isCorporate ?
                ['MFIncropNr', 'MFROAddress', 'MFIncorpDate', 'MFIncorpCountry'] : // Corporate fields
                ['MFFormerName', 'MFRAAddress', 'MFDateOfBirth', 'MFBirthCountry', 'MFNationality']; // Individual fields

              isUpdated = profileFieldsToCompare.some(field => {
                // Handle date fields specially
                if (['MFIncorpDate', 'MFDateOfBirth'].includes(field)) {
                  const currentValue = member.shareholderProfile[field];
                  const historyValue = lastMemberProfile[field];

                  // Both empty/null/undefined - no change
                  if (!currentValue && !historyValue) return false;
                  // One empty, one has value - change detected
                  if (!currentValue || !historyValue) return true;

                  // Format dates as YYYY-MM-DD for comparison
                  const currentDate = new Date(currentValue).toISOString().split('T')[0];
                  const historyDate = new Date(historyValue).toISOString().split('T')[0];
                  return currentDate !== historyDate;
                }

                const currentValue = normalizeValue(member.shareholderProfile[field]);
                const historyValue = normalizeValue(lastMemberProfile[field]);

                return currentValue !== historyValue;
              });

            }

            // Also check nominator fields if they exist (collect all nominators for this member)
            if (!isUpdated && member.MemberTypeCode === "NOM") {
              // Get all current nominators for this member from all rows
              const currentNominators = members.filter(m =>
                m.SHUniqueNr === member.SHUniqueNr &&
                m.MemberTypeCode === "NOM" &&
                m.nominator
              ).map(m => ({
                nominator: m.nominator,
                MemberName: m.MemberName,
                MemberDateStart: m.MemberDateStart,
                MemberCode: m.MemberCode
              }));

              const nominatorCodes = [...new Set(currentNominators.map(n => n.MemberCode))];

              if (currentNominators.length > 0) {
                // Get the nominator profile history records linked to this shareholder
                const nominatorProfiles = profilesHistory.filter(
                  profile => profile.ShareholderHistoryId === latestRecord.Id && nominatorCodes.includes(profile.MFCode)
                );
                const nominatorHistoryCodes = [...new Set(nominatorProfiles.map(p => p.MFCode))];

                // Check if the number of nominators has changed
                if (nominatorCodes.length !== nominatorHistoryCodes.length) {
                  isUpdated = true;
                } else {
                  // Determine nominator type based on MemberFileTypeCode
                  const isNominatorCorporate = member.MemberFileTypeCode !== "I";
                  const nominatorFieldsToCompare = isNominatorCorporate ?
                    ['MFIncropNr', 'MFROAddress', 'MFIncorpDate', 'MFIncorpCountry'] : // Corporate nominator fields
                    ['MFFormerName', 'MFRAAddress', 'MFDateOfBirth', 'MFBirthCountry', 'MFNationality']; // Individual nominator fields

                  // Also include MemberName and MemberDateStart from the member record
                  const memberNominatorFields = ['MemberName', 'MemberDateStart'];

                  // Check if any current nominator doesn't match any nominator
                  isUpdated = currentNominators.some(current => {
                    // Try to find a matching nominator by MemberCode
                    const matchingProfile = nominatorProfiles.find(profile => {
                      return profile.MFCode === current.MemberCode;
                    });

                    if (!matchingProfile) {
                      return true; // New nominator
                    }

                    // Get the latest record for the mem_Shareholders record for this nominator
                    const latestNominatorRecords = historyLogs.filter(h =>
                      h.MemberCode === current.memberCode
                    );
                    latestNominatorRecords.sort((a, b) => b.Id - a.Id);
                    const latestNominatorRecord = latestNominatorRecords[0];
                    if (!latestNominatorRecord) {
                      return false;
                    }

                    // Check if member fields match with the shareholder record
                    const memberFieldsChanged = memberNominatorFields.some(field => {
                      if (['MemberDateStart'].includes(field)) {
                        if (!current[field] || !latestNominatorRecord[field]) return current[field] !== latestNominatorRecord[field];

                        const currentDate = new Date(current[field]).toISOString().split('T')[0];
                        const confirmedDate = new Date(latestNominatorRecord[field]).toISOString().split('T')[0];
                        return currentDate !== confirmedDate;
                      }
                      return current[field] !== latestNominatorRecord[field];
                    });

                    // Check if nominator profile fields match
                    const profileFieldsChanged = nominatorFieldsToCompare.some(field => {
                      if (['MFIncorpDate', 'MFDateOfBirth'].includes(field)) {
                        if (!current.nominator[field] || !matchingProfile[field]) return current.nominator[field] !== matchingProfile[field];

                        const currentDate = new Date(current.nominator[field]).toISOString().split('T')[0];
                        const confirmedDate = new Date(matchingProfile[field]).toISOString().split('T')[0];
                        return currentDate !== confirmedDate;
                      }
                      return current.nominator[field] !== matchingProfile[field];
                    });

                    return memberFieldsChanged || profileFieldsChanged;
                  });
                }
              }

            }

            // Also check share details fields if they exist (displayed in templates)
            if (!isUpdated) {
              const shareFieldsToCompare = ['ShareClassName', 'SHCertNr', 'NrOfShares', 'SHVotingRights', 'ShareIssueDate'];

              isUpdated = shareFieldsToCompare.some(field => {
                // Handle date fields specially
                if (['ShareIssueDate'].includes(field)) {
                  const currentValue = member[field];
                  const historyValue = latestRecord[field];

                  // Both empty/null/undefined - no change
                  if (!currentValue && !historyValue) return false;
                  // One empty, one has value - change detected
                  if (!currentValue || !historyValue) return true;

                  // Format dates as YYYY-MM-DD for comparison
                  const currentDate = new Date(currentValue).toISOString().split('T')[0];
                  const historyDate = new Date(historyValue).toISOString().split('T')[0];
                  return currentDate !== historyDate;
                }

                const currentValue = normalizeValue(member[field]);
                const historyValue = normalizeValue(latestRecord[field]);

                return currentValue !== historyValue;
              });

            }

            member.confirmedAndUpdated = isUpdated;
            if (member.confirmedAndUpdated) {
              member.showHistory = false;
            }
          }
        }

        // Validate member required fields
        if (member.canUpdate) {
          // Determine member type for validation
          let requiredFields = listOfRequiredFieldsIndividual;
          if (["C", "O", "P", "T", "N", "0"].includes(member.SHFileTypeCode)) {
            requiredFields = listOfRequiredFieldsCorporate;
          }

          // Simply check for missing values
          const missingValues = requiredFields?.filter((item) => {
            const fieldValue = member[item.field];
            const profileFieldValue = member[`shareholderProfile.${item.field}`] || member.shareholderProfile?.[item.field];
            return !fieldValue && !profileFieldValue; // Checks for null, undefined, empty string
          }).map((item) => item.name)

          if (member.MemberTypeCode === "NOM") {
            // If shareholder is a nominee, check for missing nominator information
            const nominator = member.nominator;
            nominator.MemberName = member.MemberName;
            nominator.MemberDateStart = member.MemberDateStart;
            if (nominator) {
              const requiredFieldsNominator = getListFieldsToValidate(TYPE_OF_MEMBER, member.MemberFileTypeCode === "I" ? "IndividualNominator" : "CorporateNominator");
              const missingNominatorValues = requiredFieldsNominator.filter((item) => {
                const fieldValue = nominator[item.field];
                return !fieldValue; // Checks for null, undefined, empty string
              });

              if (missingNominatorValues.length) {
                missingValues.push("Nominator Details");
              }
            }
          }

          if (missingValues.length > 0) {
            member.hasMissingValues = true;
            member.showInfoQuestion = false;
            member.canConfirm = false;
            membersWithMissingValues.push({
              id: member.SHUniqueNr,
              name: member.SHName,
              missingValues: missingValues.join(', ')
            });
          }
        }

        if (member.MemberTypeCode === "JSH" || member.MemberTypeCode === "JST") {
          if (member.CombinedSHCode) {
            if (!jointMembers[member.CombinedSHCode]) {
              jointMembers[member.CombinedSHCode] = [];
            }
            jointMembers[member.CombinedSHCode].push(member);
          } else {
            console.log("Member missing CombinedSHCode");
          }
        } else {
          if (member.SHFileTypeCode === "I") {
            individualMembers.push(member);
          } else if (["C", "O", "P", "T", "N", "0"].includes(member.SHFileTypeCode)) {
            corporateMembers.push(member);
          } else {
            console.log("Unknown member type: ", member.SHFileTypeCode);
          }
          shareholderCodes.push(member.SHUniqueNr);
        }
      });
    }

    const entity = await mem_EntitiesModel?.findOne({
      where: {
        EntityLegacyID: company.code,
      },
      raw: true
    });

    const stockInformationHistory = await mem_EntitiesStockHistory.findAll({
      where: {
        EntityLegacyID: company.code,
      },
      raw: true
    });

    const mutualFundInformationHistory = await mem_EntitiesMutualFundHistory.findAll({
      where: {
        EntityLegacyID: company.code,
      },
      raw: true
    });

    // Get the latest history record for stock and mutual fund information
    const latestStockInformationHistory = stockInformationHistory?.sort((a, b) => b.Id - a.Id)[0];
    const latestMutualFundInformationHistory = mutualFundInformationHistory?.sort((a, b) => b.Id - a.Id)[0];

    let stockInformation = null;
    let mutualFundInformation = null;
    let missingStockFields = [];
    let missingFundFields = [];

    // Validate Stock information
    if (entity?.STXName || entity?.STXTicker || entity?.STXJurisdiction || entity?.STXRegulator) {
      stockInformation = {
        STXName: entity.STXName,
        STXTicker: entity.STXTicker,
        STXJurisdiction: entity.STXJurisdiction,
        STXRegulator: entity.STXRegulator,
        LastUpdateDate: entity.LastUpdateDate,
        STXListingDate: entity.STXListingDate,
        EntityStatus: entity.EntityStatus,
        showHistory: latestStockInformationHistory?.Status === DIRMEMBER_STATUS.PENDING,
        lastChange: {
          status: latestStockInformationHistory?.Status,
          changeType: latestStockInformationHistory?.TypeOfUpdateRequest,
          changeReason: latestStockInformationHistory?.UpdateRequestComments,
        },
        isConfirmed: stockInformationHistory?.some(record => record.Status === DIRMEMBER_STATUS.CONFIRMED)
      };

      // Check if record was updated from last history
      if (latestStockInformationHistory) {
        const fields = ['STXName', 'STXTicker', 'STXJurisdiction', 'STXRegulator', 'STXListingDate'];
        const isUpdated = fields.some(field => {
          if (field === 'STXListingDate') {
            if (!entity[field] || !latestStockInformationHistory?.[field]) return false;

            // Format dates as YYYY-MM-DD for comparison
            const currentDate = new Date(entity[field]).toISOString().split('T')[0];
            const historyDate = new Date(latestStockInformationHistory?.[field]).toISOString().split('T')[0];
            return currentDate !== historyDate;
          }

          return entity[field] !== latestStockInformationHistory?.[field];
        });
        stockInformation.confirmedAndUpdated = isUpdated && stockInformation.isConfirmed;
        if (stockInformation.confirmedAndUpdated) {
          stockInformation.showHistory = false;
        }
      }

      // Get required fields for Stock entities
      const stockRequiredFields = getListFieldsToValidate(TYPE_OF_MEMBER, "Stock");

      // Check for missing values
      const missingStock = stockRequiredFields.filter((item) => {
        return !stockInformation[item.field]; // Checks for null, undefined, empty string
      });

      const missingStockNames = missingStock.map((item) => item.name);
      missingStockFields = missingStock.map((item) => item.field);

      if (missingStockNames.length > 0) {
        membersWithMissingValues.push({
          id: company.code,
          name: "Stock Exchange Information",
          missingValues: missingStockNames.join(', ')
        });

        // Add missing values flag to stock information object
        stockInformation.hasMissingValues = true;
      }
    }

    // Validate Mutual Fund information
    if (entity?.BusRegNr || entity?.BusRegType || entity?.BusRegStartDate) {
      mutualFundInformation = {
        Id: company.code,
        BusRegNr: entity.BusRegNr,
        BusRegType: entity.BusRegType,
        BusRegStartDate: entity.BusRegStartDate,
        LastUpdateDate: entity.LastUpdateDate,
        EntityStatus: entity.EntityStatus,
        showHistory: latestMutualFundInformationHistory?.Status === DIRMEMBER_STATUS.PENDING,
        lastChange: {
          status: latestMutualFundInformationHistory?.Status,
          changeType: latestMutualFundInformationHistory?.TypeOfUpdateRequest,
          changeReason: latestMutualFundInformationHistory?.UpdateRequestComments,
        },
        isConfirmed: mutualFundInformationHistory?.some(record => record.Status === DIRMEMBER_STATUS.CONFIRMED)
      };

      // Check if record was updated from last history
      if (latestMutualFundInformationHistory) {
        const fields = ['BusRegNr', 'BusRegType', 'BusRegStartDate'];
        const isUpdated = fields.some(field => {
          if (field === 'BusRegStartDate') {
            if (!entity[field] || !latestMutualFundInformationHistory?.[field]) return false;

            // Format dates as YYYY-MM-DD for comparison
            const currentDate = new Date(entity[field]).toISOString().split('T')[0];
            const historyDate = new Date(latestMutualFundInformationHistory?.[field]).toISOString().split('T')[0];
            return currentDate !== historyDate;
          }

          return entity[field] !== latestMutualFundInformationHistory?.[field];
        });
        mutualFundInformation.confirmedAndUpdated = isUpdated && mutualFundInformation.isConfirmed;
        if (mutualFundInformation.confirmedAndUpdated) {
          mutualFundInformation.showHistory = false;
        }
      }


      // Get required fields for Mutual Fund entities
      const fundRequiredFields = getListFieldsToValidate(TYPE_OF_MEMBER, "MutualFund");

      // Check for missing values
      const missingFund = fundRequiredFields.filter((item) => {
        return !mutualFundInformation[item.field]; // Checks for null, undefined, empty string
      });

      const missingFundValues = missingFund.map((item) => item.name);
      missingFundFields = missingFund.map((item) => item.field);

      if (missingFundValues.length > 0) {
        membersWithMissingValues.push({
          id: company.code,
          name: "Mutual Fund Information",
          missingValues: missingFundValues.join(', ')
        });

        // Add missing values flag to mutual fund information object
        mutualFundInformation.hasMissingValues = true;
      }
    }

    const jointMemberList = [];
    for (const combinedSHCode in jointMembers) {
      const membersToJoin = jointMembers[combinedSHCode];
      const jointMember = {
        SHName: [...new Set(membersToJoin.map(m => m.SHName))].join(' & '),
        CombinedSHCode: combinedSHCode,
        SHDateStart: membersToJoin[0].SHDateStart,
        ShareIssueDate: membersToJoin[0].ShareIssueDate,
        canUpdate: membersToJoin[0].canUpdate,
        canConfirm: membersToJoin[0].canConfirm,
        showHistory: membersToJoin[0].showHistory,
        confirmedAndUpdated: membersToJoin.some(m => m.confirmedAndUpdated),
        hasMissingValues: membersToJoin.some(m => m.hasMissingValues),
        lastChange: membersToJoin[0].lastChange
      };
      jointMemberList.push(jointMember);
    }

    return res.render('director-and-members/member-forms', {
      masterClientCode: req.params.masterclientcode,
      company: company,
      entityName,
      type: "members",
      user: req.user,
      messages: req.session.messages,
      individualMembers,
      jointMembers: jointMemberList,
      corporateMembers,
      membersWithMissingValues,
      stockInformation,
      mutualFundInformation,
      missingStockFields,
      missingFundFields,
      hasInformation: stockInformation ||
        mutualFundInformation ||
        individualMembers.length > 0 ||
        corporateMembers.length > 0 ||
        jointMemberList.length > 0,
      hasShareholders: members && members.length > 0,
    });
  } catch (e) {
    console.error("Error getting member entries: ", e);
    const err = new Error('Internal server error');
    err.status = 500;
    return next(err);
  }
};

exports.getMemberDetails = async function (req, res, next) {
  try {
    const company = await getCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

    if (company?.error) {
      const err = new Error(company.error);
      err.status = company.status;
      return next(err);
    }
    // Get the members by ID from mem_Shareholders table with associated profile
    let members = await mem_ShareholdersModel.findAll({
      where: {
        EntityLegacyID: company.code,
        SHUniqueNr: req.params.id,
        MemberTypeCode: { [Op.notIn]: ['JSH', 'JST'] }, // ensure non-joint records only
      },
      include: [{
        model: mem_MemberProfilesModel,
        as: 'shareholderProfile',
        required: false
      }, {
        model: mem_ShareholdersHistoryModel,
        as: 'historyRecords',
        required: false
      }],
      raw: false
    });

    const isNominee = members.some(m => m.MemberTypeCode === 'NOM');

    const memberCodes = [...new Set(members.map((e) => e.MemberCode))];
    const nominators = await mem_MemberProfilesModel.findAll({
      where: {
        MFCode: memberCodes,
      },
      raw: true
    });

    const listOfRequiredFieldsCorporateNominator = getListFieldsToValidate(TYPE_OF_MEMBER, "CorporateNominator");
    const listOfRequiredFieldsIndividualNominator = getListFieldsToValidate(TYPE_OF_MEMBER, "IndividualNominator");
    const listOfRequiredFieldsCorporate = getListFieldsToValidate(TYPE_OF_MEMBER, "Corporate");
    const listOfRequiredFieldsIndividual = getListFieldsToValidate(TYPE_OF_MEMBER, "Individual");

    // Match member attributes to the correct profile
    const individualNominators = [];
    const corporateNominators = [];
    const missingValuesNominators = [];
    nominators.forEach((n) => {
      const member = members.find((m) => m.MemberCode === n.MFCode);
      const details = {
        MemberName: member.MemberName,
        MemberDateStart: member.MemberDateStart,
        ...n
      };
      if (n.MFCode === member.MemberCode) {
        if (member.MemberFileTypeCode === "I") {
          // Validate required fields for individual nominators
          const missingValuesNominator = listOfRequiredFieldsIndividualNominator.filter((item) => {
            const fieldValue = details[item.field];
            return !fieldValue; // Checks for null, undefined, empty string
          }).map((item) => item.field);
          missingValuesNominators.push(...missingValuesNominator);

          individualNominators.push(details);
        } else {
          // Validate required fields for corporate nominators
          const missingValuesNominator = listOfRequiredFieldsCorporateNominator.filter((item) => {
            const fieldValue = details[item.field];
            return !fieldValue; // Checks for null, undefined, empty string
          }).map((item) => item.field);
          missingValuesNominators.push(...missingValuesNominator);

          corporateNominators.push(details);
        }
      }
    });

    let shareDetails = [];
    members.forEach((m) => {
      if (m.SHCertNr && m.NrOfShares && !shareDetails.find(d => d.SHCertNr === m.SHCertNr && d.ShareClassName === m.ShareClassName)) {
        shareDetails.push({
          SHCertNr: m.SHCertNr,
          NrOfShares: m.NrOfShares,
          ShareClassName: m.ShareClassName,
          SHVotingRights: m.SHVotingRights,
          ShareIssueDate: m.ShareIssueDate,
          SHDateStart: m.SHDateStart
        });
      }
    })

    // Sort shares by issue date (oldest to newest)
    shareDetails.sort((a, b) => new Date(a.ShareIssueDate) - new Date(b.ShareIssueDate));

    // All records should share the same history so just use the first one
    const entryLogs = members[0].historyRecords;

    const member = members[0];

    let lastLogCreated;
    if (entryLogs.length > 0) {
      entryLogs.sort((a, b) => b.Id - a.Id);
      lastLogCreated = entryLogs[0];

      if (lastLogCreated.Status !== DIRMEMBER_STATUS.RECEIVED &&
        lastLogCreated.Status !== DIRMEMBER_STATUS.INITIAL &&
        lastLogCreated.Status !== DIRMEMBER_STATUS.REFRESHED
      ) {
        member.canConfirm = false;
      }

      member.showHistory = lastLogCreated.Status === DIRMEMBER_STATUS.PENDING;
      member.canUpdate = lastLogCreated.Status !== DIRMEMBER_STATUS.PENDING;

      if (lastLogCreated.Status === DIRMEMBER_STATUS.PENDING && lastLogCreated.UpdateRequestDate === null) {
        const userRequestUpdateLogs = entryLogs.find(r => r.UpdateRequestDate !== null);

        member.lastChange = {
          changeType: userRequestUpdateLogs ? userRequestUpdateLogs.TypeOfUpdateRequest : "",
          changeReason: userRequestUpdateLogs ? userRequestUpdateLogs.UpdateRequestComments : "",
          status: lastLogCreated.Status
        };
      } else {
        member.lastChange = {
          changeType: lastLogCreated.TypeOfUpdateRequest,
          changeReason: lastLogCreated.UpdateRequestComments,
          status: lastLogCreated.Status
        };
      }
    }

    if (!member) {
      const err = new Error('Member not found');
      err.status = 404;
      return next(err);
    }

    // Determine member type for validation
    let memberType;
    if (member.MemberTypeCode === "JSH" || member.MemberTypeCode === "JST") {
      throw new Error('Joint members are not supported in this view');
    } else if (member.SHFileTypeCode === "I") {
      memberType = "Individual";
    } else if (["C", "O", "P", "T", "N", "0"].includes(member.SHFileTypeCode)) {
      memberType = "Corporate";
    }

    // Get required fields based on member type
    const listOfRequiredFields = memberType === "Individual" ? listOfRequiredFieldsIndividual : listOfRequiredFieldsCorporate;

    // Simply check for missing values without extra mapping
    const missingValues = listOfRequiredFields?.filter((item) => {
      const fieldValue = member[item.field];
      const profileFieldValue = member.shareholderProfile?.[item.field];
      return !fieldValue && !profileFieldValue; // Checks for null, undefined, empty string
    }).map((item) => item.field);

    // Check if member has confirmed status in history records
    let isConfirmed = false;
    if (member.historyRecords && member.historyRecords.length > 0) {
      isConfirmed = member.historyRecords.some(record => record.Status === DIRMEMBER_STATUS.CONFIRMED);
    }

    return res.render('director-and-members/member-details-forms', {
      masterClientCode: req.params.masterclientcode,
      company: company,
      user: req.user,
      messages: req.session.messages,
      memberDetails: member,
      isIndividual: member.SHFileTypeCode === "I",
      isCorporate: ["C", "O", "P", "T", "N", "0"].includes(member.SHFileTypeCode),
      nominators,
      hasNominators: nominators.length > 0 && isNominee,
      isNominee,
      missingValuesNominators,
      individualNominators,
      corporateNominators,
      showShareDetails: shareDetails.length > 0,
      shareDetails,
      showConfirmButton: !isConfirmed && !missingValues.length && !missingValuesNominators.length,
      missingValues: missingValues,
      missingAnyValue: missingValues.length > 0 || missingValuesNominators.length > 0,
      isConfirmed: isConfirmed,
      hasNomineeArrangement: lastLogCreated?.hasNomineeArrangement
    });
  } catch (e) {
    console.error("Error getting member details: ", e);
    const err = new Error('Internal server error');
    err.status = 500;
    return next(err);
  }
};

exports.getJointMemberDetails = async function (req, res, next) {
  try {
    const company = await getCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

    if (company?.error) {
      const err = new Error(company.error);
      err.status = company.status;
      return next(err);
    }
    // Get the members by code and CombinedSHCode from mem_Shareholders table with associated profile
    let members = await mem_ShareholdersModel.findAll({
      where: {
        EntityLegacyID: company.code,
        CombinedSHCode: req.params.jointCode,
        MemberTypeCode: { [Op.in]: ['JSH', 'JST'] }, // ensure joint records only
      },
      include: [{
        model: mem_MemberProfilesModel,
        as: 'shareholderProfile',
        required: false
      }, {
        model: mem_ShareholdersHistoryModel,
        as: 'historyRecords',
        required: false
      }],
      raw: false
    });

    if (!members || members.length === 0) {
      const err = new Error('Members not found');
      err.status = 404;
      return next(err);
    }

    const listOfRequiredFieldsIndividual = getListFieldsToValidate(TYPE_OF_MEMBER, "Individual");
    const listOfRequiredFieldsCorporate = getListFieldsToValidate(TYPE_OF_MEMBER, "Corporate");
    const corporateMembers = [];
    const individualMembers = [];
    const missingValues = [];
    let hasNomineeArrangement = null;
    let hasNominators = false;

    const uniqueMembers = [...new Set(members.map(m => m.SHUniqueNr))];

    members.forEach((member) => {
      // Process only one member per SHUniqueNr (duplicates occur when multiple shares)
      if (!uniqueMembers.includes(member.SHUniqueNr)) {
        return;
      }
      uniqueMembers.splice(uniqueMembers.indexOf(member.SHUniqueNr), 1);

      let lastLogCreated;
      if (member.historyRecords.length > 0) {
        member.historyRecords.sort((a, b) => b.Id - a.Id);
        lastLogCreated = member.historyRecords[0];
        hasNomineeArrangement = lastLogCreated.hasNomineeArrangement;

        if (lastLogCreated.Status !== DIRMEMBER_STATUS.RECEIVED &&
          lastLogCreated.Status !== DIRMEMBER_STATUS.INITIAL &&
          lastLogCreated.Status !== DIRMEMBER_STATUS.REFRESHED
        ) {
          member.canConfirm = false;
        }

        member.showHistory = lastLogCreated.Status === DIRMEMBER_STATUS.PENDING;
        member.canUpdate = lastLogCreated.Status !== DIRMEMBER_STATUS.PENDING;

        if (lastLogCreated.Status === DIRMEMBER_STATUS.PENDING && lastLogCreated.UpdateRequestDate === null) {
          const userRequestUpdateLogs = member.historyRecords.find(r => r.UpdateRequestDate !== null);

          member.lastChange = {
            changeType: userRequestUpdateLogs ? userRequestUpdateLogs.TypeOfUpdateRequest : "",
            changeReason: userRequestUpdateLogs ? userRequestUpdateLogs.UpdateRequestComments : "",
            status: lastLogCreated.Status
          };
        } else {
          member.lastChange = {
            changeType: lastLogCreated.TypeOfUpdateRequest,
            changeReason: lastLogCreated.UpdateRequestComments,
            status: lastLogCreated.Status
          };
        }
      }
      let isCorporate = false;
      if (["C", "O", "P", "T", "N", "0"].includes(member.SHFileTypeCode)) {
        isCorporate = true;
      }

      if (member.MemberCode) {
        hasNominators = true;
      }

      // Simply check for missing values without extra mapping
      member.missingValues = (isCorporate ? listOfRequiredFieldsCorporate : listOfRequiredFieldsIndividual).filter((item) => {
        const fieldValue = member[item.field];
        const profileFieldValue = member.shareholderProfile?.[item.field];
        return !fieldValue && !profileFieldValue; // Checks for null, undefined, empty string
      }).map((item) => item.field);

      missingValues.push(...member.missingValues);

      if (member.historyRecords && member.historyRecords.length > 0) {
        member.isConfirmed = member.historyRecords.some(record => record.Status === DIRMEMBER_STATUS.CONFIRMED);
      }

      if (isCorporate) {
        corporateMembers.push(member);
      } else {
        individualMembers.push(member);
      }

    })

    // Only one share detail per cert number
    const shareDetails = members.filter((share, index, self) =>
      index === self.findIndex((t) => (
        t.SHCertNr === share.SHCertNr && t.ShareClassName === share.ShareClassName
      ))
    );

    // Sort shares by issue date (oldest to newest)
    shareDetails.sort((a, b) => new Date(a.ShareIssueDate) - new Date(b.ShareIssueDate));

    const isConfirmed = individualMembers.some(member => member.isConfirmed) || corporateMembers.some(member => member.isConfirmed);

    return res.render('director-and-members/joint-member-details-forms', {
      masterClientCode: req.params.masterclientcode,
      company: company,
      user: req.user,
      messages: req.session.messages,
      corporateMembers,
      individualMembers,
      shareDetails,
      jointCode: req.params.jointCode,
      hasCorporateMembers: corporateMembers.length > 0,
      hasIndividualMembers: individualMembers.length > 0,
      allMembers: [...corporateMembers, ...individualMembers],
      showConfirmButton: !isConfirmed && !missingValues.length,
      missingValues: missingValues,
      isConfirmed: isConfirmed,
      hasNominators: hasNominators,
      hasNomineeArrangement: hasNomineeArrangement
    });
  } catch (e) {
    console.error("Error getting member details: ", e);
    const err = new Error('Internal server error');
    err.status = 500;
    return next(err);
  }
};

exports.createMemberConfirmationLog = async function (req, res) {
  try {
    const company = await getCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

    if (company?.error) {
      return res.status(company.status).json({
        "status": company.status,
        "error": company.error
      });
    }

    let memberProfile = await mem_MemberProfilesModel.findOne({
      where: {
        MFUniqueNr: req.params.id,
      },
      raw: false
    });

    if (!memberProfile) {
      return res.status(404).json({
        "status": 404,
        "error": 'Member information not found'
      });
    }

    // Get ALL share records for this shareholder
    let allShareholderRows = await mem_ShareholdersModel.findAll({
      where: {
        EntityLegacyID: company.code,
        SHUniqueNr: req.params.id,
        MemberTypeCode: { [Op.notIn]: ['JSH', 'JST'] }, // ensure non-joint records only
      },
      raw: false
    });

    if (!allShareholderRows || allShareholderRows.length === 0) {
      return res.status(404).json({
        "status": 404,
        "error": 'No shareholder records found'
      });
    }

    // Get all nominator profiles (linked through MemberCode)
    const nominatorProfilesByMemberCode = {};
    for (const share of allShareholderRows) {
      if (share.MemberCode) {
        if (!nominatorProfilesByMemberCode[share.MemberCode]) {
          const nominatorProfile = await mem_MemberProfilesModel.findOne({
            where: {
              MFCode: share.MemberCode
            },
            raw: false
          });

          if (nominatorProfile) {
            nominatorProfilesByMemberCode[share.MemberCode] = nominatorProfile;
          }
        }
      }
    }

    for (const share of allShareholderRows) {
      // Create history record for this share
      const historyRecord = await mem_ShareholdersHistoryModel.create({
        UniqueRelationID: share.UniqueRelationID,
        ClientCode: share.ClientCode,
        ClientName: share.ClientName,
        ClientUniqueNr: share.ClientUniqueNr,
        EntityCode: share.EntityCode,
        EntityName: share.EntityName,
        EntityUniqueNr: share.EntityUniqueNr,
        EntityLegacyID: share.EntityLegacyID,
        RelationType: share.RelationType,
        SHCode: share.SHCode,
        SHName: share.SHName,
        SHUniqueNr: share.SHUniqueNr,
        SHFileTypeCode: share.SHFileTypeCode,
        SHFileType: share.SHFileType,
        MemberTypeCode: share.MemberTypeCode,
        MemberType: share.MemberType,
        MemberCode: share.MemberCode,
        MemberUniqueNr: share.MemberUniqueNr,
        MemberName: share.MemberName,
        MemberFileTypeCode: share.MemberFileTypeCode,
        MemberFileType: share.MemberFileType,
        MemberDateStart: share.MemberDateStart,
        MemberDateEnd: share.MemberDateEnd,
        ShareTypeCode: share.ShareTypeCode,
        ShareClassName: share.ShareClassName,
        SHVotingRights: share.SHVotingRights,
        ShareIssueDate: share.ShareIssueDate,
        SHCertNr: share.SHCertNr,
        SHDateStart: share.SHDateStart,
        NrOfShares: share.NrOfShares,
        SHAddress: share.SHAddress || '',
        BenOwnerCode: share.BenOwnerCode,
        BenOwner: share.BenOwner,
        BenOwnerCertNr: share.BenOwnerCertNr,
        ShareholderID: share.ShareholderID,
        ConfirmedDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
        Status: DIRMEMBER_STATUS.CONFIRMED,
        UserEmail: req.user.username,
        hasNomineeArrangement: req.body.hasNomineeArrangement
      });

      await mem_MemberProfilesHistoryModel.create({
        ShareholderHistoryId: historyRecord.Id,
        MFCode: memberProfile.MFCode,
        MFUniqueNr: memberProfile.MFUniqueNr,
        MFName: memberProfile.MFName,
        MFTypeCode: memberProfile.MFTypeCode,
        MFLegacyID: memberProfile.MFLegacyID,
        MFType: memberProfile.MFType,
        MFIncropNr: memberProfile.MFIncropNr,
        MFIncorpDate: memberProfile.MFIncorpDate,
        MFIncorpCountry: memberProfile.MFIncorpCountry,
        MFDateOfBirth: memberProfile.MFDateOfBirth,
        MFFormerName: memberProfile.MFFormerName,
        MFBirthCountry: memberProfile.MFBirthCountry,
        MFNationality: memberProfile.MFNationality,
        MFRAAddress: memberProfile.MFRAAddress,
        MFROAddress: memberProfile.MFROAddress,
        MFRSAddress: memberProfile.MFRSAddress,
        MFStatusCode: memberProfile.MFStatusCode,
        MFStatus: memberProfile.MFStatus,
        MFProductionOffice: memberProfile.MFProductionOffice
      });

      // If this share has a nominator, create history for that nominator's profile
      if (share.MemberCode && nominatorProfilesByMemberCode[share.MemberCode]) {
        const nominatorProfile = nominatorProfilesByMemberCode[share.MemberCode];

        await mem_MemberProfilesHistoryModel.create({
          ShareholderHistoryId: historyRecord.Id,
          MFCode: nominatorProfile.MFCode,
          MFUniqueNr: nominatorProfile.MFUniqueNr,
          MFName: nominatorProfile.MFName,
          MFTypeCode: nominatorProfile.MFTypeCode,
          MFLegacyID: nominatorProfile.MFLegacyID,
          MFType: nominatorProfile.MFType,
          MFIncropNr: nominatorProfile.MFIncropNr,
          MFIncorpDate: nominatorProfile.MFIncorpDate,
          MFIncorpCountry: nominatorProfile.MFIncorpCountry,
          MFDateOfBirth: nominatorProfile.MFDateOfBirth,
          MFFormerName: nominatorProfile.MFFormerName,
          MFBirthCountry: nominatorProfile.MFBirthCountry,
          MFNationality: nominatorProfile.MFNationality,
          MFRAAddress: nominatorProfile.MFRAAddress,
          MFROAddress: nominatorProfile.MFROAddress,
          MFRSAddress: nominatorProfile.MFRSAddress,
          MFStatusCode: nominatorProfile.MFStatusCode,
          MFStatus: nominatorProfile.MFStatus,
          MFProductionOffice: nominatorProfile.MFProductionOffice
        });
      }
    }



    return res.status(200).json({
      "status": 200,
      "message": "We have received your confirmation to member information and will process the data to BVI Registry in due course if we haven't submit before."
    });

  } catch (error) {
    console.error('Error in process to confirm member data: ', error);
    return res.status(500).json({
      "status": 500,
      "error": 'Failed to confirm member information'
    });
  }
}

exports.createJointMemberConfirmationLog = async function (req, res) {
  try {
    const company = await getCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

    if (company?.error) {
      return res.status(company.status).json({
        "status": company.status,
        "error": company.error
      });
    }

    // Get all member records from the database with the given UniqueRelationID
    let shareholderRows = await mem_ShareholdersModel.findAll({
      where: {
        EntityLegacyID: company.code,
        CombinedSHCode: req.params.jointCode,
        MemberTypeCode: { [Op.in]: ['JSH', 'JST'] }, // ensure joint records only
      },
      include: [{
        model: mem_MemberProfilesModel,
        as: 'shareholderProfile',
        required: false
      }],
      raw: false
    });

    if (!shareholderRows || shareholderRows.length === 0) {
      return res.status(404).json({
        "status": 404,
        "error": 'Member information not found'
      });
    }



    for (const share of shareholderRows) {
      // Create history record for this share
      const historyRecord = await mem_ShareholdersHistoryModel.create({
        UniqueRelationID: share.UniqueRelationID,
        ClientCode: share.ClientCode,
        ClientName: share.ClientName,
        ClientUniqueNr: share.ClientUniqueNr,
        EntityCode: share.EntityCode,
        EntityName: share.EntityName,
        EntityUniqueNr: share.EntityUniqueNr,
        EntityLegacyID: share.EntityLegacyID,
        RelationType: share.RelationType,
        SHCode: share.SHCode,
        SHName: share.SHName,
        SHUniqueNr: share.SHUniqueNr,
        SHFileTypeCode: share.SHFileTypeCode,
        SHFileType: share.SHFileType,
        MemberTypeCode: share.MemberTypeCode,
        MemberType: share.MemberType,
        MemberCode: share.MemberCode,
        MemberUniqueNr: share.MemberUniqueNr,
        MemberName: share.MemberName,
        MemberFileTypeCode: share.MemberFileTypeCode,
        MemberFileType: share.MemberFileType,
        MemberDateStart: share.MemberDateStart,
        MemberDateEnd: share.MemberDateEnd,
        ShareTypeCode: share.ShareTypeCode,
        ShareClassName: share.ShareClassName,
        SHVotingRights: share.SHVotingRights,
        ShareIssueDate: share.ShareIssueDate,
        SHCertNr: share.SHCertNr,
        SHDateStart: share.SHDateStart,
        NrOfShares: share.NrOfShares,
        SHAddress: share.SHAddress || '',
        BenOwnerCode: share.BenOwnerCode,
        BenOwner: share.BenOwner,
        BenOwnerCertNr: share.BenOwnerCertNr,
        ShareholderID: share.ShareholderID,
        ConfirmedDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
        Status: DIRMEMBER_STATUS.CONFIRMED,
        UserEmail: req.user.username,
        hasNomineeArrangement: req.body.hasNomineeArrangement
      });

      await mem_MemberProfilesHistoryModel.create({
        ShareholderHistoryId: historyRecord.Id,
        MFCode: share.shareholderProfile.MFCode,
        MFUniqueNr: share.shareholderProfile.MFUniqueNr,
        MFName: share.shareholderProfile.MFName,
        MFTypeCode: share.shareholderProfile.MFTypeCode,
        MFLegacyID: share.shareholderProfile.MFLegacyID,
        MFType: share.shareholderProfile.MFType,
        MFIncropNr: share.shareholderProfile.MFIncropNr,
        MFIncorpDate: share.shareholderProfile.MFIncorpDate,
        MFIncorpCountry: share.shareholderProfile.MFIncorpCountry,
        MFDateOfBirth: share.shareholderProfile.MFDateOfBirth,
        MFFormerName: share.shareholderProfile.MFFormerName,
        MFBirthCountry: share.shareholderProfile.MFBirthCountry,
        MFNationality: share.shareholderProfile.MFNationality,
        MFRAAddress: share.shareholderProfile.MFRAAddress,
        MFROAddress: share.shareholderProfile.MFROAddress,
        MFRSAddress: share.shareholderProfile.MFRSAddress,
        MFStatusCode: share.shareholderProfile.MFStatusCode,
        MFStatus: share.shareholderProfile.MFStatus,
        MFProductionOffice: share.shareholderProfile.MFProductionOffice
      });
    }

    return res.status(200).json({
      "status": 200,
      "message": "We have received your confirmation to member information and will process the data to BVI Registry in due course if we haven't submit before."
    });

  } catch (error) {
    console.error('Error in process to confirm member data: ', error);
    return res.status(500).json({
      "status": 500,
      "error": 'Failed to confirm member information'
    });
  }
}

exports.createStockOrFundConfirmationLog = async function (req, res) {
  try {
    const company = await getCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

    if (company?.error) {
      return res.status(company.status).json({
        "status": company.status,
        "error": company.error
      });
    }

    // Determine if this is for stock or fund
    const isStockConfirmation = req.path.includes('/confirm-stock');

    // Get entity data
    const entity = await mem_EntitiesModel.findOne({
      where: { EntityLegacyID: company.code },
      raw: true
    });

    if (!entity) {
      return res.status(404).json({
        "status": 404,
        "error": 'Entity information not found'
      });
    }

    try {
      if (isStockConfirmation) {
        // Create stock confirmation in the stock history table
        await mem_EntitiesStockHistory.create({
          EntityLegacyID: company.code,
          EntityCode: entity.EntityCode || '',
          EntityName: entity.EntityName || '',
          EntityUniqueNr: entity.EntityUniqueNr,
          ClientCode: entity.ClientCode || '',
          ClientName: entity.ClientName || '',
          ClientUniqueNr: entity.ClientUniqueNr,
          // Stock-specific fields
          STXCode: entity.STXCode || '',
          STXName: entity.STXName || '',
          STXTicker: entity.STXTicker || '',
          STXJurisdictionCode: entity.STXJurisdictionCode || '',
          STXJurisdiction: entity.STXJurisdiction || '',
          STXExchange: entity.STXExchange || '',
          STXRegulator: entity.STXRegulator || '',
          STXListingDate: entity.STXListingDate || null,
          IncorporationNumber: entity.IncorporationNumber || '',
          IncorporationDate: entity.IncorporationDate || null,
          JurisdictionCode: entity.JurisdictionCode || '',
          Jurisdiction: entity.Jurisdiction || '',
          EntityTypeCode: entity.EntityTypeCode || '',
          EntityType: entity.EntityType || '',
          EntityStatusCode: entity.EntityStatusCode || '',
          EntityStatus: entity.EntityStatus || '',
          EntitySubStatusCode: entity.EntitySubStatusCode || '',
          EntitySubStatus: entity.EntitySubStatus || '',
          ProductionOffice: entity.ProductionOffice || '',
          // Confirmation fields
          ConfirmedDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
          Status: DIRMEMBER_STATUS.CONFIRMED,
          UserEmail: req.user.username,
        });
      } else {
        // Create fund confirmation in the fund history table
        await mem_EntitiesMutualFundHistory.create({
          EntityLegacyID: company.code,
          EntityCode: entity.EntityCode || '',
          EntityName: entity.EntityName || '',
          EntityUniqueNr: entity.EntityUniqueNr,
          ClientCode: entity.ClientCode || '',
          ClientName: entity.ClientName || '',
          ClientUniqueNr: entity.ClientUniqueNr,
          // Mutual Fund specific fields
          IncorporationNumber: entity.IncorporationNumber || '',
          IncorporationDate: entity.IncorporationDate || null,
          JurisdictionCode: entity.JurisdictionCode || '',
          Jurisdiction: entity.Jurisdiction || '',
          EntityTypeCode: entity.EntityTypeCode || '',
          EntityType: entity.EntityType || '',
          ProductionOffice: entity.ProductionOffice || '',
          EntityStatusCode: entity.EntityStatusCode || '',
          EntityStatus: entity.EntityStatus || '',
          EntitySubStatusCode: entity.EntitySubStatusCode || '',
          EntitySubStatus: entity.EntitySubStatus || '',
          // Business registration fields specific to funds
          BusRegNr: entity.BusRegNr || '',
          BusRegTypeCode: entity.BusRegTypeCode || '',
          BusRegType: entity.BusRegType || '',
          BusRegStartDate: entity.BusRegStartDate || null,
          BusRegEndDate: entity.BusRegEndDate || null,
          // Confirmation fields
          ConfirmedDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
          Status: DIRMEMBER_STATUS.CONFIRMED,
          UserEmail: req.user.username,
        });
      }

      return res.status(200).json({
        "status": 200,
        "message": `${isStockConfirmation ? 'Stock' : 'Mutual Fund'} information confirmed successfully`
      });
    } catch (error) {
      console.error(`Error in confirming ${isStockConfirmation ? 'Stock' : 'Mutual Fund'} information: `, error);
      return res.status(500).json({
        "status": 500,
        "error": `Failed to confirm ${isStockConfirmation ? 'Stock' : 'Mutual Fund'} information`
      });
    }
  } catch (error) {
    console.error('Error in process to confirm stock/fund data: ', error);
    return res.status(500).json({
      "status": 500,
      "error": 'Failed to confirm information'
    });
  }
}

exports.requestMemberToUpdate = async function (req, res) {
  try {
    const company = await getCompanyData(req.params.code, req.params.masterclientcode, req.user.email);
    const data = req.body;

    if (company?.error) {
      return res.status(company.status).json({
        "status": company.status,
        "error": company.error
      });
    }

    if (!data.changeType) {
      return res.status(400).json({
        "status": 400,
        "error": 'Please select a valid option'
      });
    }

    let memberProfile = await mem_MemberProfilesModel.findOne({
      where: {
        MFUniqueNr: req.params.id,
      },
      raw: false
    });

    if (!memberProfile) {
      return res.status(404).json({
        "status": 404,
        "error": 'Member information not found'
      });
    }

    // Get ALL share records for this shareholder
    let allShareholderRows = await mem_ShareholdersModel.findAll({
      where: {
        EntityLegacyID: company.code,
        SHUniqueNr: req.params.id,
        MemberTypeCode: { [Op.notIn]: ['JSH', 'JST'] }, // ensure non-joint records only
      },
      raw: false
    });

    if (!allShareholderRows || allShareholderRows.length === 0) {
      return res.status(404).json({
        "status": 404,
        "error": 'No shareholder records found'
      });
    }

    // Get all nominator profiles (linked through MemberCode)
    const nominatorProfilesByMemberCode = {};
    for (const share of allShareholderRows) {
      if (share.MemberCode) {
        if (!nominatorProfilesByMemberCode[share.MemberCode]) {
          const nominatorProfile = await mem_MemberProfilesModel.findOne({
            where: {
              MFCode: share.MemberCode
            },
            raw: false
          });

          if (nominatorProfile) {
            nominatorProfilesByMemberCode[share.MemberCode] = {
              ...nominatorProfile.dataValues,
              isCorporate: share.MemberFileTypeCode !== "I"
            };
          }
        }
      }
    }

    // For each row in mem_Shareholders, create a history record
    const historyRecords = [];

    for (const share of allShareholderRows) {
      // Create history record for this share
      const historyRecord = await mem_ShareholdersHistoryModel.create({
        UniqueRelationID: share.UniqueRelationID,
        ClientCode: share.ClientCode,
        ClientName: share.ClientName,
        ClientUniqueNr: share.ClientUniqueNr,
        EntityCode: share.EntityCode,
        EntityName: share.EntityName,
        EntityUniqueNr: share.EntityUniqueNr,
        EntityLegacyID: share.EntityLegacyID,
        RelationType: share.RelationType,
        SHCode: share.SHCode,
        SHName: share.SHName,
        SHUniqueNr: share.SHUniqueNr,
        SHFileTypeCode: share.SHFileTypeCode,
        SHFileType: share.SHFileType,
        MemberTypeCode: share.MemberTypeCode,
        MemberType: share.MemberType,
        MemberCode: share.MemberCode,
        MemberUniqueNr: share.MemberUniqueNr,
        MemberName: share.MemberName,
        MemberFileTypeCode: share.MemberFileTypeCode,
        MemberFileType: share.MemberFileType,
        MemberDateStart: share.MemberDateStart,
        MemberDateEnd: share.MemberDateEnd,
        ShareTypeCode: share.ShareTypeCode,
        ShareClassName: share.ShareClassName,
        SHVotingRights: share.SHVotingRights,
        ShareIssueDate: share.ShareIssueDate,
        SHCertNr: share.SHCertNr,
        SHDateStart: share.SHDateStart,
        NrOfShares: share.NrOfShares,
        SHAddress: share.SHAddress || '',
        BenOwnerCode: share.BenOwnerCode,
        BenOwner: share.BenOwner,
        BenOwnerCertNr: share.BenOwnerCertNr,
        ShareholderID: share.ShareholderID,
        UpdateRequestDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
        Status: DIRMEMBER_STATUS.PENDING,
        UserEmail: req.user.username,
        TypeOfUpdateRequest: data.changeType,
        UpdateRequestComments: data.changeReason || "",
        hasNomineeArrangement: data.hasNomineeArrangement
      });

      historyRecords.push(historyRecord);

      await mem_MemberProfilesHistoryModel.create({
        ShareholderHistoryId: historyRecord.Id,
        MFCode: memberProfile.MFCode,
        MFUniqueNr: memberProfile.MFUniqueNr,
        MFName: memberProfile.MFName,
        MFTypeCode: memberProfile.MFTypeCode,
        MFLegacyID: memberProfile.MFLegacyID,
        MFType: memberProfile.MFType,
        MFIncropNr: memberProfile.MFIncropNr,
        MFIncorpDate: memberProfile.MFIncorpDate,
        MFIncorpCountry: memberProfile.MFIncorpCountry,
        MFDateOfBirth: memberProfile.MFDateOfBirth,
        MFFormerName: memberProfile.MFFormerName,
        MFBirthCountry: memberProfile.MFBirthCountry,
        MFNationality: memberProfile.MFNationality,
        MFRAAddress: memberProfile.MFRAAddress,
        MFROAddress: memberProfile.MFROAddress,
        MFRSAddress: memberProfile.MFRSAddress,
        MFStatusCode: memberProfile.MFStatusCode,
        MFStatus: memberProfile.MFStatus,
        MFProductionOffice: memberProfile.MFProductionOffice
      });

      // If this share has a nominator, create history for that nominator's profile
      if (share.MemberCode && nominatorProfilesByMemberCode[share.MemberCode]) {
        const nominatorProfile = nominatorProfilesByMemberCode[share.MemberCode];

        await mem_MemberProfilesHistoryModel.create({
          ShareholderHistoryId: historyRecord.Id,
          MFCode: nominatorProfile.MFCode,
          MFUniqueNr: nominatorProfile.MFUniqueNr,
          MFName: nominatorProfile.MFName,
          MFTypeCode: nominatorProfile.MFTypeCode,
          MFLegacyID: nominatorProfile.MFLegacyID,
          MFType: nominatorProfile.MFType,
          MFIncropNr: nominatorProfile.MFIncropNr,
          MFIncorpDate: nominatorProfile.MFIncorpDate,
          MFIncorpCountry: nominatorProfile.MFIncorpCountry,
          MFDateOfBirth: nominatorProfile.MFDateOfBirth,
          MFFormerName: nominatorProfile.MFFormerName,
          MFBirthCountry: nominatorProfile.MFBirthCountry,
          MFNationality: nominatorProfile.MFNationality,
          MFRAAddress: nominatorProfile.MFRAAddress,
          MFROAddress: nominatorProfile.MFROAddress,
          MFRSAddress: nominatorProfile.MFRSAddress,
          MFStatusCode: nominatorProfile.MFStatusCode,
          MFStatus: nominatorProfile.MFStatus,
          MFProductionOffice: nominatorProfile.MFProductionOffice
        });
      }
    }

    // Send email notification
    let emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO;

    // Get entity to determine production office
    const entity = await mem_EntitiesModel?.findOne({
      where: {
        EntityLegacyID: company.code,
      },
      raw: true
    });
    let noProductionOffice = false;
    // Set correct production office
    if (entity && entity.ProductionOffice) {
      if (entity.ProductionOffice === 'TBVI') {
        emailTo = process.env.REQUEST_UPDATE_EMAIL_TBVI;
      } else if (entity.ProductionOffice === 'THKO') {
        emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO;
      } else if (entity.ProductionOffice === 'TCYP') {
        emailTo = process.env.REQUEST_UPDATE_EMAIL_TCYP;
      } else if (entity.ProductionOffice === 'TPANVG') {
        emailTo = process.env.REQUEST_UPDATE_EMAIL_TPANVG;
      }
    } else {
      noProductionOffice = true;
    }

    // Get missing information
    const listOfRequiredFieldsCorporate = getListFieldsToValidate(TYPE_OF_MEMBER, "Corporate");
    const listOfRequiredFieldsIndividual = getListFieldsToValidate(TYPE_OF_MEMBER, "Individual");

    // Validate member profile depending on type and all nominator profiles, if any. Only add "Nominator Details" if missing values
    const missingInformation = [];
    const requiredFields = allShareholderRows[0].SHFileTypeCode === "I" ? listOfRequiredFieldsIndividual : listOfRequiredFieldsCorporate;

    const missingValues = requiredFields.filter((item) => {
      const fieldValue = allShareholderRows[0][item.field];
      const profileFieldValue = memberProfile?.[item.field];
      return !fieldValue && !profileFieldValue; // Checks for null, undefined, empty string
    }).map((item) => item.name);

    if (missingValues.length) {
      missingInformation.push(...missingValues);
    }

    for (const key in nominatorProfilesByMemberCode) {
      const nominatorProfile = nominatorProfilesByMemberCode[key];
      nominatorProfile.MemberName = allShareholderRows[0].MFName;
      nominatorProfile.MemberDateStart = allShareholderRows[0].MemberDateStart;
      const requiredFieldsNominator = getListFieldsToValidate(TYPE_OF_MEMBER, nominatorProfile.isCorporate ? "CorporateNominator" : "IndividualNominator");
      const missingNominatorValues = requiredFieldsNominator.filter((item) => {
        const fieldValue = nominatorProfile[item.field];
        return !fieldValue; // Checks for null, undefined, empty string
      }).map((item) => item.name);

      if (missingNominatorValues.length && !missingInformation.includes("Nominator Details")) {
        missingInformation.push("Nominator Details");
      }
    }

    // Send email notification
    const subject = `${process.env.EMAIL_SUBJECT_PREFIX || ''} ${company.name} – Request update for Portal`;

    let email = MailFormatter.generateMemberRequestUpdateEmail({
      "companyCode": allShareholderRows[0].EntityCode + " (" + company.code + ")",
      "companyName": allShareholderRows[0].EntityName + " (" + company.name + ")",
      "memberCode": allShareholderRows[0].SHCode || 'N/A',
      "mcc": allShareholderRows[0].ClientCode + " (" + company.masterclientcode + ")",
      "requestor": req.user.username,
      "requestType": data.changeType,
      "comment": data.changeReason || 'N/A',
      "position": "Member",
      "relationType": allShareholderRows[0].SHFileTypeCode === 'I' ? 'Individual' : 'Corporate',
      "nomineeArrangement": (data.hasNomineeArrangement || Object.keys(nominatorProfilesByMemberCode).length > 0) ? 'Yes' : 'No',
      "missingInformation": missingInformation.join(', ') || 'N/A'
    });

    let emailResponse = await MailController.asyncSend(
      emailTo,
      noProductionOffice ? '(!Production office unknown) ' + subject : subject,
      email.textString,
      email.htmlString
    );

    if (emailResponse.error) {
      console.error("Send member email error: ", emailResponse);
    }

    return res.status(200).json({
      "status": 200,
      "message": "Request an update has been created successfully"
    });

  } catch (error) {
    console.error("Error creating request an update for member: ", error);
    return res.status(500).json({
      "status": 500,
      "error": 'Internal server error'
    });
  }
}

exports.requestJointMemberToUpdate = async function (req, res) {
  try {
    const company = await getCompanyData(req.params.code, req.params.masterclientcode, req.user.email);
    const data = req.body;

    if (company?.error) {
      return res.status(company.status).json({
        "status": company.status,
        "error": company.error
      });
    }

    if (!data.changeType) {
      return res.status(400).json({
        "status": 400,
        "error": 'Please select a valid option'
      });
    }

    // Get ALL share records for this shareholder
    let allShareholderRows = await mem_ShareholdersModel.findAll({
      where: {
        EntityLegacyID: company.code,
        CombinedSHCode: req.params.jointCode,
        MemberTypeCode: { [Op.in]: ['JSH', 'JST'] }, // ensure joint records only
      },
      include: [{
        model: mem_MemberProfilesModel,
        as: 'shareholderProfile',
        required: false
      }],
      raw: false
    });

    if (!allShareholderRows || allShareholderRows.length === 0) {
      return res.status(404).json({
        "status": 404,
        "error": 'No shareholder records found'
      });
    }

    const listOfRequiredFieldsCorporate = getListFieldsToValidate(TYPE_OF_MEMBER, "Corporate");
    const listOfRequiredFieldsIndividual = getListFieldsToValidate(TYPE_OF_MEMBER, "Individual");

    let missingValues = {};
    for (const share of allShareholderRows) {
      // Create history record for this share
      const historyRecord = await mem_ShareholdersHistoryModel.create({
        UniqueRelationID: share.UniqueRelationID,
        ClientCode: share.ClientCode,
        ClientName: share.ClientName,
        ClientUniqueNr: share.ClientUniqueNr,
        EntityCode: share.EntityCode,
        EntityName: share.EntityName,
        EntityUniqueNr: share.EntityUniqueNr,
        EntityLegacyID: share.EntityLegacyID,
        RelationType: share.RelationType,
        SHCode: share.SHCode,
        SHName: share.SHName,
        SHUniqueNr: share.SHUniqueNr,
        SHFileTypeCode: share.SHFileTypeCode,
        SHFileType: share.SHFileType,
        MemberTypeCode: share.MemberTypeCode,
        MemberType: share.MemberType,
        MemberCode: share.MemberCode,
        MemberUniqueNr: share.MemberUniqueNr,
        MemberName: share.MemberName,
        MemberFileTypeCode: share.MemberFileTypeCode,
        MemberFileType: share.MemberFileType,
        MemberDateStart: share.MemberDateStart,
        MemberDateEnd: share.MemberDateEnd,
        ShareTypeCode: share.ShareTypeCode,
        ShareClassName: share.ShareClassName,
        SHVotingRights: share.SHVotingRights,
        ShareIssueDate: share.ShareIssueDate,
        SHCertNr: share.SHCertNr,
        SHDateStart: share.SHDateStart,
        NrOfShares: share.NrOfShares,
        SHAddress: share.SHAddress || '',
        BenOwnerCode: share.BenOwnerCode,
        BenOwner: share.BenOwner,
        BenOwnerCertNr: share.BenOwnerCertNr,
        ShareholderID: share.ShareholderID,
        UpdateRequestDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
        Status: DIRMEMBER_STATUS.PENDING,
        UserEmail: req.user.username,
        TypeOfUpdateRequest: data.changeType,
        UpdateRequestComments: data.changeReason || "",
        hasNomineeArrangement: data.hasNomineeArrangement
      });

      await mem_MemberProfilesHistoryModel.create({
        ShareholderHistoryId: historyRecord.Id,
        MFCode: share.shareholderProfile.MFCode,
        MFUniqueNr: share.shareholderProfile.MFUniqueNr,
        MFName: share.shareholderProfile.MFName,
        MFTypeCode: share.shareholderProfile.MFTypeCode,
        MFLegacyID: share.shareholderProfile.MFLegacyID,
        MFType: share.shareholderProfile.MFType,
        MFIncropNr: share.shareholderProfile.MFIncropNr,
        MFIncorpDate: share.shareholderProfile.MFIncorpDate,
        MFIncorpCountry: share.shareholderProfile.MFIncorpCountry,
        MFDateOfBirth: share.shareholderProfile.MFDateOfBirth,
        MFFormerName: share.shareholderProfile.MFFormerName,
        MFBirthCountry: share.shareholderProfile.MFBirthCountry,
        MFNationality: share.shareholderProfile.MFNationality,
        MFRAAddress: share.shareholderProfile.MFRAAddress,
        MFROAddress: share.shareholderProfile.MFROAddress,
        MFRSAddress: share.shareholderProfile.MFRSAddress,
        MFStatusCode: share.shareholderProfile.MFStatusCode,
        MFStatus: share.shareholderProfile.MFStatus,
        MFProductionOffice: share.shareholderProfile.MFProductionOffice
      });

      // Check missing values
      const requiredFields = share.SHFileTypeCode === "I" ? listOfRequiredFieldsIndividual : listOfRequiredFieldsCorporate;
      missingValues[share.SHCode] = requiredFields.filter((item) => {
        const fieldValue = share[item.field];
        const profileFieldValue = share.shareholderProfile?.[item.field];
        return !fieldValue && !profileFieldValue; // Checks for null, undefined, empty string
      }).map((item) => item.name);
    }

    let emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO;

    // Get entity to determine production office
    const entity = await mem_EntitiesModel?.findOne({
      where: {
        EntityLegacyID: company.code,
      },
      raw: true
    });
    let noProductionOffice = false;
    // Set correct production office
    if (entity && entity.ProductionOffice) {
      if (entity.ProductionOffice === 'TBVI') {
        emailTo = process.env.REQUEST_UPDATE_EMAIL_TBVI;
      } else if (entity.ProductionOffice === 'THKO') {
        emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO;
      } else if (entity.ProductionOffice === 'TCYP') {
        emailTo = process.env.REQUEST_UPDATE_EMAIL_TCYP;
      } else if (entity.ProductionOffice === 'TPANVG') {
        emailTo = process.env.REQUEST_UPDATE_EMAIL_TPANVG;
      }
    } else {
      noProductionOffice = true;
    }

    // Send email notification
    const subject = `${process.env.EMAIL_SUBJECT_PREFIX || ''} ${company.name} – Request update for Portal`;

    let missingInformationText = "";
    for (const shareCode in missingValues) {
      if (missingValues[shareCode].length > 0) {
        missingInformationText += `${shareCode}: ${missingValues[shareCode].join(', ')}. `;
      }
    }

    const shNumbers = [...new Set(allShareholderRows.map((e) => e.SHCode))];

    let email = MailFormatter.generateMemberRequestUpdateEmail({
      "companyCode": allShareholderRows[0].EntityCode + " (" + company.code + ")",
      "companyName": allShareholderRows[0].EntityName + " (" + company.name + ")",
      "memberCode": shNumbers.join(', '),
      "mcc": allShareholderRows[0].ClientCode + " (" + company.masterclientcode + ")",
      "requestor": req.user.username,
      "requestType": data.changeType,
      "comment": data.changeReason || 'N/A',
      "position": "Member",
      "relationType": "Joint",
      "nomineeArrangement": data.hasNomineeArrangement ? 'Yes' : 'No',
      "missingInformation": missingInformationText ? missingInformationText : 'N/A'
    });

    let emailResponse = await MailController.asyncSend(
      emailTo,
      noProductionOffice ? '(!Production office unknown) ' + subject : subject,
      email.textString,
      email.htmlString
    );

    if (emailResponse.error) {
      console.error("Send member email error: ", emailResponse);
    }

    return res.status(200).json({
      "status": 200,
      "message": "Request an update has been created successfully"
    });

  } catch (error) {
    console.error("Error creating request an update for member: ", error);
    return res.status(500).json({
      "status": 500,
      "error": 'Internal server error'
    });
  }
}

exports.requestStockOrFundToUpdate = async function (req, res) {
  try {
    const company = await getCompanyData(req.params.code, req.params.masterclientcode, req.user.email);
    const data = req.body;

    if (company?.error) {
      return res.status(company.status).json({
        "status": company.status,
        "error": company.error
      });
    }

    if (!data.changeType) {
      return res.status(400).json({
        "status": 400,
        "error": 'Please select a valid option'
      });
    }

    // Determine if this is for stock or fund
    const isStockUpdate = req.path.includes('/request-stock-update');
    const updateType = isStockUpdate ? 'STOCK' : 'FUND';

    // Get entity data
    const entity = await mem_EntitiesModel.findOne({
      where: { EntityLegacyID: company.code },
      raw: true
    });

    if (!entity) {
      return res.status(404).json({
        "status": 404,
        "error": 'Entity information not found'
      });
    }

    try {
      if (updateType === 'STOCK') {
        // Create log entry with all relevant stock fields
        await mem_EntitiesStockHistory.create({
          EntityLegacyID: company.code,
          EntityCode: entity.EntityCode || '',
          EntityName: entity.EntityName || '',
          EntityUniqueNr: entity.EntityUniqueNr,
          ClientCode: entity.ClientCode || '',
          ClientName: entity.ClientName || '',
          ClientUniqueNr: entity.ClientUniqueNr,
          // Stock-specific fields
          STXCode: entity.STXCode || '',
          STXName: entity.STXName || '',
          STXTicker: entity.STXTicker || '',
          STXJurisdictionCode: entity.STXJurisdictionCode || '',
          STXJurisdiction: entity.STXJurisdiction || '',
          STXExchange: entity.STXExchange || '',
          STXRegulator: entity.STXRegulator || '',
          STXListingDate: entity.STXListingDate || null,
          IncorporationNumber: entity.IncorporationNumber || '',
          IncorporationDate: entity.IncorporationDate || null,
          JurisdictionCode: entity.JurisdictionCode || '',
          Jurisdiction: entity.Jurisdiction || '',
          EntityTypeCode: entity.EntityTypeCode || '',
          EntityType: entity.EntityType || '',
          EntityStatusCode: entity.EntityStatusCode || '',
          EntityStatus: entity.EntityStatus || '',
          EntitySubStatusCode: entity.EntitySubStatusCode || '',
          EntitySubStatus: entity.EntitySubStatus || '',
          ProductionOffice: entity.ProductionOffice || '',
          // Request fields
          UpdateRequestDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
          Status: DIRMEMBER_STATUS.PENDING,
          UserEmail: req.user.username,
          TypeOfUpdateRequest: data.changeType,
          UpdateRequestComments: data.changeReason || "",
          ConfirmationType: updateType,
          RelationType: updateType,
          MemberTypeCode: updateType === 'STOCK' ? 'STX' : 'BRG',
          MemberType: updateType,
          SHName: entity.EntityName || '',
        });
      } else if (updateType === 'FUND') {
        // Create log entry with all relevant mutual fund fields
        await mem_EntitiesMutualFundHistory.create({
          EntityLegacyID: company.code,
          EntityCode: entity.EntityCode || '',
          EntityName: entity.EntityName || '',
          EntityUniqueNr: entity.EntityUniqueNr,
          ClientCode: entity.ClientCode || '',
          ClientName: entity.ClientName || '',
          ClientUniqueNr: entity.ClientUniqueNr,
          // Mutual Fund specific fields
          IncorporationNumber: entity.IncorporationNumber || '',
          IncorporationDate: entity.IncorporationDate || null,
          JurisdictionCode: entity.JurisdictionCode || '',
          Jurisdiction: entity.Jurisdiction || '',
          EntityTypeCode: entity.EntityTypeCode || '',
          EntityType: entity.EntityType || '',
          ProductionOffice: entity.ProductionOffice || '',
          EntityStatusCode: entity.EntityStatusCode || '',
          EntityStatus: entity.EntityStatus || '',
          EntitySubStatusCode: entity.EntitySubStatusCode || '',
          EntitySubStatus: entity.EntitySubStatus || '',
          // Business registration fields specific to funds
          BusRegNr: entity.BusRegNr || '',
          BusRegTypeCode: entity.BusRegTypeCode || '',
          BusRegType: entity.BusRegType || '',
          BusRegStartDate: entity.BusRegStartDate || null,
          BusRegEndDate: entity.BusRegEndDate || null,
          // Request fields
          UpdateRequestDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
          Status: DIRMEMBER_STATUS.PENDING,
          UserEmail: req.user.username,
          TypeOfUpdateRequest: data.changeType,
          UpdateRequestComments: data.changeReason || "",
          ConfirmationType: updateType,
          RelationType: updateType,
          MemberTypeCode: updateType === 'STOCK' ? 'STX' : 'BRG',
          MemberType: updateType,
          SHName: entity.EntityName || '',
        })
      }

      let emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO;
      let noProductionOffice = false;
      if (entity && entity.ProductionOffice) {
        if (entity.ProductionOffice === 'TBVI') {
          emailTo = process.env.REQUEST_UPDATE_EMAIL_TBVI;
        } else if (entity.ProductionOffice === 'THKO') {
          emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO;
        } else if (entity.ProductionOffice === 'TCYP') {
          emailTo = process.env.REQUEST_UPDATE_EMAIL_TCYP;
        } else if (entity.ProductionOffice === 'TPANVG') {
          emailTo = process.env.REQUEST_UPDATE_EMAIL_TPANVG;
        }
      } else {
        noProductionOffice = true;
      }
      const subject = `${process.env.EMAIL_SUBJECT_PREFIX || ''} ${company.name} – Request update for ${updateType === 'STOCK' ? 'Listed Company Information' : 'Mutual Fund Information'}`;

      // Get required fields for Stock entities
      const requiredFields = getListFieldsToValidate(TYPE_OF_MEMBER, updateType === 'STOCK' ? 'Stock' : 'MutualFund');

      // Check for missing values
      const missingFields = requiredFields.filter((item) => {
        return !entity[item.field];
      }).map((item) => item.name);

      let email = MailFormatter.generateStockOrFundRequestUpdateEmail({
        "companyCode": entity.EntityCode + " (" + company.code + ")",
        "companyName": entity.EntityName + " (" + company.name + ")",
        "mcc": entity.ClientCode + " (" + company.masterclientcode + ")",
        "requestor": req.user.username,
        "requestType": data.changeType,
        "comment": data.changeReason || 'N/A',
        "relationType": updateType === 'STOCK' ? 'Listed Company' : 'Mutual Fund',
        "missingInformation": missingFields.join(', ') || 'N/A'
      });

      let emailResponse = await MailController.asyncSend(
        emailTo,
        noProductionOffice ? '(!Production office unknown) ' + subject : subject,
        email.textString,
        email.htmlString
      );

      if (emailResponse.error) {
        console.error(`Send ${updateType} email error: `, emailResponse);
      }

      return res.status(200).json({
        "status": 200,
        "message": `${updateType} update request created successfully`
      });
    } catch (error) {
      console.error(`Error in creating ${updateType} update request: `, error);
      return res.status(500).json({
        "status": 500,
        "error": `Failed to create ${updateType} update request`
      });
    }
  } catch (e) {
    console.error("Error creating request an update for stock/fund: ", e);
    return res.status(500).json({
      "status": 500,
      "error": 'Internal server error'
    });
  }
}
