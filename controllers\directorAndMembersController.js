const httpConstants = require('http2').constants
const moment = require('moment')
const excel = require('node-excel-export')
const sqlDb = require('../models-sql')
const mem_Directors = sqlDb.mem_Directors
const mem_Shareholders = sqlDb.mem_Shareholders
const mem_DirectorsHistory = sqlDb.mem_DirectorsHistory
const mem_ShareholdersHistory = sqlDb.mem_ShareholdersHistory
const mem_MemberProfiles = sqlDb.mem_MemberProfiles
const mem_BeneficialOwners = sqlDb.mem_BeneficialOwners
const mem_BeneficialOwnersHistory = sqlDb.mem_BeneficialOwnersHistory
const { Op } = require('sequelize')
const _ = require('lodash')
const { getContainerClient } = require('../utils/azureStorage')
const {
  DIRECTOR_REQUIRED_FIELDS,
  MEMBER_REQUIRED_FIELDS,
  BENEFICIAL_OWNER_REQUIRED_FIELDS
} = require('../utils/directorAndMemberConstants')
const CompanyModel = require('../models/company').schema;

exports.getDashboard = async function (req, res, next) {
  try {
    const showImportDataModule =
      req.session.authentication.isDirBoImportManager
    res.render('director-and-members/index', {
      user: req.session.user,
      title: 'Company Information Dashboard',
      authentication: req.session.authentication,
      hasProductionOfficeGroups:
        Array.isArray(req.session.productionOfficeGroups) &&
        req.session.productionOfficeGroups.length > 0,
      showImportDataModule
    })
  } catch (e) {
    next(e)
  }
}

exports.getSearch = async function (req, res, next) {
  const baseFilter = {
    filter_name: '',
    filter_director_name: '',
    filter_masterclient: '',
    filter_entity_number: '',
    filter_referral: '',
    filter_position: '',
    filter_production: '',
    filter_confirmed_range_start: '',
    filter_confirmed_range_end: '',
    status: [],
    subStatus: []
  }
  const filters = _.isEmpty(req.body) ? baseFilter : req.body
  try {
    const [results, hasFilter] = await searchData(req)
    const vpDirectors = _.isEmpty(req.body) ? [] : results
    res.render('director-and-members/search', {
      title: 'Director/Member Search',
      vpDirectors,
      user: req.user,
      filters,
      hasFilter,
      productionOfficeOptions: req.session.productionOfficeGroups || [],
      messages: req.session.messages
    })
  } catch (e) {
    next(e)
  }
}

exports.getImportDataHistoryView = async function (req, res, next) {
  try {
    const containerClient = getContainerClient(
      process.env.AZURE_STORAGE_CONTAINER_MEMBERINFO_ARCHIVE,
      process.env.AZURE_STORAGE_VPDIRECTOR_ACCOUNT,
      process.env.AZURE_STORAGE_VPDIRECTOR_ACCESS_KEY
    )
    const response = containerClient.listBlobsFlat()
    const importFiles = []
    for await (const blob of response) {
      const filename = blob.name.includes('/')
        ? blob.name.split('/').pop()
        : blob.name
      importFiles.push({
        name: blob.name,
        filename,
        createdAt: moment(blob.properties.createdOn)
          .utc()
          .format('YYYY-MM-DD HH:mm'),
        updatedAt: moment(blob.properties.lastModified)
          .utc()
          .format('YYYY-MM-DD HH:mm')
      })
    }
    importFiles.sort(
      (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
    )
    res.render('director-and-members/import-data-history', {
      title: 'Import Company Information Data',
      importFiles
    })
  } catch (e) {
    next(e)
  }
}

exports.downloadArchiveVPDirectorFile = async function (req, res, next) {
  try {
    const containerClient = getContainerClient(
      process.env.AZURE_STORAGE_CONTAINER_MEMBERINFO_ARCHIVE,
      process.env.AZURE_STORAGE_VPDIRECTOR_ACCOUNT,
      process.env.AZURE_STORAGE_VPDIRECTOR_ACCESS_KEY
    )
    const filename = req.params.filename
    if (!filename) {
      const err = new Error('File not found')
      err.status = 404
      return next(err)
    }
    const blobClient = containerClient.getBlobClient(filename)
    const downloadResponse = await blobClient.download()
    if (downloadResponse.contentLength === 0) {
      return res
        .status(httpConstants.HTTP_STATUS_NOT_FOUND)
        .send({ message: 'File not found' })
    }
    res.set(
      'Content-Disposition',
      `attachment; filename="${filename}"`
    )
    downloadResponse.readableStreamBody.pipe(res)
  } catch (e) {
    const err = new Error('Error downloading file')
    err.status = 502
    next(err)
  }
}

exports.exportSearchXls = async function (req, res, next) {
  try {
    const [entries] = await searchData(req)
    const styles = {
      headerTable: {
        fill: { fgColor: { rgb: 'ffffff' } },
        border: {
          top: { style: 'thin', color: '000000' },
          bottom: { style: 'thin', color: '000000' },
          left: { style: 'thin', color: '000000' },
          right: { style: 'thin', color: '000000' }
        },
        font: { color: { rgb: '000000' }, sz: 12 }
      }
    }
    const spec = {
      Entity_Name: { displayName: 'Entity Name', headerStyle: styles.headerTable, width: 120 },
      Entity_Number: { displayName: 'VP Entity Number', headerStyle: styles.headerTable, width: 120 },
      Company_Number: { displayName: 'Entity Portal Code', headerStyle: styles.headerTable, width: 120 },
      Master_Client_Code: { displayName: 'Master Client Code', headerStyle: styles.headerTable, width: 160 },
      Referral_Office: { displayName: 'Referral Office', headerStyle: styles.headerTable, width: 120 },
      Production_Office: { displayName: 'Production Office', headerStyle: styles.headerTable, width: 120 },
      Director_and_BO: { displayName: 'Director/BO Name', headerStyle: styles.headerTable, width: 120 },
      Position: { displayName: 'Position', headerStyle: styles.headerTable, width: 120 },
      Type: { displayName: 'Type', headerStyle: styles.headerTable, width: 120 },
      Share_Certificate_Numbers: { displayName: 'Share Certificate Number(s)', headerStyle: styles.headerTable, width: 180 },
      Status: { displayName: 'Status', headerStyle: styles.headerTable, width: 120 },
      SubStatus: { displayName: 'Specifics', headerStyle: styles.headerTable, width: 120 },
      Request_Update_Date: { displayName: 'Request Update Date', headerStyle: styles.headerTable, width: 120 },
      Confirmed_Update_Date: { displayName: 'Confirmed Date', headerStyle: styles.headerTable, width: 120 }
    }
    const dataset = entries.map(e => ({
      Entity_Name: e.EntityName,
      Entity_Number: e.EntityCode,
      Master_Client_Code: e.MasterClientCode,
      Company_Number: e.CompanyNumber,
      Referral_Office: e.ReferralOffice,
      Director_and_BO: e.Name || '',
      Position: e.RelationType === 'Owner/Controller' ? 'BO' : e.RelationType,
      Type: e.Type || '',
      Share_Certificate_Numbers: e.ShareCertificateNumbers || '',
      Production_Office: e.ProductionOffice || '',
      Status: e.Status === 'VP DATA RECEIVED' ? 'SUBSEQUENT' : e.Status,
      SubStatus:
        ['CONFIRMED', 'PENDING UPDATE REQUEST'].includes(e.Status)
          ? ''
          : e.MissingInfo === 1
            ? 'MISSING INFORMATION'
            : '',
      Request_Update_Date: e.UpdateRequestDate
        ? moment(e.UpdateRequestDate).format('YYYY-MM-DD')
        : '',
      Confirmed_Update_Date:
        ['VP DATA RECEIVED', 'REFRESHED'].includes(e.Status)
          ? ''
          : e.ConfirmedDate
            ? moment(e.ConfirmedDate).format('YYYY-MM-DD')
            : ''
    }))
    const report = excel.buildExport([{ name: 'Report', specification: spec, data: dataset }])
    res.attachment('Search_Report.xlsx')
    res.send(report)
  } catch (e) {
    next(e)
  }
}

async function searchData(req) {
  const {
    filter_name,
    filter_director_name,
    filter_masterclient,
    filter_entity_number,
    filter_referral,
    filter_position,
    filter_production,
    filter_confirmed_range_start,
    filter_confirmed_range_end,
    status: statusFilter,
    subStatus: subStatusFilter
  } = req.body || {}

  const hasFilter =
    filter_name ||
    filter_director_name ||
    filter_masterclient ||
    filter_entity_number ||
    filter_referral ||
    filter_position ||
    statusFilter ||
    subStatusFilter
  if (!hasFilter) return [[], false]

  const prodGroups = req.session.productionOfficeGroups || []

  const companyFilter = { partitionkey: 'company', isDeleted: { $ne: true } }
  if (filter_name) companyFilter.name = { $regex: filter_name, $options: 'i' }
  if (filter_masterclient?.length >= 2)
    companyFilter.masterclientcode = { $regex: filter_masterclient, $options: 'i' }
  if (filter_entity_number?.length > 2)
    companyFilter.code = { $regex: filter_entity_number, $options: 'i' }
  if (filter_referral)
    companyFilter.referral_office = { $regex: filter_referral, $options: 'i' }

  const companies = await CompanyModel.find(
    companyFilter,
    { _id: 1, code: 1, name: 1, masterclientcode: 1, referral_office: 1, productionOffice: 1 }
  )
  if (!companies.length) return [[], true]

  const companyMap = _.keyBy(companies, 'code')
  const codes = companies.map(c => c.code)

  const dirWhere = {
    EntityLegacyID: { [Op.in]: codes },
    ...(filter_director_name && { DirName: { [Op.like]: `%${filter_director_name}%` } }),
    ...(filter_position === 'Member' && { DirUniqueNr: null })
  }
  const shWhere = {
    EntityLegacyID: { [Op.in]: codes },
    ...(filter_director_name && { SHName: { [Op.like]: `%${filter_director_name}%` } }),
    ...(filter_position === 'Director' && { SHUniqueNr: null })
  }

  const [allDirs, allShs] = await Promise.all([
    mem_Directors.findAll({
      where: dirWhere,
      include: [{
        model: mem_MemberProfiles,
        as: 'directorProfile',
        required: false
      }],
      raw: false
    }),
    mem_Shareholders.findAll({
      where: shWhere,
      include: [{
        model: mem_MemberProfiles,
        as: 'shareholderProfile',
        required: false
      }],
      raw: false
    })
  ])

  // Group shareholders using CombinedSHCode for joint members (same logic as client portal)
  const shareholderGroups = {}
  const individualShareholders = []

  allShs.forEach((sh) => {
    if (sh.CombinedSHCode) {
      // This is a joint member
      if (!shareholderGroups[sh.CombinedSHCode]) {
        shareholderGroups[sh.CombinedSHCode] = []
      }
      shareholderGroups[sh.CombinedSHCode].push(sh)
    } else {
      // This is an individual member
      individualShareholders.push(sh)
    }
  })

  // Process joint shareholders into grouped records
  const jointShareholders = []
  Object.entries(shareholderGroups).forEach(([combinedCode, members]) => {
    const base = members[0]

    // Determine if this is a corporate or individual joint group
    // If any member is corporate, treat the whole group as corporate
    const hasCorporateMember = members.some(m => ["C", "O", "P", "T", "N", "0"].includes(m.SHFileTypeCode))
    const jointFileType = hasCorporateMember ? "Corporate" : "Individual"

    const jointRecord = {
      ...base.dataValues,
      // Combine names with " & " separator
      CombinedSHCode: combinedCode,
      SHName: members.map(m => m.SHName).join(' & '),
      // Collect all unique certificate numbers
      certificateNumbers: _(members)
        .map('SHCertNr')
        .filter(cert => cert && cert.trim() !== '')
        .uniq()
        .value(),
      // Use the first member's dates as representative
      SHDateStart: base.SHDateStart,
      ShareIssueDate: base.ShareIssueDate,
      // Mark as joint and set file type
      isJoint: true,
      jointMembers: members,
      jointFileType: jointFileType
    }
    jointShareholders.push(jointRecord)
  })

  // Combine individual and joint shareholders
  const repsSh = [...individualShareholders, ...jointShareholders]

  // Get beneficial owners data
  const allBOs = await mem_BeneficialOwners.findAll({
    where: { EntityLegacyID: { [Op.in]: codes } },
    raw: false
  })

  // Group joint beneficial owners using the same logic as client portal
  const boJointGroups = {}
  allBOs.forEach((bo) => {
    const jointKey = bo.BOJointOwnerCode || bo.BOJointOwnerUniqueNr
    if (!jointKey) return
    if (!boJointGroups[jointKey]) boJointGroups[jointKey] = []
    boJointGroups[jointKey].push(bo)
  })

  // Process beneficial owners similar to client portal
  const individualBOs = []
  const exemptedBOs = []
  const jointBOs = []

  allBOs.forEach((bo) => {
    if (bo.BOMemberType === 'Joint Owner') {
      jointBOs.push(bo)
    } else if (bo.BOMemberType === 'Beneficial Owner') {
      individualBOs.push(bo)
    } else if (bo.BOMemberType === 'Exempt Owner') {
      exemptedBOs.push(bo)
    }
  })

  // Group and flatten joint BOs
  const jointBOList = []
  Object.entries(boJointGroups).forEach(([key, members]) => {
    const base = members[0]
    jointBOList.push({
      jointKey: key,
      BOName: members.map((m) => m.BOName).join(' & '),
      BONatureOfInterestCode: base.BONatureOfInterestCode,
      BODateCommenced: base.BODateCommenced,
      members,
      isJoint: true
    })
  })

  const dirIds = allDirs.map(d => d.UniqueRelationID)
  const shIds = repsSh.map(r => r.UniqueRelationID)
  const boIds = allBOs.map(b => b.UniqueRelationID)
  const [dirH, shH, boH] = await Promise.all([
    mem_DirectorsHistory.findAll({ where: { UniqueRelationID: { [Op.in]: dirIds } } }),
    mem_ShareholdersHistory.findAll({ where: { UniqueRelationID: { [Op.in]: shIds } } }),
    mem_BeneficialOwnersHistory.findAll({ where: { UniqueRelationID: { [Op.in]: boIds } } })
  ])
  const dirHistMap = _.groupBy(dirH, 'UniqueRelationID')
  const shHistMap = _.groupBy(shH, 'UniqueRelationID')
  const boHistMap = _.groupBy(boH, 'UniqueRelationID')

  const Status = statusFilter ? _.castArray(statusFilter) : []
  const SubStatus = subStatusFilter ? _.castArray(subStatusFilter) : []
  const posFilter = filter_position && filter_position !== 'all'
    ? filter_position
    : null

  const results = []

  for (const dir of allDirs) {
    const comp = companyMap[dir.EntityLegacyID]
    const { status, confirmedDate, updateRequestDate } =
      determineStatusFromHistory(dirHistMap[dir.UniqueRelationID] || [])

    const directorForValidation = { ...dir.dataValues }

    if (dir.directorProfile) {
      directorForValidation.ServiceAddress = dir.directorProfile.MFRSAddress
      directorForValidation.ResidentialOrRegisteredAddress = dir.directorProfile.MFRAAddress

      if (dir.DirFileType?.toLowerCase() === "individual") {
        directorForValidation.DateOfBirthOrIncorp = dir.directorProfile.MFDateOfBirth
        directorForValidation.PlaceOfBirthOrIncorp = dir.directorProfile.MFBirthCountry
        directorForValidation.Nationality = dir.directorProfile.MFNationality
      } else {
        directorForValidation.MFIncropNr = dir.directorProfile.MFIncropNr
        directorForValidation.MFROAddress = dir.directorProfile.MFROAddress
        directorForValidation.MFIncorpDate = dir.directorProfile.MFIncorpDate
        directorForValidation.MFIncorpCountry = dir.directorProfile.MFIncorpCountry
      }
    }

    const fields = getListFieldsToValidate(false, dir.DirFileType?.toLowerCase() === "individual" ? "Individual" : "Corporate")
    const missing = fields.some(item => {
      const fieldValue = directorForValidation[item.field]
      return fieldValue === null || fieldValue === undefined || fieldValue === ""
    })

    // Calculate Type for Director
    const directorType = dir.DirFileType?.toLowerCase() === "individual" ? "Individual" : "Corporate"

    const rec = {
      CompanyNumber: comp.code,
      EntityCode: comp.code,
      EntityName: comp.name,
      MasterClientCode: comp.masterclientcode,
      ReferralOffice: comp.referral_office,
      Name: dir.DirName,
      Code: dir.DirCode,
      RelationType: 'Director',
      FileType: dir.DirFileType,
      Type: directorType,
      ShareCertificateNumbers: null, // Directors don't have certificate numbers
      UniqueRelationId: dir.UniqueRelationID,
      OfficerType: dir.DirOfficerType,
      FromDate: dir.DirFromDate,
      ToDate: dir.DirToDate,
      ProductionOffice: comp.productionOffice,
      UpdateRequestDate: updateRequestDate,
      ConfirmedDate: confirmedDate,
      Status: status,
      MissingInfo: missing
    }

    if (posFilter && rec.RelationType !== posFilter) continue
    if (passesAllFilters(rec, Status, SubStatus, filter_production, prodGroups, filter_confirmed_range_start, filter_confirmed_range_end)) {
      results.push(rec)
    }
  }

  for (const rep of repsSh) {
    const comp = companyMap[rep.EntityLegacyID]
    const { status, confirmedDate, updateRequestDate } =
      determineStatusFromHistory(shHistMap[rep.UniqueRelationID] || [])

    const memberForValidation = { ...rep.dataValues }
    if (rep.shareholderProfile) {
      memberForValidation.shareholderProfile = rep.shareholderProfile.dataValues
    }

    // For joint members, we need to check if any member has the required profile
    let missing = false
    if (rep.isJoint && rep.jointMembers) {
      // Check if any joint member has the required profile
      const hasProfile = rep.jointMembers.some(member => member.shareholderProfile)
      if (!hasProfile) {
        missing = true
      } else {
        // Use the joint file type for validation
        const fields = getListFieldsToValidate(true, rep.jointFileType)
        missing = fields.some(item => {
          // Check if any joint member has the required field
          const hasField = rep.jointMembers.some(member => {
            const fieldValue = member[item.field]
            const profileFieldValue = member.shareholderProfile?.dataValues?.[item.field]
            return fieldValue || profileFieldValue
          })
          return !hasField
        })
      }
    } else {
      // Individual member validation
      const fields = getListFieldsToValidate(true, rep.SHFileTypeCode === "I" ? "Individual" : "Corporate")
      missing = fields.some(item => {
        const fieldValue = memberForValidation[item.field]
        const profileFieldValue = memberForValidation.shareholderProfile?.[item.field]
        return !fieldValue && !profileFieldValue
      })
    }

    // Calculate Type for Member/Shareholder
    let memberType
    if (rep.isJoint) {
      memberType = "Joint"
    } else if (rep.SHFileTypeCode === "I") {
      memberType = "Individual"
    } else if (["C", "O", "P", "T", "N", "0"].includes(rep.SHFileTypeCode)) {
      memberType = "Corporate"
    } else {
      memberType = "Unknown"
    }

    // Format certificate numbers as comma-separated string
    const certificateNumbers = rep.certificateNumbers && rep.certificateNumbers.length > 0
      ? rep.certificateNumbers.join(', ')
      : null

    const rec = {
      CompanyNumber: comp.code,
      EntityCode: comp.code,
      EntityName: comp.name,
      MasterClientCode: comp.masterclientcode,
      ReferralOffice: comp.referral_office,
      Name: rep.SHName,
      Code: rep.SHCode,
      RelationType: 'Member',
      FileType: rep.SHFileType,
      Type: memberType,
      ShareCertificateNumbers: certificateNumbers,
      UniqueRelationId: rep.UniqueRelationID,
      OfficerType: rep.MemberType,
      FromDate: rep.MemberDateStart,
      ToDate: rep.MemberDateEnd,
      ProductionOffice: comp.productionOffice,
      UpdateRequestDate: updateRequestDate,
      ConfirmedDate: confirmedDate,
      Status: status,
      MissingInfo: missing
    }

    if (posFilter && rec.RelationType !== posFilter) continue
    if (passesAllFilters(rec, Status, SubStatus, filter_production, prodGroups, filter_confirmed_range_start, filter_confirmed_range_end)) {
      results.push(rec)
    }
  }

  // Process beneficial owners
  for (const bo of allBOs) {
    const comp = companyMap[bo.EntityLegacyID]
    const { status, confirmedDate, updateRequestDate } =
      determineStatusFromHistory(boHistMap[bo.UniqueRelationID] || [])

    // Determine BO type for UI and required field lookup
    let boType
    if (bo.BOMemberType === 'Exempt Reporting Entity') {
      switch (bo.BOExemptedOwnerTypeCode) {
        case 'VGRS10':
          boType = 'Listed Company'
          break
        case 'VGRS11':
          boType = 'Subsidiaries'
          break
        case 'VGRS12':
        case 'VGRS03':
          boType = 'Licensed Trustee'
          break
        case 'VGRS13':
          boType = 'Foreign Fund'
          break
        case 'VGRS09':
          boType = 'Specified Fund'
          break
        default:
          boType = 'Unknown Exemption'
      }
    } else if (bo.BOMemberType === 'Beneficial Owner') {
      boType = 'Individual BO'
    } else if (bo.BOMemberType === 'Joint Owner') {
      boType = 'Joint BO'
    } else if (bo.BOMemberType === 'Exempt Owner') {
      switch (bo.BOExemptedOwnerTypeCode) {
        case 'VGRS10':
          boType = 'Exempted Owner Listed Company'
          break
        case 'VGRS12':
        case 'VGRS03':
          boType = 'Exempted Owner Licensed Trustee'
          break
        case 'VGRS13':
          boType = 'Exempted Owner Foreign Fund'
          break
        case 'VGRS09':
          boType = 'Exempted Owner Specified Fund'
          break
        default:
          boType = 'Unknown Exempted Owner'
      }
    } else {
      boType = 'Unknown'
    }

    // Check if this is a joint BO that should be grouped
    const isJointGrouped = jointBOList.some(jb =>
      jb.members.some(m => m.UniqueRelationID === bo.UniqueRelationID)
    )

    // Skip individual joint BOs as they'll be handled by the grouped version
    if (bo.BOMemberType === 'Joint Owner' && isJointGrouped) continue

    // Validate beneficial owner required fields
    const boFields = getListFieldsToValidate(true, boType)
    const missing = boFields.some(item => {
      const fieldValue = bo[item.field]
      return fieldValue === null || fieldValue === undefined || fieldValue === ""
    })

    // Validate BO Interest fields based on BONatureOfInterestCode
    let interestMissing = false
    if (bo.BOMemberType !== 'Exempt Reporting Entity' && bo.BONatureOfInterestCode) {
      if (bo.BONatureOfInterestCode === 'VGNI10' || bo.BONatureOfInterestCode === 'VGNI11') {
        // Ownership - BOInterestPercent is mandatory
        if (bo.BOInterestPercent === null || bo.BOInterestPercent === undefined || bo.BOInterestPercent === '') {
          interestMissing = true
        }
      } else if (bo.BONatureOfInterestCode === 'VGNI12') {
        // Control - at least one control field must be present
        const hasAnyControl = !!bo.BOInterestByControl1 || !!bo.BOInterestByControl2 || !!bo.BOInterestByControl3 || !!bo.BOInterestByControlOther
        if (!hasAnyControl) {
          interestMissing = true
        }
      }
    }

    const rec = {
      CompanyNumber: comp.code,
      EntityCode: comp.code,
      EntityName: comp.name,
      MasterClientCode: comp.masterclientcode,
      ReferralOffice: comp.referral_office,
      Name: bo.BOName,
      Code: bo.BOCode,
      RelationType: 'Beneficial Owner',
      FileType: bo.BOMemberType,
      Type: boType,
      ShareCertificateNumbers: null, // BOs don't have certificate numbers
      UniqueRelationId: bo.UniqueRelationID,
      OfficerType: bo.BOMemberType,
      FromDate: bo.BODateCommenced,
      ToDate: bo.BODateCeased,
      ProductionOffice: comp.productionOffice,
      UpdateRequestDate: updateRequestDate,
      ConfirmedDate: confirmedDate,
      Status: status,
      MissingInfo: missing || interestMissing ? 1 : 0
    }

    if (posFilter && rec.RelationType !== posFilter) continue
    if (passesAllFilters(rec, Status, SubStatus, filter_production, prodGroups, filter_confirmed_range_start, filter_confirmed_range_end)) {
      results.push(rec)
    }
  }

  // Add grouped joint BOs
  for (const jointBO of jointBOList) {
    const comp = companyMap[jointBO.members[0].EntityLegacyID]
    const { status, confirmedDate, updateRequestDate } =
      determineStatusFromHistory(boHistMap[jointBO.members[0].UniqueRelationID] || [])

    // Validate joint BO required fields
    const jointBOFields = getListFieldsToValidate(true, 'Joint BO')
    const missing = jointBOFields.some(item => {
      const fieldValue = jointBO.members[0][item.field]
      return fieldValue === null || fieldValue === undefined || fieldValue === ""
    })

    // Validate BO Interest fields
    let interestMissing = false
    if (jointBO.BONatureOfInterestCode) {
      if (jointBO.BONatureOfInterestCode === 'VGNI10' || jointBO.BONatureOfInterestCode === 'VGNI11') {
        // Ownership - check if any member has percentage
        const hasPercentage = jointBO.members.some(m =>
          m.BOInterestPercent !== null && m.BOInterestPercent !== undefined && m.BOInterestPercent !== ''
        )
        if (!hasPercentage) {
          interestMissing = true
        }
      } else if (jointBO.BONatureOfInterestCode === 'VGNI12') {
        // Control - check if any member has control fields
        const hasAnyControl = jointBO.members.some(m =>
          !!m.BOInterestByControl1 || !!m.BOInterestByControl2 || !!m.BOInterestByControl3 || !!m.BOInterestByControlOther
        )
        if (!hasAnyControl) {
          interestMissing = true
        }
      }
    }

    const rec = {
      CompanyNumber: comp.code,
      EntityCode: comp.code,
      EntityName: comp.name,
      MasterClientCode: comp.masterclientcode,
      ReferralOffice: comp.referral_office,
      Name: jointBO.BOName,
      Code: jointBO.members[0].BOCode,
      RelationType: 'Beneficial Owner',
      FileType: 'Joint Owner',
      Type: 'Joint BO',
      ShareCertificateNumbers: null,
      UniqueRelationId: jointBO.members[0].UniqueRelationID,
      OfficerType: 'Joint Owner',
      FromDate: jointBO.BODateCommenced,
      ToDate: null,
      ProductionOffice: comp.productionOffice,
      UpdateRequestDate: updateRequestDate,
      ConfirmedDate: confirmedDate,
      Status: status,
      MissingInfo: missing || interestMissing ? 1 : 0
    }

    if (posFilter && rec.RelationType !== posFilter) continue
    if (passesAllFilters(rec, Status, SubStatus, filter_production, prodGroups, filter_confirmed_range_start, filter_confirmed_range_end)) {
      results.push(rec)
    }
  }

  if (!results.length && SubStatus.includes('NO DIRECTOR/MEMBER/PARTNER/BO')) {
    for (const comp of companies) {
      if (checkProductionOfficeAccess(comp.productionOffice, prodGroups, filter_production)) {
        results.push(createCompanyOnlyRecord(comp))
      }
    }
  }

  return [results, true]
}

function determineStatusFromHistory(historyRecords) {
  let status = 'NOT CONFIRMED', confirmedDate = null, updateRequestDate = null
  if (historyRecords.length) {
    const sorted = _.orderBy(historyRecords, ['CreatedAt'], ['desc'])
    status = sorted[0].Status || status
    confirmedDate = sorted[0].ConfirmedDate
    updateRequestDate = sorted[0].UpdateRequestDate
    const conf = historyRecords.find(h => h.Status === 'CONFIRMED')
    if (conf) { status = 'CONFIRMED'; confirmedDate = conf.ConfirmedDate }
  }
  return { status, confirmedDate, updateRequestDate }
}

function passesAllFilters(rec, Status, SubStatus, filterProduction, productionOfficeGroups = [], dateStart, dateEnd) {
  if (Status.length && !Status.includes(rec.Status)) return false
  if (!checkProductionOfficeAccess(rec.ProductionOffice, productionOfficeGroups, filterProduction)) return false
  if (!checkDateRangeFilter(rec.ConfirmedDate, dateStart, dateEnd)) return false
  if (!checkSubStatusFilter(SubStatus, rec)) return false
  return true
}

function checkProductionOfficeAccess(compOffice, productionOfficeGroups = [], filterProduction) {
  if (filterProduction) {
    if (compOffice !== filterProduction) return false
  } else {
    if (compOffice && !productionOfficeGroups.includes(compOffice) && ['THKO', 'TBVI', 'TCYP', 'TPANVG'].includes(compOffice))
      return false
  }
  return true
}

function checkDateRangeFilter(date, start, end) {
  if (start && end) {
    if (!date) return false
    const d = new Date(date)
    return d >= new Date(start) && d <= new Date(end)
  }
  if (start) {
    if (!date) return false
    return new Date(date) >= new Date(start)
  }
  if (end) {
    if (!date) return false
    return new Date(date) <= new Date(end)
  }
  return true
}

function checkSubStatusFilter(SubStatus, record) {
  if (!SubStatus.length) return true
  if (SubStatus.includes('NO DIRECTOR/MEMBER/PARTNER/BO')) return false
  if (SubStatus.includes('MISSING INFORMATION') && !record.MissingInfo) return false
  return true
}

function createCompanyOnlyRecord(company) {
  return {
    CompanyNumber: company.code,
    EntityCode: company.code,
    EntityName: company.name,
    MasterClientCode: company.masterclientcode,
    ReferralOffice: company.referral_office,
    Name: null,
    RelationType: null,
    Type: null,
    ShareCertificateNumbers: null,
    ProductionOffice: company.productionOffice,
    Status: 'NO DIRECTOR/MEMBER/PARTNER/BO',
    MissingInfo: 0
  }
}

function getListFieldsToValidate(isMember, officerType) {
  if (isMember) {
    return MEMBER_REQUIRED_FIELDS[officerType] || []
  }
  if (officerType && officerType.includes('BO')) {
    return BENEFICIAL_OWNER_REQUIRED_FIELDS[officerType] || []
  }
  return DIRECTOR_REQUIRED_FIELDS[officerType] || []
}