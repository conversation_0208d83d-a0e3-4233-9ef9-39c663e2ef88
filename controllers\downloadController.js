const {
    filereview: FileReviewModel,
} = require('../models/filereview');
const NaturalPersonModel = require('../models/naturalperson');
const OrganizationModel = require('../models/organization');
const ClientReviewModel = require('../models/client');
const CompanyIncorporationModel = require('../models/clientIncorporation');
const MessageModel = require('../models/message');
const EntryModel = require("../models/entry").EntryModel;
const FinancialReportModel = require('../models/financialreport');
const relationController = require('./relations/relationController');
const pdfController = require('./pdfController');
const Archiver = require('archiver');
const httpConstants = require('http2').constants;
const {validate: validateUUID} = require('uuid');
const { getContainerClient } = require('../utils/azureStorage');

exports.downloadReviewFiles = async function (req, res, next) {
    try {
        let review;
        let clientReview;
        let filesToReturn;
        let uploadFiles;
        if (req.params.type === "standard") {
            review = await FileReviewModel.findById(req.params.companyId);
            clientReview = await ClientReviewModel.findById(review.clientId);
            const file = review.files.find((file) => file.id.toString() === req.query.fileId);
            uploadFiles = file.uploadFiles;
            filesToReturn = clientReview.files.filter((file) => uploadFiles.includes(file.fileId));
        } else if (req.params.type === "natural") {
            review = await NaturalPersonModel.findOne({_id: req.params.companyId})
        } else {
            review = await OrganizationModel.findOne({_id: req.params.companyId})
        }

        if (!review) {
            const err = new Error('Review not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }

        downloadApplicationFile(req, res, next, filesToReturn, true);
    } catch (e) {
        const err = new Error('File not found');
        err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
        return next(err);
    }
};

exports.downloadFile = async function (req, res, next) {
    try {
        let review = await FileReviewModel.findById(req.params.reviewId);
        let fileToDownload;
        let relationFiles = [];
        if (review) {
            if (req.params.type === "standard") {
                const clientReview = await ClientReviewModel.findById(review.clientId);
                // const rowFiles = review.files.find((file) => file.id.toString() === req.query.rowId);
                fileToDownload = clientReview.files.find((file) => file.fileId === req.params.fileId);
            }
            else if (req.params.type === "natural" && req.params.relationId){
                const naturalRelation = await NaturalPersonModel.findById(req.params.relationId);
                const electronicIdFiles = naturalRelation.electronicIdInfo && naturalRelation.electronicIdInfo.files ?
                  naturalRelation.electronicIdInfo.files : [];
                relationFiles = [...naturalRelation.identification.files, ...naturalRelation.pepDetails.files,
                    ...naturalRelation.worldCheck.files, ...electronicIdFiles];


            }else if (req.params.relationId){
                const orgRelation = await OrganizationModel.findById(req.params.relationId);
                relationFiles = [...orgRelation.details.files, ...orgRelation.detailsPartner.files,
                    ...orgRelation.mutualFundDetails.files, ...orgRelation.foundation.files, ...orgRelation.worldCheck.files];
            }

            if (relationFiles.length){
                const rowFile = relationFiles.find((relFile) => relFile.id === req.params.rowId);
                if (rowFile){
                    fileToDownload = rowFile.uploadFiles.find((uploadFile) => uploadFile.fileId === req.params.fileId);
                }
            }

            if (!fileToDownload && req.params.type !== "standard" ){
                const tempFiles = relationController.getTempUploadFiles(req.session);
                if (tempFiles){
                    const fileKeys = Object.keys(tempFiles);
                    for (const key of fileKeys){
                        const sectionKey = tempFiles[key];
                        for (const rowIndex of Object.keys(sectionKey)){
                            const row = sectionKey[rowIndex];
                            const file = row.find((f) => f.fileId === req.params.fileId);
                            if (file){
                                fileToDownload = file;
                                break;
                            }
                        }
                        if (fileToDownload){
                            break;
                        }
                    }
                }
            }
            if (fileToDownload) {
                downloadApplicationFile(req, res, next, fileToDownload, false);
            }

        } else {
            res.redirect('/file-reviewer/dashboard');
        }
    } catch (e) {
        console.log(e);
        const err = new Error('File not found');
        err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
        return next(err);
    }
};

const downloadApplicationFile = (req, res, next, files, multipleFiles) => {
    const containerClient = getContainerClient(process.env.AZURE_STORAGE_CONTAINER_FILEREVIEW,
      process.env.AZURE_STORAGE_ACCOUNT,
      process.env.AZURE_STORAGE_ACCESS_KEY);
    
    let blobUrl = "";

    if (multipleFiles){
        if (files && files.length > 0) {
            for (let idx=0;idx < files.length;idx++){

                if (files[idx].files){
                    const exists = files[idx].files.find((upload) => upload.id.toString() === req.params.fileId);
                    if (exists){
                        blobUrl = exists.url;
                        break
                    }
                }
            }

        }
    }
    else{
        blobUrl = files.url;
    }

    if (blobUrl.length > 0) {
        const pathFile = blobUrl.split(process.env.AZURE_STORAGE_CONTAINER_FILEREVIEW + "/" );

        const blobClient = containerClient.getBlobClient(pathFile[1]);

        blobClient.download().then((downloadResponse) => {
          if (downloadResponse.contentLength === 0) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).send({ "message": "File not found" });
          }
          downloadResponse.readableStreamBody.pipe(res);
        }).catch((error) => {
          console.error("Error downloading blob:", error);
          return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).send({ "message": "Error downloading file" });
        });
    } else {
        const err = new Error('File not found');
        err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
        return next(err);
    }
};

exports.downloadClientIncorporationFiles = async function (req, res, next) {
    try {
        let incorporation = await CompanyIncorporationModel.findById(req.params.incorporationId);
        let fileToDownload;
        if (!incorporation) {
            const err = new Error('Review not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }

        if (!req.query.relation){
            if (req.query.requestDataId){
                const requestInfo = incorporation.requestInformation.find((requestData) =>
                  requestData && requestData._id.toString() === req.query.requestDataId);
                if (requestInfo){
                    fileToDownload = requestInfo.files.find((f) => f && f.fileId.toString() === req.params.fileId);
                }
            }
            else if (req.query.informationId){
                const returnedInformation = incorporation.clientReturnedInformation.find((clientInfo) =>
                  clientInfo && clientInfo._id.toString() === req.query.informationId);
                if (returnedInformation){
                    fileToDownload = returnedInformation.files.find((f) => f && f.fileId.toString() === req.params.fileId);
                }
            }
            else if (incorporation.files){
                const files = [...incorporation.files.structureChartFiles, ...incorporation.files.passportFiles,
                    ...incorporation.files.addressProofFiles, ...incorporation.files.otherDeclarationFiles];
                fileToDownload= files.find((f) => f && f.fileId.toString() === req.params.fileId);
            }
        }else{
            const relation = incorporation.relations.find((r) => r._id && r._id.toString() === req.query.relation);
            if (relation){
                let relationFiles;
                if (relation.type === "natural") {
                    const identificationFiles = relation.identification.files ? relation.identification.files : [];
                    const pepFiles = relation.pepDetails.files ? relation.pepDetails.files : [];
                    const worldCheckFiles = relation.worldCheck.files ? relation.worldCheck.files : [];
                    const electronicIdFiles =  relation.electronicIdInfo && relation.electronicIdInfo.files ? relation.electronicIdInfo.files : [];
                    relationFiles = [...identificationFiles, ...electronicIdFiles, ...pepFiles, ...worldCheckFiles];
                } else {
                    const detailsFiles = relation.details.files ? relation.details.files : [];
                    const detailsPartnerFiles = relation.detailsPartner.files ? relation.detailsPartner.files : [];
                    const limitedFiles = relation.limitedCompanyDetails.files ? relation.limitedCompanyDetails.files : [];
                    const mutualFundFiles = relation.mutualFundDetails.files ? relation.mutualFundDetails.files : [];
                    const foundationFiles = relation.foundation.files ? relation.foundation.files : [];
                    const worldCheckFiles = relation.worldCheck.files ? relation.worldCheck.files : [];
                    relationFiles = [...detailsFiles, ...detailsPartnerFiles, ...limitedFiles, ...mutualFundFiles, ...foundationFiles,
                        ...worldCheckFiles];
                }

                if (relationFiles){
                   const files = relationFiles.find((f) => f.id === req.query.fileType);
                   if (files && files.uploadFiles){
                       fileToDownload= files.uploadFiles.find((f) => f && f.fileId.toString() === req.params.fileId);
                   }
                }
            }
        }


        if (fileToDownload && fileToDownload.url){
            const containerClient = getContainerClient(process.env.AZURE_STORAGE_CONTAINER_CLIENT_INCORPORATION,
              process.env.AZURE_STORAGE_ACCOUNT,
              process.env.AZURE_STORAGE_ACCESS_KEY);

            const pathFile = fileToDownload.url.split(process.env.AZURE_STORAGE_CONTAINER_CLIENT_INCORPORATION  + "/" );

            const blobClient = containerClient.getBlobClient(pathFile[1]);

            blobClient.download().then((downloadResponse) => {
              if (downloadResponse.contentLength === 0) {
                return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).send({ "message": "File not found" });
              }
              res.set('Content-Type', fileToDownload.mimeType);
              res.set('Content-Disposition: inline; filename="' + fileToDownload.blobName + '"');

              downloadResponse.readableStreamBody.pipe(res);
            }).catch((error) => {
              console.error("Error downloading blob:", error);
              return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).send({ "message": "Error downloading file" });
            });
        }
        else{
            const err = new Error('File not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }

    } catch (e) {
        console.log("error ", e);
        const err = new Error('File not found');
        err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
        return next(err);
    }
};

exports.downloadAllIncorporationFiles = async function (req, res, next) {
    try {
        let incorporation = await CompanyIncorporationModel.findById(req.params.incorporationId);
        if (!incorporation) {
            const err = new Error('Review not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }

        const pathFile = incorporation._id.toString();
        let zip = Archiver('zip', {
            zlib: { level: 9 }
        });

        zip.on('error', function(err) {
            res.status(500).send({error: err.message});
        });

        zip.pipe(res);
        res.attachment(incorporation.name + '-incorporation-files.zip');

        const summaryPdf = await pdfController.generateIncorporationPdf(incorporation, res, next, true);

        if (summaryPdf && summaryPdf.success){
            zip.append(summaryPdf.result, {name: 'Incorporation Summary.pdf'})
        }


        const blobFiles = await listIncorporationBlobFiles(pathFile);
        if (blobFiles.length > 0){
            for (let i = 0; i < blobFiles.length; i++) {
                const filename = blobFiles[i].name.split(pathFile + "/");
                const blob = await getBlobFromAzure(blobFiles[i].name);
                if (blob.success){
                    zip.append(blob.result, { name: filename[1] })
                }
            }
        }
        await zip.finalize(function (err) {
            if (err) {
                throw err;
            }
            res.set('Content-Type', 'application/zip');
            res.set('Content-Disposition', 'attachment; filename=' + 'incorporation-files.zip');
        });

    }catch (e) {
        console.log("error ", e);
        const err = new Error('File not found');
        err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
        return next(err);
    }
};

exports.listIncorporationFiles = listIncorporationBlobFiles;

async function listIncorporationBlobFiles(directory) {
    try {
      const containerClient = getContainerClient(process.env.AZURE_STORAGE_CONTAINER_CLIENT_INCORPORATION,
        process.env.AZURE_STORAGE_ACCOUNT,
        process.env.AZURE_STORAGE_ACCESS_KEY);

      const results = [];

      const response = containerClient.listBlobsFlat({ prefix: directory });

      for await (const blob of response) {
          results.push(blob);
      }

      return results;
    } catch (e) {
      console.log("Couldn't list blobs for container %s", process.env.AZURE_STORAGE_CONTAINER_CLIENT_INCORPORATION + directory);
      console.error(e);
      return [];
    }
}

async function getBlobFromAzure(blobName) {
    try {
      const containerClient = getContainerClient(process.env.AZURE_STORAGE_CONTAINER_CLIENT_INCORPORATION,
        process.env.AZURE_STORAGE_ACCOUNT,
        process.env.AZURE_STORAGE_ACCESS_KEY);
  
      const blobClient = containerClient.getBlobClient(blobName);
  
      const downloadResponse = await blobClient.downloadToBuffer();
  
      return { success:true, result: downloadResponse };
    } catch (e) {
      return { success: false, error: e };
    }
}

exports.downloadMessageFile = async function (req, res, next) {
    try {
        let message = await MessageModel.findById(req.params.messageId);
        let fileToDownload;

        if (message && message.files && message.files.length > 0) {
            fileToDownload = message.files.find((f) => f.fileId === req.params.fileId);
        }

        if(!fileToDownload){
            let sessData = req.session;

            if (sessData && sessData.messageFiles){
                let messageFiles = sessData.messageFiles[req.params.messageId];
                if (messageFiles && messageFiles.length > 0){
                    fileToDownload = messageFiles.find((f) => f.fileId === req.params.fileId);
                }
            }
        }


        if (fileToDownload && fileToDownload.url){
            const pathFile = fileToDownload.url.split(fileToDownload.container  + "/" );

            const containerClient = getContainerClient(fileToDownload.container,
              process.env.AZURE_STORAGE_ACCOUNT,
              process.env.AZURE_STORAGE_ACCESS_KEY);
            
            const blobClient = containerClient.getBlobClient(pathFile[1]);

            blobClient.download().then((downloadResponse) => {
              if (downloadResponse.contentLength === 0) {
                return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).send({ "message": "File not found" });
              }
              res.contentType(fileToDownload.mimeType);
              res.set('Content-Disposition: inline; filename="' + fileToDownload.blobName + '"');
              downloadResponse.readableStreamBody.pipe(res);
            }).catch((error) => {
              console.error("Error downloading blob:", error);
              return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).send({ "message": "Error downloading file" });
            });
        }
        else{
            const err = new Error('File not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }

    } catch (e) {
        console.log("error ", e);
        const err = new Error('File not found');
        err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
        return next(err);
    }
};


/**
 * Download a RFI document from azure container and return to render.
 * Called from download file button in the request information modal.
 * @function downloadEntryRfiFile
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @param {function}  next HTTP request function to continue the request when the process fail.
 * @return file.
 */
exports.downloadEntryRfiFile = async function (req, res, next) {
    try {
        let entry = await EntryModel.findById(req.params.entryId);

        if (!entry){
            const err = new Error('Entry not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }


        let fileToDownload;
        if (req.params.requestId  && validateUUID(req.params.requestId)){
            const requestedInformationList = entry.requested_information?.details || [];
            let rfiInfo = requestedInformationList.find((requestInfo) => requestInfo.id === req.params.requestId);
            if (!rfiInfo){
                const err = new Error('File not found');
                err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
                return next(err);
            }

            fileToDownload = rfiInfo?.length > 0 ? rfiInfo.files.find((f) => f._id && f._id.toString() === req.params.fileId) : null;

            if(!fileToDownload){
                let returnedInformation;
                if (entry.client_returned_information?.details?.length > 0){
                    returnedInformation = entry.client_returned_information.details.find((returnedInfo) =>
                      returnedInfo.request_id === req.params.requestId);
                }

                if (!returnedInformation){
                    const err = new Error('File not found');
                    err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
                    return next(err);
                }

                if (returnedInformation.files && returnedInformation.files.length > 0) {
                    fileToDownload = returnedInformation.files.find((f) => f._id && f._id.toString() === req.params.fileId);
                }
            }


        }else{
            const sessData = req.session;
            if (sessData.requestedTempRFIFiles && sessData.requestedTempRFIFiles[req.params.entryId] &&
              sessData.requestedTempRFIFiles[req.params.entryId].length > 0) {
                fileToDownload = sessData.requestedTempRFIFiles[req.params.entryId].find((f) => f.fileId === req.params.fileId);
            }
        }



        if (fileToDownload && fileToDownload.url) {
            const containerClient = getContainerClient(process.env.AZURE_STORAGE_CONTAINER_SUBSTANCE_UPLOADS,
              process.env.AZURE_STORAGE_ACCOUNT,
              process.env.AZURE_STORAGE_ACCESS_KEY);
            
            const pathFile = fileToDownload.url.split(process.env.AZURE_STORAGE_CONTAINER_SUBSTANCE_UPLOADS  + "/" );
            const blobClient = containerClient.getBlobClient(pathFile[1]);

            blobClient.download().then((downloadResponse) => {
              if (downloadResponse.contentLength === 0) {
                return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).send({ "message": "File not found" });
              }
              res.set('Content-Disposition: inline; filename="' + fileToDownload.blobName + '"');
              downloadResponse.readableStreamBody.pipe(res);
            }).catch((error) => {
              console.error("Error downloading blob:", error);
              return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).send({ "message": "Error downloading file" });
            });
        }
        else{
            const err = new Error('File not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }

    } catch (e) {
        console.log("error ", e);
        const err = new Error('Internal server error');
        err.status = httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR;
        return next(err);
    }
};


/**
 * Download a financial report  RFI document from azure container and return to render.
 * @function downloadFinancialReportRfiFile
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @param {function}  next HTTP request function to continue the request when the process fail.
 * @return file.
 */
exports.downloadFinancialReportRfiFile = async function (req, res, next) {
    try {

        const report = await FinancialReportModel.findById(req.params.reportId);

        if (!report) {
            const err = new Error('Financial Report not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }


        let fileToDownload;
        if (req.params.requestId && validateUUID(req.params.requestId)) {
            const requestedInformationList = report.requestedInformation?.details || [];
            let rfiInfo = requestedInformationList.find((requestInfo) => requestInfo.id === req.params.requestId);
            if (!rfiInfo) {
                const err = new Error('File not found');
                err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
                return next(err);
            }

            fileToDownload = rfiInfo?.length > 0 ? rfiInfo.files.find((f) => f._id && f._id.toString() === req.params.fileId) : null;

            if (!fileToDownload) {
                let returnedInformation;
                if (report.clientResponseInformation?.details?.length > 0) {
                    returnedInformation = report.clientResponseInformation.details.find((returnedInfo) =>
                        returnedInfo.requestId === req.params.requestId);
                }
                if (!returnedInformation) {
                    const err = new Error('File not found');
                    err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
                    return next(err);
                }

                if (returnedInformation.files && returnedInformation.files.length > 0) {
                    fileToDownload = returnedInformation.files.find((f) => f._id && f._id.toString() === req.params.fileId);
                }
            }


        } else {
            const sessData = req.session;
            if (sessData.requestedTempRFIFiles && sessData.requestedTempRFIFiles[req.params.reportId] &&
                sessData.requestedTempRFIFiles[req.params.reportId].length > 0) {
                fileToDownload = sessData.requestedTempRFIFiles[req.params.reportId].find((f) => f.fileId === req.params.fileId);
            }
        }
        
        console.log("file to downloiad ", fileToDownload)

        if (fileToDownload && fileToDownload.url) {
            const containerClient = getContainerClient(process.env.AZURE_STORAGE_CONTAINER_FINANCIAL_REPORTS,
              process.env.AZURE_STORAGE_ACCOUNT,
              process.env.AZURE_STORAGE_ACCESS_KEY);
              
            const pathFile = fileToDownload.url.split(process.env.AZURE_STORAGE_CONTAINER_FINANCIAL_REPORTS + "/");
            const blobClient = containerClient.getBlobClient(pathFile[1]);

            console.log("blobClient ", blobClient);

            blobClient.download().then((downloadResponse) => {
                if (downloadResponse.contentLength === 0) {
                    return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).send({ "message": "File not found" });
                }

                if (fileToDownload.mimeType || fileToDownload.mimetype){
                    res.contentType(fileToDownload.mimeType ? fileToDownload.mimeType : fileToDownload.mimetype);
                }else{
                    res.contentType('application/pdf');
                }
               
                res.set('Content-Disposition: inline; filename="' + fileToDownload.blobName + '"');
                downloadResponse.readableStreamBody.pipe(res);
            }).catch((error) => {
              console.error("Error downloading blob:", error);
              return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).send({ "message": "Error downloading file" });
            });
        }
        else {
            const err = new Error('File not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }

    } catch (e) {
        console.log("error ", e);
        const err = new Error('Internal server error');
        err.status = httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR;
        return next(err);
    }
};


/**
 * Generate Summary PDF for financial report
 * @function downloadAllFinancialReportFiles
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return PDF file attached in the response
 */
exports.downloadAllFinancialReportFiles = async function (req, res, next) {
    try {
        const report = await FinancialReportModel.findById(req.params.reportId);

        if (!report) {
            const err = new Error('Financial Report not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }

        const containerClient = getContainerClient(
          process.env.AZURE_STORAGE_CONTAINER_FINANCIAL_REPORTS,
          process.env.AZURE_STORAGE_ACCOUNT,
          process.env.AZURE_STORAGE_ACCESS_KEY
        );

        const archive = Archiver("zip", {
            zlib: { level: 9 },
        });
        const pathFile = report._id.toString();
        let files = [];
        if (report.files) {
            files = [
                ...report.files.exemptEvidenceFiles,
                ...report.files.copyResolutionFiles,
                ...report.files.accountingRecordFiles,
                ...report.files.annualReturnFiles,
            ];
        }

        const fileNames =[];
        for (let file of files) {

            const blobClient = containerClient.getBlobClient(`${pathFile}/${file.blobName}`);
            let stream = (await blobClient.download()).readableStreamBody;

            let filename = "";
            if(!fileNames.includes(file.originalName)){
                filename = file.originalName;
            }else{
                const nameRepeatedCount = fileNames.filter((f) => f === file.originalName);
                filename = `${nameRepeatedCount.length+1}-${file.originalName}`
            }
            fileNames.push(file.originalName);
            archive.append(stream, {
                name: `${filename}`,
            });

        }

        archive.on("error", function (err) {
            throw err;
        });

        archive.pipe(res);
        res.attachment(report.companyData.name + '-report-files.zip');

        archive.finalize(function (err) {
            if (err) {
                throw err;
            }
            res.set('Content-Type', 'application/zip');
            res.set('Content-Disposition', `attachment; filename='${report.companyData.name} -report-files.zip'`);
        });
    } catch (e) {
        console.log(e);
        next(e);
    }

};
