const {
    filereview: FileReviewModel,
} = require('../../models/filereview');
const NaturalPersonModel = require('../../models/naturalperson');
const ClientReviewModel = require('../../models/client');
const relationController = require('../relations/relationController');
const OrganizationModel = require('../../models/organization');
const excel = require("node-excel-export");

exports.getFileObserverList = async function (req, res) {
    try {
        let fileReviewFilter = "dummyfilter";
        let fileReviews;

        if (req.body.search_filter && req.body.search_filter.length > 2) {
            fileReviews = await FileReviewModel.find({
                "status.code": {"$ne": 'NOT STARTED'},

                $or: [{ 'companyName': { $regex: req.body.search_filter, $options: 'i' } },
                { 'companyCode': { $regex: req.body.search_filter, $options: 'i' } },
                { 'masterClientCode': { $regex: req.body.search_filter, $options: 'i' } }]

            }, {
                'companyName': 1, 'companyCode': 1, 'masterClientCode': 1, 'status': 1, '_id': 1, 'files': 1,
                'fileReview': 1
            }).sort('companyName').limit(1000);
        } else {
            fileReviews = await FileReviewModel.find({
                "status.code": { $regex: fileReviewFilter, $options: "i" },
            }, {
                'companyName': 1, 'companyCode': 1, 'masterClientCode': 1, 'status': 1, '_id': 1, 'files': 1,
                'fileReview': 1
            }).sort('companyName').limit(100);
        }



        res.render('file-reviewer/file-observer/file-observer-list',
            {
                user: req.session.user,
                title: "File Observer",
                fileReviews: fileReviews
            });

    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};

exports.openFileReview = async function (req, res) {
    try {
        let fileReview = await FileReviewModel.findById(req.params.companyId);
        const clientReview = await ClientReviewModel.findById(fileReview.clientId);
        let standardFileQuestions = fileReview.files;
        let freeQuestions = clientReview.freeQuestions;
        let recordKeepingQuestions = clientReview.recordDetails;
        let companyActivity = clientReview.companyActivity;
        let companyActivityReview = fileReview.companyActivityReview;
        // check if there is any pep inside the file review
        let pep = false;
        if (clientReview.naturalRelations.length) {
            pep = await NaturalPersonModel.countDocuments({ fileReview: req.params.companyId, pep: true }) > 0;
        }

        res.render('file-reviewer/file-observer/file-observer-review', {
            user: req.session.user,
            title: 'Company Files: ' + fileReview.companyName,
            id: fileReview._id,
            file: fileReview,
            standardFileQuestions: standardFileQuestions,
            freeQuestions: freeQuestions,
            recordKeepingQuestions: recordKeepingQuestions,
            pep: pep,
            companyActivity: companyActivity,
            companyActivityReview: companyActivityReview,
        });

    } catch (error) {
        console.log(error);
        res.redirect('/file-reviewer/file-observer-list');
    }

};

exports.getRelations = async function (req, res) {
    try {
        let relationsData = await relationController.getReviewRelations(req.params.companyId);


        if (relationsData) {
            res.render('file-reviewer/file-observer/file-observer-relations-list', {
                user: req.session.user,
                file: relationsData.file,
                title: relationsData.file.companyName + ': Beneficial Owners',
                id: req.params.companyId,
                beneficialOwners: relationsData.beneficialOwners,
                shareholders: relationsData.shareholders,
                directors: relationsData.directors
            });
        } else {
            res.redirect('/file-reviewer/dashboard');
        }
    } catch (error) {
        console.log(error);
        res.redirect('/');
    }
};

exports.getFilesByReview = async function (req, res) {
    try {
        let review;
        let clientReview;
        let filesToReturn;
        let uploadFiles;
        if (req.query.type === "standard") {
            review = await FileReviewModel.findById(req.params.companyId);
            clientReview = await ClientReviewModel.findById(review.clientId);
            const file = review.files.find((file) => file.id.toString() === req.query.fileId);
            uploadFiles = file.uploadFiles;
            filesToReturn = clientReview.files.filter((file) => uploadFiles.includes(file.fileId));
        } else if (req.query.type === "natural") {
            if (req.query.fileId) {
                review = await NaturalPersonModel.findById(req.query.fileId);
            }
        } else {
            if (req.query.fileId) {
                review = await OrganizationModel.findById(req.query.fileId);
            }
        }
        if (!review) {
            return res.status(500).end();
        }
        return res.status(200).json({ success: true, files: filesToReturn });

    } catch (e) {
        console.log(e);
    }
};

exports.exportReviews = async function (req, res) {
    try {
        const documentsData = (await FileReviewModel.find({}, { _id: 1, companyCode: 1, companyName: 1, createdAt: 1 }))
            .map((review) => {
                review.uniqueUrl = `${process.env.AZURE_APP_RETURN_URL}/file-reviewer/file-observer-review/${review._id}`;
                return review;
            })

        const styles = {
            headerTable: {
                fill: {
                    fgColor: {
                        rgb: "ffffff",
                    },
                },
                border: {
                    top: { style: "thin", color: "000000" },
                    bottom: { style: "thin", color: "000000" },
                    left: { style: "thin", color: "000000" },
                    right: { style: "thin", color: "000000" },
                },
                font: {
                    color: {
                        rgb: "000000",
                    },
                    sz: 12,
                    bold: false,
                    underline: false,
                },
            },
        };

        const specification = {
            companyCode: {
                displayName: "Code",
                headerStyle: styles.headerTable,
                width: 120,
            },
            companyName: {
                displayName: "Company Name",
                headerStyle: styles.headerTable,
                width: 200,

            },
            createdAt: {
                displayName: "Created at",
                headerStyle: styles.headerTable,
                width: 120,
            },
            uniqueUrl: {
                displayName: "View",
                headerStyle: styles.headerTable,
                width: 700,
            },
        };
        const report = excel.buildExport([
            {
                name: "FileReviews",
                specification: specification,
                data: documentsData,
            },
        ]);

        res.attachment("FileReviews.xlsx");
        return res.send(report);
    } catch (error) {
        console.log(error);
    }
};


