const {filereview: FileReviewModel} = require('../../models/filereview');
const ClientReviewModel = require('../../models/client');
const ConfigModel = require('../../models/config');
const NaturalPersonModel = require('../../models/naturalperson');
const OrganizationModel = require('../../models/organization');
const RequestedFileModel = require('../../models/requestedFile');
const MessageModel = require("../../models/message");
const MailController = require('../mailController');
const MailFormatter = require('../mailFormatController');
const IdPalController = require('../idPalController');
const MessageController = require('../client-management/messageController');
const CompanyModel = require('../../models/company');
const relationController = require('../relations/relationController');
const utils = require('../../utils/utils');
const ObjectId = require('mongoose').Types.ObjectId;
const {v4: uuidv4} = require('uuid');
const moment = require("moment");


/* --------------
  NOTE: WHEN SESSION IS IMPLEMENTED, CHANGE EVERY "<EMAIL>" FOR
  THE CURRENT USERNAME
  --------------- */

// Randomly select files for File Review Officer
function randomDispatcher(fileList) {
    // Before calling this function, verify mongo query with { file_reviewer: { username: <username> } } has no results
    let max = fileList.length;
    let fileListKeys = Object.keys(fileList);
    let key = fileListKeys[Math.floor(Math.random() * max)];
    return fileList[key];
}

function clearReviewValidations(review, sendTo) {

    const naturalSections = ["details", "identification", "residentialAddress", "mailingAddress", "taxResidence", "advisorDetails",
        "principalAddress", "pepDetails", "worldCheck"];

    const orgSections = ["details", "detailsPartner", "principalAddress", "mailingAddress", "listedCompanyDetails",
        "limitedCompanyDetails", "mutualFundDetails", "foundation", "worldCheck"];

    review.companyActivityReview.validated = false;
    if (review.files) {
        review.files.forEach((file) => {
            file.validated = false;
        });
    }

    review.markModified("files");
    const groups = ["beneficialOwners", "shareholders", "directors"];

    for (let group of groups) {
        if (review[group] && review[group].natural) {
            review[group].natural.forEach((relation) => {
                for (let section of naturalSections) {
                    if (relation[section]) {
                        if (sendTo === "sendOfficer") {
                            relation[section]["complete"] = false;
                        }
                        relation[section]["validated"] = false;

                    }
                }

                if (relation["files"]) {
                    relation["files"].forEach((file) => {
                        file["validated"] = false;
                    })
                }
            });
        }

        if (review[group] && review[group].organization) {
            review[group].organization.forEach((relation) => {
                for (let section of orgSections) {
                    if (relation[section]) {
                        if (sendTo === "sendOfficer") {
                            relation[section]["complete"] = false;
                        }
                        relation[section]["validated"] = false;
                    }
                }

                if (relation["files"]) {
                    relation["files"].forEach((file) => {
                        file["validated"] = false;
                    })
                }
            });
        }

    }

    if (review.positions) {
        review.positions.forEach((position) => {
            for (let section of Object.keys(position)) {
                if (section in naturalSections) {
                    if (sendTo === "sendOfficer") {
                        position[section]["complete"] = false;
                    }
                    position[section]["validated"] = false;
                }
                if (section === "files") {
                    position[section].forEach((file) => {
                        file["validated"] = false;
                    })
                }

            }
        })
    }

    return review
}


async function saveClientInformation(clientId, form) {
    // reset values before saving new ones
    try {
        let clientReview = await ClientReviewModel.findById(clientId);

        if (clientReview) {
            clientReview.recordDetails = {...clientReview.recordDetails, ...form.records};
            clientReview.freeQuestions = form.freeQuestions;
            await clientReview.save();
        }

    } catch (e) {
        console.log(e);
    }
}

async function createFileReview(req, riskGroup) {
    try {
        let company;
        let companies = [];
        let pendingFileReview = true;
        const configTemplate = await ConfigModel.findOne({});

        companies = await CompanyModel.schema.find({
            'riskgroup': {$in: riskGroup}, 'filereviews': {$in: [null, []]}});
        if(companies.length > 0){
            companies.sort((a,b) => parseInt(b.code) - parseInt(a.code));
        }

        const currentDate = new Date();
        const standardFiles = [];
        configTemplate.nonManagedFiles.forEach((fileManaged) => {
            const file = {
                id: uuidv4(),
                internal: fileManaged.internal,
                external: fileManaged.external,
                fileGroup: fileManaged.fileGroup,
                present: false,
                explanation: '',
                comments: '',
                validated: false,
                uploadFiles: []
            };
            standardFiles.push(file);
        });

        let newFileReview;
        let companyIndex = 0;

        while (pendingFileReview){
            if (companies.length > 0 && companyIndex <= companies.length){
                company = companies[companyIndex];
                companyIndex++;
            }
            console.log("Company selected to new review: ", company);
            if (company){
                newFileReview = new FileReviewModel({
                    masterClientCode: company.masterclientcode,
                    companyCode: company.code,
                    companyName: company.name,
                    companyType: company.company_type,
                    riskGroup: company.riskgroup,
                    correctType: true,
                    status: {
                        code: "NOT STARTED",
                        statusDate: currentDate
                    },
                    fileReview: {},
                    qualityAssurance: {},
                    beneficialOwners: [],
                    shareholders: [],
                    directors: [],
                    files: standardFiles,
                    updatedAt: currentDate,
                    createdAt: currentDate,
                    partitionkey: 'filereview'
                });

                let existsReview = await FileReviewModel.findOne(
                  {masterClientCode: company.masterclientcode, companyCode: company.code },
                  {_id: 1, companyCode: 1});

                console.log("Company already has review? : ", existsReview ? existsReview._id : false);

                if (!existsReview){
                    // Create Filereview if not exists existing review
                    await newFileReview.save();
                    company.filereviews.push(newFileReview._id);
                    company.markModified('filereviews');
                    company.modifiedBy = req.session.user.username;
                    await company.save();
                    pendingFileReview = false;
                }
                else{
                    // Add existing filereview _id  reference to company
                    await CompanyModel.schema.findByIdAndUpdate(company._id, {
                        $addToSet: { filereviews: existsReview._id },
                        modifiedBy: req.session.user.username
                    });
                    company = null;
                    newFileReview = null;
                }
            }
            else{
                // Break while if not exists company to create new review
                break;
            }
        }

        return {
            status: 200,
            message: newFileReview ?
              'New File Review created for company ' + company.code + " at " +
              moment().format('YYYY-MM-DD HH:mm') :
              'Not found companies to create File Review ',
            fileReview: newFileReview
        };


    } catch (e) {
        console.log("ERROR - dispatching new File Review: ", e);
        return {
            status: 500,
            message: 'Error generating a new File Review',
        };
    }
}

async function createFileReviewByCompanyCode(req, riskGroup, findCompany) {
    try {
        let company;
        const configTemplate = await ConfigModel.findOne({});

        company = await CompanyModel.schema.findOne({code: findCompany, riskgroup: {$in: riskGroup}});

        const currentDate = new Date();
        const standardFiles = [];
        configTemplate.nonManagedFiles.forEach((fileManaged) => {
            const file = {
                id: uuidv4(),
                internal: fileManaged.internal,
                external: fileManaged.external,
                fileGroup: fileManaged.fileGroup,
                present: false,
                explanation: '',
                comments: '',
                validated: false,
                uploadFiles: []
            };
            standardFiles.push(file);
        });

        let newFileReview = new FileReviewModel({
            masterClientCode: company.masterclientcode,
            companyCode: company.code,
            companyName: company.name,
            companyType: company.company_type,
            riskGroup: company.riskgroup,
            correctType: true,
            status: {
                code: "NOT STARTED",
                statusDate: currentDate
            },
            fileReview: {},
            qualityAssurance: {},
            beneficialOwners: [],
            shareholders: [],
            directors: [],
            files: standardFiles,
            updatedAt: currentDate,
            createdAt: currentDate,
            partitionkey: 'filereview'
        });

        let existsReview = await FileReviewModel.findOne(
          {masterClientCode: company.masterclientcode, companyCode: company.code },
          {_id: 1, companyCode: 1});

        if (!existsReview){
            await newFileReview.save();
            company.filereviews.push(newFileReview._id);
            company.markModified('filereviews');
            company.modifiedBy = req.session.user.username;
            await company.save();

            return {
                status: 200,
                message: "New Filereview created successfully for company " + company.code,
                fileReview: newFileReview
            }
        }
        else{
            // Add existing filereview _id  reference to company
            await CompanyModel.schema.findByIdAndUpdate(company._id,{
                $addToSet: { filereviews: existsReview._id },
                modifiedBy: req.session.user.username
            });
            return {
                status: 400,
                message: "Already exists Filereview created for company " + company.code,
            }
        }
    } catch (e) {
        console.log("Error creating filereview for company code: ", e);
        return {
            status: 500,
            message: 'Error generating a new filereview for company code ' + findCompany,
        };
    }
}

exports.getDashboard = function (req, res) {

    res.render('file-reviewer/dashboard',
        {
            user: req.session.user,
            title: "File Review Dashboard",
            authentication: req.session.authentication,
            showStatistics: req.session.authentication.isFileReviewer ||
                req.session.authentication.isQualityAssurance || req.session.authentication.isCompliance

        });
};

//Controller function for rendering the file reviewer dashboard
exports.getFileReviewList = async function (req, res) {
    try {
        let riskGroup = utils.getUserRiskGroup(req.session);
        const limitToPick = process.env.LIMIT_FILE_REVIEWS_ASSIGNED || 1;
        let assignedAuto = [];
        let assignedByOfficer = [];
        let onHold = [];
        let cantPick = false;

        // FETCH FILES AND FILTER BASED ON CONSTRAINTS
        // the company_type is based on the file reviewer, needs to change
        let filesForUser = await FileReviewModel.find({
            'fileReview.username': req.session.user.username.toLowerCase(), // TODO CHANGE TO SESSION EMAIL
            correctType: true,
            riskGroup: {$in: riskGroup}
        });

        assignedAuto = filesForUser.filter((file) => {
            return file.status.code === 'ASSIGNED' &&(!file.fileReview.assignedBy.username ||
              (file.fileReview.assignedBy.username && !file.fileReview.assignedBy.username.toLowerCase !== req.session.user.username.toLowerCase()));
        });
        // IF THERE ARE NO AUTO ASSIGNED FILES, ASSIGN ONE RANDOMLY

        if (assignedAuto.length === 0) {
            let selectedAuto;
            let createdReview;
            const currentDate = new Date();
            console.log("CREATING A NEW FILE REVIEW BY " + req.session.user.username +
                " AT " + currentDate.toString());
            createdReview = await createFileReview(req, riskGroup);

            console.log("NEW FILE REVIEW RESPONSE STATUS ", createdReview.status);
            console.log("NEW FILE REVIEW RESPONSE ", createdReview.message);
            if (createdReview && createdReview.status !== 200){
                return res.render('error', { status: 500, message: "Internal server error" });
            }

            if (createdReview && createdReview.status === 200 && createdReview.fileReview) {
                console.log("NEW FILE REVIEW: ", createdReview.fileReview._id);
                selectedAuto = createdReview.fileReview;
            } else {
                console.log("FILE REVIEW NOT CREATED");
                let filesNotStarted = await FileReviewModel.find({
                    correctType: true, riskGroup: {$in: riskGroup}, "status.code": 'NOT STARTED'
                }).sort('createdAt').limit(100);
                selectedAuto = filesNotStarted ? randomDispatcher(filesNotStarted) : null;
            }
            if (selectedAuto) {
                assignedAuto.push(selectedAuto);
                selectedAuto.status.code = 'ASSIGNED';
                selectedAuto.fileReview = {
                    username: req.session.user.username.toLowerCase(),
                    name: req.session.user.name,
                    dateAssigned: new Date(),
                };
                await selectedAuto.save();
                let fileIndex = filesForUser.findIndex(file => file._id.toString() === selectedAuto._id.toString());
                if (fileIndex !== -1) {
                    filesForUser[fileIndex] = selectedAuto;
                } else {
                    filesForUser.push(selectedAuto);
                }
            }
        }

        // CHECKS IF THE FILE REVIEWER CAN PICK MORE FILES MANUALLY
        cantPick = assignedAuto.length >= limitToPick;

        assignedByOfficer = filesForUser.filter((file) => {
            return file.status.code === 'SEND TO FILE REVIEW OFFICER BY QA' ||
                file.status.code === 'SEND TO FILE REVIEW OFFICER BY CO' || (file.status.code === 'ASSIGNED' &&
                    file.fileReview.assignedBy.username &&
                    file.fileReview.assignedBy.username.toLowerCase() !== req.session.user.username.toLowerCase());
        });

        assignedByOfficer.map((review) => {
            if (review.status.code === 'SEND TO FILE REVIEW OFFICER BY QA' || review.status.code === 'SEND TO FILE REVIEW OFFICER BY CO') {
                review.status.code = 'ASSIGNED';
            }
        });
        onHold = filesForUser.filter((file) => file.status.code === 'ON HOLD' ||  file.status.code === 'SENT TO CLIENT');

        if(onHold.length > 0) {
            for (let i = 0; i < onHold.length; i++) {
                if (onHold[i].pendingElectronicIds){
                    const electronicIds = onHold[i].pendingElectronicIds;

                    const idsCompleted = electronicIds.filter((e) => e.complete === true);

                    if (idsCompleted.length === electronicIds.length){
                        onHold[i].electronicRequests = "COMPLETED"
                    }
                    else if (idsCompleted.length === 0){
                        onHold[i].electronicRequests = "PENDING ALL"
                    }
                    else if (electronicIds.length - idsCompleted.length >= 1){
                        onHold[i].electronicRequests = "PENDING AT LEAST ONE"
                    }


                }
            }
        }


        req.session.submissions = {
            onHold: onHold,
            assignedByOfficer: assignedByOfficer,
            assignedAuto: assignedAuto,
            allFiles: [],
            cantPick: cantPick
        };
        res.render('file-reviewer/file-review-list', {
            user: req.session.user,
            title: 'Dashboard',
            assignedAuto,
            assignedByOfficer,
            onHold,
            allFiles: [],
            cantPick,
            riskGroup,
        });
    } catch (error) {
        console.log(error);
        res.redirect('/');
    }
};

exports.searchFileReview = async function (req, res) {
    try {
        const limitToPick = process.env.LIMIT_FILE_REVIEWS_ASSIGNED || 1;
        let riskGroup = utils.getUserRiskGroup(req.session);
        let fileReviewFilter = '';
        let companiesReview = [];
        let allFiles = [];
        if (req.body.filter_filereview && req.body.filter_filereview.length > 2) {
            fileReviewFilter = req.body.filter_filereview;

            companiesReview = await CompanyModel.schema.aggregate([
                {
                    "$match":
                        {
                            riskgroup: {$in: riskGroup},
                            $or: [{'name': {$regex: fileReviewFilter, $options: 'i'}},
                                {'code': {$regex: fileReviewFilter, $options: 'i'}},
                                {'masterclientcode': {$regex: fileReviewFilter, $options: 'i'}},
                                {'incorporationcode': {$regex: fileReviewFilter, $options: 'i'}}]
                        },
                },
                {
                    "$lookup": {
                        "from": "filereviews",
                        "localField": "code",
                        "foreignField": "companyCode",
                        "as": "fileReviews"
                    }
                },
                {"$limit": 1000}
            ]);
            if (companiesReview.length) {
                allFiles = companiesReview.map((review) => {
                    let dateAssigned = '';
                    let reviewerName = '';
                    let reviewerUserName = '';
                    let status = {
                        code: "NOT STARTED",
                        statusDate: new Date(),
                    };
                    if (review.fileReviews.length && review.fileReviews[0].fileReview) {
                        dateAssigned = review.fileReviews[0].fileReview.dateAssigned;
                        reviewerName = review.fileReviews[0].fileReview.name;
                        reviewerUserName = review.fileReviews[0].fileReview.username ?  review.fileReviews[0].fileReview.username.toLowerCase() : '';

                        if (review.fileReviews[0].status.code && review.fileReviews[0].status.code.toUpperCase() === "SUBMITTED") {
                            status = {
                                code: "NOT STARTED",
                                statusDate: review.fileReviews[0].status.statusDate,
                            };
                        } else {
                            status = review.fileReviews[0].status;
                        }
                    }

                    return {
                        _id: review.fileReviews.length ? review.fileReviews[0]._id : "company" + review.code,
                        companyName: review.name,
                        companyCode: review.code,
                        riskGroup: review.riskgroup,
                        fileReview: {
                            dateAssigned: dateAssigned,
                            name: reviewerName,
                            username: reviewerUserName
                        },
                        status: status,
                    }
                });
            }

            const fileReviewsResponse = await FileReviewModel.find({
                companyType: 'non managed', riskGroup: {$in: riskGroup},
                $or: [{'companyName': {$regex: fileReviewFilter, $options: 'i'}},
                    {'companyCode': {$regex: fileReviewFilter, $options: 'i'}},
                    {'masterClientCode': {$regex: fileReviewFilter, $options: 'i'}}]
            }).sort('createdAt').limit(100);

            if (fileReviewsResponse.length) {
                fileReviewsResponse.forEach((review) => {
                    const fileIndex = allFiles.findIndex(file => file._id.toString() === review._id.toString());
                    if (fileIndex !== -1) {
                        allFiles[fileIndex] = review;
                    } else {
                        allFiles.push(review);
                    }
                });
            }
        }


        allFiles.map((file) => {
            if (file.fileReview.username) {
                if (file.fileReview.username.toLowerCase() !== req.session.user.username.toLowerCase() &&
                    (file.status.code.toUpperCase() === 'ASSIGNED' || file.status.code.toUpperCase() === 'ON HOLD')) {
                    file.status.code = 'IN PROGRESS';
                }
            }
        });
        const cantPick = req.session.submissions.assignedAuto.length >= limitToPick;
        req.session.submissions.cantPick = cantPick;

        res.render('file-reviewer/file-review-list', {
            user: req.session.user,
            title: 'Dashboard',
            assignedAuto: req.session.submissions && req.session.submissions.assignedAuto ?
              req.session.submissions.assignedAuto: [],
            assignedByOfficer: req.session.submissions && req.session.submissions.assignedByOfficer ?
              req.session.submissions.assignedByOfficer: [],
            onHold: req.session.submissions && req.session.submissions.onHold ?
              req.session.submissions.onHold: [],
            allFiles,
            cantPick:  cantPick,
            riskGroup,
        });

    } catch (e) {
        console.log(e);
        res.redirect('/');
    }
};

// Controller function for ajax call to pick a submitted file
exports.pickFileReview = async function (req, res) {
    try {
        let riskGroup = utils.getUserRiskGroup(req.session);
        let file = {};
        if (req.session.submissions.cantPick === false) {
            if (req.body.id.includes('company')) {
                const companyCode = req.body.id.replace('company', '');
                const currentDate = new Date;
                console.log("CREATING A NEW FILE REVIEW BY " + req.session.user.username + " AT " +
                    currentDate.toString());
                let createdReviewResponse = await createFileReviewByCompanyCode(req, riskGroup, companyCode);

                if (createdReviewResponse && createdReviewResponse.status !== 200){
                    console.log("ERROR PICKING FILE REVIEW: ", createdReviewResponse.message);
                    return res.status( createdReviewResponse.status).end();
                }else{
                    console.log("NEW FILE REVIEW: ", createdReviewResponse.fileReview._id);
                    file = createdReviewResponse.fileReview;
                }

            } else {
                file = await FileReviewModel.findById(req.body.id);
            }

            if (file){
                file.status = {
                    code: 'ASSIGNED',
                    statusDate: new Date(),
                };
                file.fileReview = {
                    assignedBy: {
                        username: req.session.user.username,
                        role: 'file reviewer',
                    },
                    username: req.session.user.username,
                    name: req.session.user.name,
                    dateAssigned: new Date(),
                };
                await file.save();
                req.session.submissions.assignedAuto.push(file);
                return res.status(200).end();
            }
            else{
                return res.status(400).end();
            }

        } else {
            return res.status(400).end();
        }
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};

// Controller function for ajax call to pick a submitted file
exports.getReview = async function (req, res) {
    try {
        let review = await FileReviewModel.findById(req.params.reviewId, "_id companyName companyCode fileReview status clientId");
        if (review) {
            return res.status(200).json({"success": true, "review": review});
        } else {
            return res.status(404).json({"success": false});
        }
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};

exports.deleteReview = async function (req, res) {
    try {
        let review = await FileReviewModel.findById(req.params.reviewId);

        if (!review){
            return res.status(404).json({"status": 404, "message": "Not Found"});
        }

        await NaturalPersonModel.updateMany({lockedByFileReview: review._id}, {lockedByFileReview: null});
        await OrganizationModel.updateMany({lockedByFileReview: review._id}, {lockedByFileReview: null});

        const company = await CompanyModel.schema.findOne({code: review.companyCode});

        if (company && company.filereviews){
            const reviewIndex = company.filereviews.findIndex((filereview) =>
              filereview.toString() === review._id.toString());

            if (reviewIndex > -1){
                company.filereviews.splice(reviewIndex, 1);
                await company.save();
            }
        }

        await review.delete();
        return res.status(200).json({"status": 200, "message": "Success"});
    } catch (error) {
        console.log(error);
        return res.status(500).json({"status": 500, "message": "Internal Server Error"});
    }
};

// Controller function for sending an email notification a file reviewer
exports.sendFileReviewNotification = async function (req, res) {
    try {
        let companyId = req.body.companyCode;
        let companyName = req.body.companyName;
        let nameToNotify = req.body.nameToNotify;
        let emailToNotify = req.body.emailToNotify;
        let emailText = req.body.emailText;
        let email = MailFormatter.generateEmail(companyId, companyName, nameToNotify, emailText);
        let sentEmailResult = await MailController.asyncSend(
          emailToNotify,
            'TBVI File Review',
            email.textString,
            email.htmlString
        );
        if (sentEmailResult.accepted) {
            return res.status(200).end();
        } else {
            return res.status(500).end();
        }
    } catch (error) {
        return res.status(500).end();
    }
};

// Controller function for rendering the type validation of the file
exports.verifyFileType = async function (req, res) {
    try {
        let file = await FileReviewModel.findById(req.params.companyId);
        if (file.fileReview.username.toLowerCase() === req.session.user.username.toLowerCase()) {
            res.render('file-reviewer/confirm-type', {
                user: req.session.user,
                title: 'Verify File Type: ' + file.companyName,
                id: file._id,
                file,
            });
        } else {
            res.redirect('/file-reviewer/file-review-list');
        }
    } catch (error) {
        res.redirect('/file-reviewer/file-review-list');
    }
};

exports.changeFileType = async function (req, res) {
    try {
        let file = await FileReviewModel.findById(req.params.companyId);
        if (req.body.optTypeCorrect === 'yes') {
            file.correctType = true;
            if (!file.clientId) {
                let createdClient = await ClientReviewModel.findOne({companyCode: file.companyCode});

                if (!createdClient) {
                    // it's not initialized
                    const newReviewClient = new ClientReviewModel({
                        type: file.companyType,
                        companyCode: file.companyCode,
                        companyName: file.companyName,
                        recordDetails: {
                            recordHolder: '',
                            primaryAddress: '',
                            secondaryAddress: '',
                            state: '',
                            city: '',
                            postalCode: '',
                            email: '',
                            operationCountry: '',
                        },
                        files: [],
                        freeQuestions: [],
                        naturalRelations: [],
                        organizationRelations: [],
                        createdAt: new Date(),
                        updatedAt: new Date(),
                        partitionkey: 'client'
                    });
                    createdClient = await newReviewClient.save();
                }
                file.clientId = createdClient._id;
            }
            await file.save();
            res.redirect('/file-reviewer/open-file-review/' + req.params.companyId);
        } else {
            // if there is a new type (pep, managed, other)
            if (req.body.newType) {
                // change file attrs and save to DB
                const updateFields = {
                    companyType: req.body.newType,
                    correctType: false,
                    status: {
                        code: 'SUBMITTED',
                        statusDate: new Date()
                    }
                };
                file = {...file, ...updateFields};
                await file.save();
                res.redirect('/file-reviewer/file-review-list');
            } else {
                res.redirect('/file-reviewer/file-review-list');
            }
        }
    } catch (error) {
        console.log(error);
        res.redirect('/file-reviewer/file-review-list');
    }
};

// Controller function for rendering the file review of a specific file
exports.openFileReview = async function (req, res) {
    try {
        let file = await FileReviewModel.findById(req.params.companyId);
        if (file.fileReview.username.toLowerCase() === req.session.user.username.toLowerCase()) {
            const clientReview = await ClientReviewModel.findById(file.clientId);
            let standardFileQuestions = file.files;
            let freeQuestions = clientReview.freeQuestions;
            let recordKeepingQuestions = clientReview.recordDetails;
            let companyActivity = clientReview.companyActivity;
            let companyActivityReview = file.companyActivityReview;
            // check if there is any pep inside the file review
            let pep = false;
            if (clientReview.naturalRelations.length) {
                pep = await NaturalPersonModel.countDocuments({fileReview: req.params.companyId, pep: true}) > 0;

            }

            res.render('file-reviewer/file-review', {
                user: req.session.user,
                title: 'Company Files: ' + file.companyName,
                id: file._id,
                file: file,
                standardFileQuestions: standardFileQuestions,
                freeQuestions: freeQuestions,
                recordKeepingQuestions: recordKeepingQuestions,
                pep: pep,
                companyActivity: companyActivity,
                status: file.status.code,
                companyActivityReview: companyActivityReview,
            });
        } else {
            res.redirect('/file-reviewer/file-review-list');
        }
    } catch (error) {
        res.redirect('/file-reviewer/file-review-list');
    }
};

exports.getUploadedFiles = async function (req, res) {
    try {
        let data;
        let review;
        let filesToReturn = [];

        if (req.query.type === "standard") {
            review = await FileReviewModel.findById(req.query.reviewId);

            const dataIndex = review.files.findIndex((file) => file.id === req.query.fileId);

            if (dataIndex !== -1) {
                data = review.files[dataIndex];

                if (data.uploadFiles.length === 0) {
                    data.present = false;
                    review.files.set(dataIndex, data);
                    await review.save();
                } else {
                    const clientReview = await ClientReviewModel.findById(review.clientId);

                    if (clientReview && clientReview.files.length > 0) {
                        filesToReturn = clientReview.files.filter((file) => file.fileTypeId === req.query.fileId)
                    }
                }
            }
        } else if (req.query.type === "natural") {
            if (req.query.reviewId) {
                review = await NaturalPersonModel.findById(req.query.reviewId);
                if (review) {
                    let naturalFiles = [];
                    const identificationFiles = review.identification.files ? review.identification.files : [];
                    const pepFiles = review.pepDetails.files ? review.pepDetails.files : [];
                    const worldCheckFiles = review.worldCheck.files ? review.worldCheck.files : [];
                    const electronicIdFiles = review.electronicIdInfo && review.electronicIdInfo.files ? review.electronicIdInfo.files : [];
                    naturalFiles = [...identificationFiles, ...pepFiles, ...worldCheckFiles, ...electronicIdFiles];
                    naturalFiles = naturalFiles.find((file) => file.id === req.query.fileId);
                    filesToReturn = naturalFiles && naturalFiles.uploadFiles ? naturalFiles.uploadFiles : [];
                }
            }
        } else {
            if (req.query.reviewId) {
                review = await OrganizationModel.findById(req.query.reviewId);
                if (review) {
                    let organizationFiles = [];
                    const detailsFiles = review.details.files ? review.details.files : [];
                    const detailsPartnerFiles = review.detailsPartner.files ? review.detailsPartner.files : [];
                    const limitedFiles = review.limitedCompanyDetails.files ? review.limitedCompanyDetails.files : [];
                    const mutualFundFiles = review.mutualFundDetails.files ? review.mutualFundDetails.files : [];
                    const foundationFiles = review.foundation.files ? review.foundation.files : [];
                    const worldCheckFiles = review.worldCheck.files ? review.worldCheck.files : [];
                    organizationFiles = [...detailsFiles, ...detailsPartnerFiles, ...limitedFiles, ...mutualFundFiles, ...foundationFiles,
                        ...worldCheckFiles];

                    organizationFiles = organizationFiles.find((file) => file.id === req.query.fileId);
                    filesToReturn = organizationFiles && organizationFiles.uploadFiles ? organizationFiles.uploadFiles : [];
                }
            }

        }
        if (req.query.getTempFiles) {
            const tempFilesGroup = relationController.getTempUploadFiles(req.session, req.query.fileGroup);
            if (tempFilesGroup) {
                const tempFiles = tempFilesGroup[req.query.row] ? tempFilesGroup[req.query.row] : [];
                filesToReturn = [...filesToReturn, ...tempFiles];
            }

        }
        return res.json({success: true, files: filesToReturn});
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};

exports.saveFileReview = async function (req, res) {
    try {
        let review = await FileReviewModel.findById(req.params.companyId);
        const clientReview = await ClientReviewModel.findById(review.clientId);
        if (req.body.page === '1') {
            review.updatedAt = new Date();
            await saveClientInformation(review.clientId, req.body);

            if (req.body.files && req.body.files) {
                const keys = Object.keys(req.body.files);
                for (const key of keys) {
                    const fileIndex = review.files.findIndex((file) => file.id === key);

                    if (fileIndex !== -1) {
                        let updatedFile = review.files[fileIndex];
                        if (updatedFile) {
                            updatedFile.present = !!req.body.files[key].present;
                            updatedFile.explanation = req.body.files[key].explanation ? req.body.files[key].explanation : '';
                            review.files.set(fileIndex, updatedFile);
                        }

                    }
                }
                if (req.body.companyActivity) {
                    review.companyActivityReview.present = !!req.body.companyActivity.present;
                    review.companyActivityReview.explanation = req.body.companyActivity.explanation;
                    clientReview.companyActivity.activity = req.body.companyActivity.select;
                    await clientReview.save();
                }
            }
        }
        if (req.body.btnId === 'onHoldButton') {
            review.remark = req.body['internalTridentRemark'];
            let onHoldAmount = await FileReviewModel.countDocuments({
                'fileReview.username': req.session.user.username.toLowerCase(),
                'status.code': 'ON HOLD',
            });
            if (onHoldAmount < 2) {
                review.status = {
                    code: 'ON HOLD',
                    statusDate: new Date()
                };
            } else {
                return res.status(400).end();
            }
        }
        await review.save();
        return res.status(200).end();
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};


exports.updateFileReview = async function (req, res) {
    try {
        let file = await FileReviewModel.findById(req.body.fileReviewId);
        if (file) {
            for (let [key, value] of Object.entries(req.body.fieldsToUpdate)) {
                file[key] = value;
            }
            await file.save(function (err) {
                if (err) {
                    console.log('error saving')
                }
            });
            return res.status(200).end();
        }
    } catch (error) {
        return res.status(500).end();
    }
};


exports.saveStandardPage = async function (req, res) {
    try {
        let file = await FileReviewModel.findById(req.params.companyId);
        const clientReview = await ClientReviewModel.findById(file.clientId);
        await saveClientInformation(file.clientId, req.body, true);

        if (req.body.files && req.body.files) {
            const keys = Object.keys(req.body.files);
            for (const key of keys) {
                const fileIndex = file.files.findIndex((file) => file.id === key);

                if (fileIndex !== -1) {
                    let updatedFile = file.files[fileIndex];
                    if (updatedFile) {
                        updatedFile.present = !!req.body.files[key].present;
                        updatedFile.explanation = req.body.files[key].explanation ? req.body.files[key].explanation : '';
                        file.files.set(fileIndex, updatedFile);
                    }

                }
            }
        }
        if (req.body.companyActivity) {
            file.companyActivityReview.present = !!req.body.companyActivity.present;
            file.companyActivityReview.explanation = req.body.companyActivity.explanation;
            clientReview.companyActivity.activity = req.body.companyActivity.select;
            await clientReview.save();
        }
        await file.save();
        res.redirect('/file-reviewer/open-file-review/' + req.params.companyId + '/beneficial-owners');
    } catch (error) {
        console.log(error);
        res.redirect('/');
    }
};

exports.getRelations = async function (req, res) {
    try {
        const fileReview = await FileReviewModel.findById(req.params.companyId, {_id:1, status: 1, pendingElectronicIds: 1});
        let hasPendingElectronicInfo = false;
        let relationsData = await relationController.getReviewRelations(req.params.companyId, true);

        let relations = [...relationsData.beneficialOwners, ...relationsData.shareholders,
            ...relationsData.directors];

        if (relations) {
            let naturals = [...new Set(relations.filter(
                (r) => r.type === "natural" && r.lockedByFileReview === null).map((r) => r._id))
            ];
            let organizations = [...new Set(relations.filter(
                (r) => r.type !== "natural" && r.lockedByFileReview === null).map((r) => r._id))
            ];

            if (relationsData.positionList.length) {
                const positions = [...new Set(relationsData.positionList.filter(
                    (p) => p && p.position && p.position.lockedByFileReview === null).map((p) => p.referenceId))
                ];

                if (positions.length) {
                    naturals = [...naturals, ...positions]
                }
            }
            let relIds = [];
            if (naturals) {
                relIds = [...relIds, ...naturals];
                await NaturalPersonModel.updateMany({_id: {$in: naturals}},
                    {$set: {lockedByFileReview: ObjectId(req.params.companyId)}})
            }
            if (organizations) {
                relIds = [...relIds, ...organizations];
                await OrganizationModel.updateMany({_id: {$in: organizations}},
                    {$set: {lockedByFileReview: ObjectId(req.params.companyId)}})
            }

            if (relIds) {
                relIds.forEach((relationId) => {
                    let index = relationsData.beneficialOwners.findIndex((r) => r && r._id === relationId);
                    if (index > -1) {
                        relationsData.beneficialOwners[index].lockedByFileReview = req.params.companyId;
                    }

                    index = relationsData.shareholders.findIndex((r) => r && r._id === relationId);
                    if (index > -1) {
                        relationsData.shareholders[index].lockedByFileReview = req.params.companyId;
                    }

                    index = relationsData.directors.findIndex((r) => r && r._id === relationId);
                    if (index > -1) {
                        relationsData.directors[index].lockedByFileReview = req.params.companyId;
                    }

                });

            }
        }

        if (fileReview && fileReview.pendingElectronicIds && fileReview.pendingElectronicIds.length> 0){

            if (relationsData.electronicIdList && relationsData.electronicIdList.length > 0){

                fileReview.pendingElectronicIds = fileReview.pendingElectronicIds.filter((p) =>
                  !relationsData.electronicIdCanceledList.includes(p.relationId.toString()) &&
                  relationsData.electronicIdList.includes(p.relationId.toString()));

                fileReview.markModified('pendingElectronicIds');
                await fileReview.save();

            }


            const incompleteElectronicIds = fileReview.pendingElectronicIds.filter((p) => p.complete === false);

            if (incompleteElectronicIds.length > 0){
                hasPendingElectronicInfo = true;
            }


        }


        if (relationsData && relationsData.file.fileReview.username.toLowerCase() === req.session.user.username.toLowerCase()) {
            res.render('file-reviewer/beneficial-owners', {
                user: req.session.user,
                file: relationsData.file,
                title: relationsData.file.companyName,
                id: req.params.companyId,
                fileReview: fileReview,
                beneficialOwners: relationsData.beneficialOwners,
                shareholders: relationsData.shareholders,
                directors: relationsData.directors,
                hasPendingElectronicInfo: hasPendingElectronicInfo || relationsData.hasElectronicIdNotStarted,
                hasElectronicIdNotStarted: relationsData.hasElectronicIdNotStarted,
                incorrectShareholderPercentage: relationsData.shareholders.length > 0 && relationsData.totalPercentage !== 100,
            });
        } else {
            res.redirect('/file-reviewer/dashboard');
        }
    } catch (error) {
        console.log(error);
        res.redirect('/');
    }
};

exports.submitFileReview = async function (req, res) {
    try {
        let review = await FileReviewModel.findById(req.body.companyId);
        if (review) {
            if (req.body.status === "validateReview") {
                review.status.code = "REVIEWED";
                review.status.statusDate = new Date();
                review.fileReview.validatedDate = new Date();

                const reviewComment = {
                    username: req.session.user.username,
                    role: 'FR',
                    comment: req.body.comment,
                    date: new Date(),
                    from: 'FR',
                    to: 'QA',
                };
                review.comments.push(reviewComment);

            }
            await review.save();
            return res.status(200).json({success: true});
        } else {
            return res.status(400).end();
        }
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};

exports.validateFileReview = async function (req, res) {
    try {
        let review = await FileReviewModel.findById(req.params.companyId);
        let incompleteSection = [];
        let incompleteFiles = [];
        let sections = {
            "natural": ["details", "identification", "residentialAddress", "mailingAddress", "taxResidence", "advisorDetails", "principalAddress", "worldCheck"],
            "corporate": ["details", "principalAddress", "mailingAddress", "listedCompanyDetails", "limitedCompanyDetails", "mutualFundDetails", "worldCheck"],
            "foundation": ["details", "principalAddress", "mailingAddress", "listedCompanyDetails", "limitedCompanyDetails", "mutualFundDetails", "foundation", "worldCheck"],
            "limited": ["details", "principalAddress", "mailingAddress", "listedCompanyDetails", "limitedCompanyDetails", "mutualFundDetails", "worldCheck"],
            "trust": ["details", "principalAddress", "mailingAddress", "mutualFundDetails", "worldCheck"],
        };
        let incompleteReview = false;
        let relationInformation = [];
        if (review) {
            if (review.beneficialOwners) {
                relationInformation = [...review.beneficialOwners.natural, ...review.beneficialOwners.organization];
            }
            if (review.shareholders) {
                relationInformation = [...relationInformation, ...review.shareholders.natural, ...review.shareholders.organization];
            }
            if (review.directors) {
                relationInformation = [...relationInformation, ...review.directors.natural, ...review.directors.organization];
            }

            let relations = await relationController.getReviewRelations(req.params.companyId);

            if (relations) {
                relations = [...relations.beneficialOwners, ...relations.shareholders, ...relations.directors];
            }
            for (let file of review.files) {
                if (!file.present) {
                    incompleteFiles.push(file);
                }
            }

            if (relations && relationInformation) {
                relationInformation.forEach((relation) => {
                    const rel = relations.find((r) => r && r._id.toString() === relation.referenceId.toString());
                    const sectionsByType = sections[rel.type] ? [...sections[rel.type]] : [];
                    if (rel && rel.type === "natural" && rel.pep === true) {
                        sectionsByType.push('pepDetails');
                    }
                    sectionsByType.forEach((section) => {
                        if (relation[section] && !relation[section].complete) {
                            const index = incompleteSection.findIndex((rel) => rel && rel._id.toString() === relation.referenceId.toString());
                            if (index === -1) {
                                incompleteSection.push(relation);
                            }

                        }
                    });
                });
            }

            if (incompleteSection.length > 0 || incompleteFiles.length > 0) {
                incompleteReview = true;
            }
        }
        return res.status(200).json({success: true, isIncomplete: incompleteReview});
    } catch (error) {
        console.log(error);
        res.redirect('/');
    }
};

exports.editPercentage = async function (req, res) {
    try {
        let review = await FileReviewModel.findById(req.params.companyId);
        if (review) {
            if (req.body.type === "natural") {
                await ClientReviewModel.updateOne({
                        "_id": review.clientId,
                        "naturalRelations": {
                            "$elemMatch": {
                                "referenceId": req.body.relationId,
                                "group": req.body.group
                            }
                        }
                    },
                    {"naturalRelations.$.percentage": req.body.percentage}
                );
            } else {
                await ClientReviewModel.updateOne({
                        "_id": review.clientId,
                        "organizationRelations": {
                            "$elemMatch": {
                                "referenceId": req.body.relationId,
                                "group": req.body.group
                            }
                        }
                    },
                    {"organizationRelations.$.percentage": req.body.percentage}
                );
            }
        }
        return res.status(200).json({success: true});
    } catch (error) {
        console.log(error);
        res.redirect('/');
    }

};

exports.changeReviewStatus = async function (req, res) {
    try {
        const roleCode = {
            "fileReviewer": 'FR',
            "qualityAssurance": 'QA',
            "client": 'CL',
            "compliance": 'CO'
        };

        const statusCodes = {
            "sendOfficer": 'SEND TO FILE REVIEW OFFICER BY ' + roleCode[req.body.officer],
            "sendClient": 'SEND TO CLIENT BY ' + roleCode[req.body.officer],
            "sendQuality": 'ASSIGNED QA BY ' + roleCode[req.body.officer],
            "sendCompliance": 'COMPLIANCE BY ' + roleCode[req.body.officer],
            "validate": "VALIDATED QA"
        };

        let review = await FileReviewModel.findById(req.params.reviewId);
        if (review) {

            if (req.body.officer === "qualityAssurance") {
                review.qualityAssurance = {
                    username: req.session.user.username,
                    name: req.session.user.name,
                    dateAssigned: new Date(),
                };
            }

            const assigner = {
                username: req.session.user.username,
                role: roleCode[req.body.officer],
                dateAssigned: new Date(),
            };


            if (req.body.status === "sendOfficer" || req.body.status === "sendClient") {
                review.fileReview.assignedBy = assigner;
            } else if (req.body.status === "sendCompliance") {
                review.compliance.assignedBy = roleCode[req.body.officer];
            }

            const reviewComment = {
                username: req.session.user.username,
                role: assigner.role,
                comment: req.body.comment,
                date: new Date(),
                from: assigner.role,
                to: req.body.status === "sendOfficer" ? 'FR' :
                    req.body.status === "sendQuality" ? 'QA' :
                        req.body.status === "sendCompliance" ? 'CO' : 'CL',
            };

            if (req.body.officer === "compliance" && review.status.code === "COMPLIANCE") {
                if (review.compliance.assignedBy.toString() === "CO" && (req.body.status === "sendOfficer" || req.body.status === "sendQuality")) {
                    review = clearReviewValidations(review, req.body.status);
                }
            }
            review.status.code = statusCodes[req.body.status];
            review.comments.push(reviewComment);
            await review.save();
            return res.status(200).json({success: true});
        } else {
            return res.status(400).end();
        }
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};


exports.addComment = async function (req, res) {
    try {
        let review = await FileReviewModel.findById(req.params.reviewId);
        if (review) {
            const reviewComment = {
                username: req.session.user.username,
                role: req.body.officer,
                comment: req.body.comment,
                date: new Date(),
                from: req.body.officer,
                to: ''
            };

            if (req.body.status) {
                reviewComment.to = req.body.status === "sendOfficer" ? 'FR' :
                    req.body.status === "sendQuality" ? 'QA' :
                        req.body.status === "compliance" ? 'CO' : 'CL';
            }

            review.comments.push(reviewComment);
            await review.save();
            return res.status(200).json({success: true});
        } else {
            return res.status(400).end();
        }
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};


exports.requestElectronicInfo = async function (req, res) {
    try {
        let review = await FileReviewModel.findById(req.params.reviewId);
        let naturalRelations = [];
        let relationWithErrors = [];
        if (!review){
            return res.status(404).json({status: 404, error: "File Review not found"});
        }

        const client =  await ClientReviewModel.findById(review.clientId);

        if(!client){
            return res.status(404).json({status: 404, error: "File Review Client not found"});
        }

        const naturalIds = [...new Set(client.naturalRelations.map(r => r.referenceId))];

        if (naturalIds){
            naturalRelations = await NaturalPersonModel.find({_id: {"$in": naturalIds},
                "electronicIdInfo.isElectronicId": true, "electronicIdInfo.status": {"$ne": "COMPLETED"}},
              {_id: 1, details: 1, electronicIdInfo: 1});
        }

        if (naturalRelations.length > 0){
            for (let i = 0; i < naturalRelations.length; i++) {
                const electronicIdInfo = naturalRelations[i].electronicIdInfo;
                if (electronicIdInfo && electronicIdInfo.isElectronicId){
                    const name = naturalRelations[i].details.firstName + " " +  naturalRelations[i].details.lastName;
                    if (!electronicIdInfo.email){
                        electronicIdInfo.comments.push({
                            username: req.session.user.username,
                            comment: "The e-mail is missing to send the id-pal invitation ",
                            date: new Date(),
                            status: 'PENDING INVITATION',
                        });
                        relationWithErrors.push(name);
                        continue;
                    }

                    let uuid;
                    if(electronicIdInfo.uuid && electronicIdInfo.status !== "EXPIRED"){
                        uuid = electronicIdInfo.uuid;
                    }
                    else{
                        const palResponse = await IdPalController.generateUuid(naturalRelations[i]._id.toString());
                        if (palResponse && palResponse.status === 200){
                            uuid =  palResponse.uuid;
                        }
                        else{
                            electronicIdInfo.electronicIdInvitationSent = false;
                            electronicIdInfo.status = "NOT SENT";
                            electronicIdInfo.comments.push({
                                username: req.session.user.username,
                                comment: "Error sending invitation: " + (palResponse.message && palResponse.message.error ?
                                  palResponse.message.error : palResponse.message) ,
                                date: new Date(),
                                status: electronicIdInfo.status,
                            });
                            relationWithErrors.push(name);
                        }
                    }

                    if (uuid){
                        const relationElectronicReference = {
                            relationId: naturalRelations[i]._id,
                            uuid:  uuid,
                            complete: false,
                        };
                        const url = process.env.CLIENTPORTAL_APP_HOST + "/idpal?uuid=" + uuid;

                        let email = MailFormatter.generateFileReviewIdPalEmail(url, 'new-invitation-template');
                        let sentEmailResult = await MailController.asyncSend(
                          electronicIdInfo.email,
                          review.companyName + ' - ID Verification',
                          email.textString,
                          email.htmlString
                        );

                        if (sentEmailResult.accepted) {
                            electronicIdInfo.electronicIdInvitationSent = true;
                            electronicIdInfo.status = "IN PROGRESS";
                            review.status.code = "ON HOLD";
                            electronicIdInfo.invitationDate = new Date();
                            electronicIdInfo.uuid = uuid;
                            electronicIdInfo.comments.push({
                                username:req.session.user.username,
                                comment: "ID request has been send to e-mail " + electronicIdInfo.email,
                                date: new Date(),
                                status: electronicIdInfo.status,
                            });
                            review.comments.push(({
                                username:req.session.user.username,
                                role: "FR",
                                comment: req.session.user.username + " send invitation to " + electronicIdInfo.email +
                                " requesting electronic id information at " + moment().format('YYYY-MM-DD'),
                                date: new Date(),
                            }));
                            if (!review.pendingElectronicIds) {
                                review.pendingElectronicIds = [relationElectronicReference];
                            }
                            else{
                                const index = review.pendingElectronicIds.findIndex((e) =>
                                  e.relationId && e.relationId.toString() === naturalRelations[i]._id.toString());

                                if (index > -1){
                                    review.pendingElectronicIds[index] = relationElectronicReference;
                                }
                                else{
                                    review.pendingElectronicIds.push(relationElectronicReference);
                                }

                            }
                        }
                        else{
                            electronicIdInfo.electronicIdInvitationSent = false;
                            electronicIdInfo.status.code = "NOT SENT";
                            electronicIdInfo.comments.push({
                                username: req.session.user.username,
                                comment: "Error sending email invitation",
                                date: new Date(),
                                status: electronicIdInfo.status,
                            });

                        }
                    }
                    naturalRelations[i].electronicIdInfo = electronicIdInfo;
                    await NaturalPersonModel.findByIdAndUpdate(naturalRelations[i]._id, naturalRelations[i]);
                }
            }
            await review.save();
        }
        if (relationWithErrors.length > 0){
            return res.status(400).json({
                status: 400,
                error: "Error sending invitation to the next relations: " + relationWithErrors.join(", ")
            })
        }
        else{
            return res.status(200).json({
                status: 200,
                message: "Invitations send successfully"
            })
        }

    } catch (error) {
        console.log(error);
        return res.status(500).json({status:500, error: "Internal server error"});
    }
};


exports.requestClientFiles = async function (req, res) {
    try {
        let review = await FileReviewModel.findById(req.params.reviewId);

        if (!review){
            return res.status(404).end();
        }

        const clientReview = await ClientReviewModel.findById(review.clientId);

        if (!clientReview){
            return res.status(404).end();
        }

        await saveClientInformation(review.clientId, req.body);
        if (req.body.files && req.body.files) {
            const keys = Object.keys(req.body.files);
            for (const key of keys) {
                const fileIndex = review.files.findIndex((file) => file.id === key);

                if (fileIndex !== -1) {
                    let updatedFile = review.files[fileIndex];
                    if (updatedFile) {
                        updatedFile.present = !!req.body.files[key].present;
                        updatedFile.explanation = req.body.files[key].explanation ? req.body.files[key].explanation : '';
                        review.files.set(fileIndex, updatedFile);
                    }

                }
            }
            if (req.body.companyActivity) {
                review.companyActivityReview.present = !!req.body.companyActivity.present;
                review.companyActivityReview.explanation = req.body.companyActivity.explanation;
                clientReview.companyActivity.activity = req.body.companyActivity.select;

            }
            await clientReview.save();
        }

        review.status = {
            code: "SENT TO CLIENT",
            statusDate: new Date()
        };

        review.markModified('files');

        let requestFileNames = [];
        let requestFiles = review.files.filter((f) => f.present !== true && f.internal !== "World Check Completed").map((f) => {
            requestFileNames.push(f.external);
            return {
                id: f.id,
                name: f.external,
                present:  false,
                explanation: f.explanation,
                uploadFiles: []
            }
        });


        const requestComment = {
            username: req.session.user.username.toLowerCase(),
            role: 'FR',
            comment: 'Requested files: ' + requestFileNames.join(', '),
            date: new Date(),
            from: 'FR',
            to: 'CL'
        };
        review.comments.push(requestComment);
        review.markModified('comments');
        review.updatedAt = new Date();


        let requestFilesObject = new RequestedFileModel({
            referenceId: review._id,
            status: 'NOT STARTED',
            companyCode: review.companyCode,
            companyName: review.companyName,
            masterClientCode: review.masterClientCode,
            reviewerComment: req.body.requestClientFilesComment || '',
            files: requestFiles,
            createdAt: new Date(),
            updatedAt: new Date(),
        });

        let generateMessage = await MessageController.generateRequestFileByFRMessage({
            subject: "Compliance Request for Documents",
            mcc: review.masterClientCode,
            companyName: review.companyName
        });

        if (!generateMessage || generateMessage.status !== 200){
            console.log("ERROR GENERATING MESSAGE ", generateMessage);
            return res.status(generateMessage.status).end();
        }else{
            requestFilesObject.messageId = generateMessage.mid;
            await  requestFilesObject.save();
            await review.save();
            return res.status(200).end();
        }
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};



exports.cancelRequestClientFiles = async function (req, res) {
    try {
        let review = await FileReviewModel.findById(req.params.reviewId);

        if (!review){
            return res.status(404).end();
        }


        review.status = {
            code: "ON HOLD",
            statusDate: new Date()
        };

        const requestComment = {
            username: req.session.user.username.toLowerCase(),
            role: 'FR',
            comment: 'Update my file request canceled, reason: ' + (req.body.comment ? req.body.comment : ''),
            date: new Date(),
            from: 'FR',
            to: ''
        };

        let requestedFile = await RequestedFileModel.findOne({referenceId: review._id, status: {"$in": ["NOT STARTED", "IN PROGRESS"]}});

        if (requestedFile){
            await MessageModel.findOneAndDelete({_id: requestedFile.messageId, "status": "SCHEDULED"});
            requestedFile.status = "CANCELLED";
            requestedFile.comments = "File request canceled by " + req.session.user.username.toLowerCase() +
              ". Reason: " + (req.body.comment ? req.body.comment : '');
            requestedFile.updatedAt = new Date();
            await requestedFile.save();
        }

        review.comments.push(requestComment);
        review.updatedAt = new Date();
        review.markModified('comments');
        await review.save();
        return res.status(200).end();
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};


exports.updateFileProvidedField = async function(req, res) {
    try {
        let review = await FileReviewModel.findById(req.body.reviewId);
        let updated = false;
        if (!review){
            return res.status(404).json({status: 404, error: "File Review not found"});
        }

        if (review.files.length > 0){
            let fileIndex = review.files.findIndex((f) => f.id === req.body.fileId);

            if (fileIndex > -1){
                review.files[fileIndex].provided = false;
                review.markModified('files');
                await review.save();
                updated = true;
            }
        }

        if (updated){
            return res.status(200).json({
                status: 200,
                message: "Provided field updated successfully"
            })
        }
        else{
            return res.status(400).json({
                status: 400,
                message: "Error updating the provided file field"
            })
        }


    } catch (error) {
        console.log(error);
        return res.status(500).json({status:500, error: "Internal server error"});
    }

}
