const {
    filereview: FileReviewModel,
} = require('../../models/filereview');
const NaturalPersonModel = require('../../models/naturalperson');
const ClientReviewModel = require('../../models/client');
const FileReviewArchiveModel = require('../../models/filereviewarchive');
const relationController = require('../relations/relationController');
const OrganizationModel = require('../../models/organization');

exports.getQualityAssuranceList = async function (req, res) {
    try {
        let fileReviews = [];
        let filteredReviews = [];

        let assignedReviews = await FileReviewModel.find({
            "status.code": {"$in": ['ASSIGNED QA BY CO', 'IN PROGRESS BY QA']},
            "qualityAssurance.username": req.session.user.username.toLowerCase()
        });

        if (req.body.search_filter && req.body.search_filter.length > 2) {
            fileReviews = await FileReviewModel.find({
                "status.code": 'REVIEWED',

                $or: [{'companyName': {$regex: req.body.search_filter, $options: 'i'}},
                    {'companyCode': {$regex: req.body.search_filter, $options: 'i'}},
                    {'masterClientCode': {$regex: req.body.search_filter, $options: 'i'}}]

            }, {
                'companyName': 1, 'companyCode': 1, 'masterClientCode': 1, 'status': 1, '_id': 1, 'files': 1,
                'fileReview': 1
            }).sort('companyName').limit(100);
        } else {
            fileReviews = await FileReviewModel.find({
                "status.code": 'REVIEWED',
            }, {
                'companyName': 1, 'companyCode': 1, 'masterClientCode': 1, 'status': 1, '_id': 1, 'files': 1,
                'fileReview': 1
            }).sort('companyName').limit(100);
        }

        if (req.body.filter_filereview === 'completed') {

            for (let i = 0; i < fileReviews.length; i++) {
                for (let j = 0; j < fileReviews[i].files.length; j++) {
                    if ((fileReviews[i].files[j].present === false || fileReviews[i].companyActivityReview.present === false)) {
                        break;
                    } else {
                        if (j === fileReviews[i].files.length - 1) {
                            fileReviews[i].check = true;
                            filteredReviews.push(fileReviews[i])
                        }
                    }
                }
                if (i === fileReviews.length - 1) {
                    fileReviews = filteredReviews;
                    break;
                }
            }
        } else {
            if (req.body.filter_filereview === 'client-interaction') {
                for (let i = 0; i < fileReviews.length; i++) {
                    if (fileReviews[i].approved !== true) {
                        for (let j = 0; j < fileReviews[i].files.length; j++) {
                            if ((fileReviews[i].files[j].present === false || fileReviews[i].companyActivityReview.present === false)) {
                                fileReviews[i].check = false;
                                filteredReviews.push(fileReviews[i]);
                                break;
                            }
                        }
                        if (i === fileReviews.length - 1) {
                            fileReviews = filteredReviews;
                            break;
                        }
                    }
                }
            }
        }

        res.render('file-reviewer/quality-assurance/quality-assurance-list',
            {
                user: req.session.user,
                title: "Quality Assurance",
                assignedReviews: assignedReviews,
                fileReviews: fileReviews
            });

    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};

exports.openQualityReview = async function (req, res) {
    try {
        let fileReview = await FileReviewModel.findById(req.params.companyId);
        if(fileReview && fileReview.status.code === "REVIEWED"){
            fileReview.status = {
                code: 'IN PROGRESS BY QA',
                statusDate: new Date()
            };
            fileReview.qualityAssurance = {
                username: req.session.user.username,
                name: req.session.user.name,
                dateAssigned: new Date(),
            };
            await fileReview.save();
        }
        const clientReview = await ClientReviewModel.findById(fileReview.clientId);
        let standardFileQuestions = fileReview.files;
        let freeQuestions = clientReview.freeQuestions;
        let recordKeepingQuestions = clientReview.recordDetails;
        let companyActivity = clientReview.companyActivity;
        let companyActivityReview = fileReview.companyActivityReview;
        // check if there is any pep inside the file review
        let pep = false;
        if (clientReview.naturalRelations.length) {
            pep = await NaturalPersonModel.countDocuments({fileReview: req.params.companyId, pep: true}) > 0;
        }

        res.render('file-reviewer/quality-assurance/quality-assurance-review', {
            user: req.session.user,
            title: 'Company Files: ' + fileReview.companyName,
            id: fileReview._id,
            file: fileReview,
            standardFileQuestions: standardFileQuestions,
            freeQuestions: freeQuestions,
            recordKeepingQuestions: recordKeepingQuestions,
            pep: pep,
            companyActivity: companyActivity,
            companyActivityReview: companyActivityReview,
        });

    } catch (error) {
        console.log(error);
        res.redirect('/file-reviewer/quality-assurance-list');
    }

};

exports.saveStandardQualityReview = async function (req, res) {
    try {
        let review = await FileReviewModel.findById(req.params.companyId);
        let reviewFiles = review.files;
        if (req.body.files) {
            reviewFiles.forEach((file) => {
                const fileId = req.body.files[file.id];
                if (fileId) {
                    if (fileId.validated) {
                        file.validated = true;
                    }
                } else {
                    file.validated = false;
                }
            });
            review.files = reviewFiles;
            review.markModified("files");
        }
        if (req.body.companyActivity) {
            review.companyActivityReview.validated = !!req.body.companyActivity.validated;
        }
        await review.save();
        res.redirect('/file-reviewer/quality-assurance-review/' + req.params.companyId + '/relations');
    } catch (error) {
        console.log(error);
        res.redirect('/');
    }
};

exports.getRelations = async function (req, res) {
    try {
        let relationsData = await relationController.getReviewRelations(req.params.companyId);


        if (relationsData) {
            res.render('file-reviewer/quality-assurance/quality-assurance-relations-list', {
                user: req.session.user,
                file: relationsData.file,
                title: relationsData.file.companyName + ': Beneficial Owners',
                id: req.params.companyId,
                beneficialOwners: relationsData.beneficialOwners,
                shareholders: relationsData.shareholders,
                directors: relationsData.directors
            });
        } else {
            res.redirect('/file-reviewer/dashboard');
        }
    } catch (error) {
        console.log(error);
        res.redirect('/');
    }
};

exports.getFilesByReview = async function (req, res) {
    try {
        let review;
        let clientReview;
        let filesToReturn;
        let uploadFiles;
        if (req.query.type === "standard") {
            review = await FileReviewModel.findById(req.params.companyId);
            clientReview = await ClientReviewModel.findById(review.clientId);
            const file = review.files.find((file) => file.id.toString() === req.query.fileId);
            uploadFiles = file.uploadFiles;
            filesToReturn = clientReview.files.filter((file) => uploadFiles.includes(file.fileId));
        } else if (req.query.type === "natural") {
            if (req.query.fileId) {
                review = await NaturalPersonModel.findById(req.query.fileId);
            }
        } else {
            if (req.query.fileId) {
                review = await OrganizationModel.findById(req.query.fileId);
            }
        }
        if (!review) {
            return res.status(500).end();
        }
        return res.status(200).json({success: true, files: filesToReturn});

    } catch (e) {
        console.log(e);
    }
};

exports.validateQualityReview = async function (req, res) {
    try {
        let review = await FileReviewModel.findById(req.params.companyId);
        let incompleteReview = false;
        let incompleteSection = [];
        let incompleteFiles = [];
        let incompleteFilesSection = false;
        let incompleteActivity = false;
        let relationInformation = [];
        let positionsList = [];
        let sections = {
            "natural": ["details", "identification", "residentialAddress", "mailingAddress", "taxResidence", "advisorDetails", "principalAddress", "worldCheck"],
            "corporate": ["details", "detailsPartner", "principalAddress", "mailingAddress", "listedCompanyDetails", "limitedCompanyDetails", "mutualFundDetails", "worldCheck"],
            "foundation": ["details", "detailsPartner", "principalAddress", "mailingAddress", "listedCompanyDetails", "limitedCompanyDetails", "mutualFundDetails", "foundation", "worldCheck"],
            "limited": ["details", "detailsPartner", "principalAddress", "mailingAddress", "listedCompanyDetails", "limitedCompanyDetails", "mutualFundDetails", "worldCheck"],
            "trust": ["details", "detailsPartner", "principalAddress", "mailingAddress", "mutualFundDetails", "worldCheck"],
        };
        if (review) {
            if (review.beneficialOwners) {
                relationInformation = [...review.beneficialOwners.natural, ...review.beneficialOwners.organization];
            }
            if (review.shareholders) {
                relationInformation = [...relationInformation, ...review.shareholders.natural, ...review.shareholders.organization];
            }
            if (review.directors) {
                relationInformation = [...relationInformation, ...review.directors.natural, ...review.directors.organization];
            }

            let relations = await relationController.getReviewRelations(req.params.companyId, true);

            if (relations) {
                positionsList = relations ? relations.positionList : [];
                relations = [...relations.beneficialOwners, ...relations.shareholders, ...relations.directors];
            }
            for (let file of review.files) {
                if (!file.validated) {
                    incompleteFiles.push(file);
                }
            }

            if (relations && relationInformation) {
                relationInformation.forEach((relation) => {
                    const rel = relations.find((r) => r && r._id.toString() === relation.referenceId.toString());
                    const sectionsByType = sections[rel.type] ? [...sections[rel.type]] : [];
                    if (rel && rel.type === "natural" && rel.pep === true) {
                        sectionsByType.push('pepDetails');
                    }

                    if (rel && rel.lockedByFileReview && rel.lockedByFileReview.toString() === req.params.companyId) {

                        sectionsByType.forEach((section) => {
                            if (relation[section] && !relation[section].validated) {
                                const index = incompleteSection.findIndex((rel) => rel && rel._id.toString() === relation.referenceId.toString());
                                if (index === -1) {
                                    incompleteSection.push(relation);
                                }

                            }
                            if (rel[section] && rel[section].files) {
                                const fileIds = rel[section].files.map((f) => f.id);
                                if (relation.files) {
                                    const files = relation.files.filter((file) => fileIds.includes(file.referenceFile));
                                    const invalidFile = files.some((file) => file.validated === false);
                                    if (invalidFile) {
                                        incompleteFilesSection = true;
                                    }
                                } else {
                                    incompleteFilesSection = true;
                                }
                            }
                        });
                    }
                });
            }

            if (relations && review.positions) {

                if (!positionsList) {
                    return res.status(404).json({success: false});
                }

                review.positions.forEach((position) => {
                    sections["natural"].forEach((section) => {
                        const reviewPosition = positionsList.find((p) => p && p.id.toString() === position.referenceId.toString());
                        if (reviewPosition["position"].lockedByFileReview &&
                            reviewPosition["position"].lockedByFileReview.toString() === req.params.companyId) {
                            if (position[section] && !position[section].validated) {
                                const index = incompleteSection.findIndex((rel) =>
                                    rel && rel._id.toString() === position.referenceId.toString());

                                if (index === -1) {
                                    incompleteSection.push(position);
                                }
                            }

                            if (section === "identification" || section === "worldCheck") {

                                if (!reviewPosition || (reviewPosition && !reviewPosition["position"])) {
                                    incompleteFilesSection = true;
                                } else {
                                    const pos = reviewPosition["position"];

                                    if (pos[section] && pos[section].files) {
                                        const invalidFile = pos[section].files.some((file) => file.validated === false);
                                        if (invalidFile) {
                                            incompleteFilesSection = true;
                                        }
                                    }
                                }

                            }

                        }
                    });


                });
            }

            if (review.companyActivityReview.validated === false) {
                incompleteActivity = true;
            }

            if (incompleteSection.length || incompleteFiles.length || incompleteFilesSection || incompleteActivity) {
                incompleteReview = true;
            }
        }

        return res.status(200).json({success: true, isIncomplete: incompleteReview});
    } catch (error) {
        console.log(error);
        res.redirect('/');
    }
};

exports.submitQualityAssuranceReview = async function (req, res) {
    try {
        const statusCodes = {
            "sendOfficer": 'SEND TO FILE REVIEW OFFICER BY QA',
            "sendClient": 'SEND TO CLIENT BY QA',
            "sendQuality": 'SEND TO QA BY CO',
            "compliance": 'COMPLIANCE BY QA',
            "validate": "VALIDATED QA"
        };

        let review = await FileReviewModel.findById(req.params.companyId);
        let isNotValid = req.body.responseValidate === "true";
        if (review) {
            review.qualityAssurance = {
                username: req.session.user.username,
                name: req.session.user.name,
                dateAssigned: new Date(),
            };
            if (req.body.status === "sendOfficer" || req.body.status === "compliance" || req.body.status === "sendClient") {
                review.status.code = statusCodes[req.body.status];

                review.fileReview.assignedBy = {
                    username: req.session.user.username,
                    role: 'QA',
                    dateAssigned: new Date(),
                };

                const reviewComment = {
                    username: req.session.user.username,
                    role: 'QA',
                    comment: req.body.comment,
                    date: new Date(),
                    from: 'QA',
                    to: req.body.status === "sendOfficer" ? 'FR' :
                        req.body.status === "sendCompliance" ? 'CO' : 'CL',
                };
                review.comments.push(reviewComment);

            } else {

                if (isNotValid === false) {
                    const fileReviewArchive = await createFileReviewArchive(review);

                    if (!fileReviewArchive) {
                        console.log("Error creating the archive for the fileReview");
                        return res.status(500).end();
                    }

                    review.status.code = statusCodes['validate'];
                    review.approved = true;
                    review.qualityAssurance.validatedDate = new Date();
                    const reviewComment = {
                        username: req.session.user.username,
                        role: 'QA',
                        comment: 'File review validated by QA',
                        date: new Date(),
                        from: 'QA',
                        to: ''
                    };
                    review.comments.push(reviewComment);

                    await NaturalPersonModel.updateMany({lockedByFileReview: req.params.companyId}, {lockedByFileReview: null});
                    await OrganizationModel.updateMany({lockedByFileReview: req.params.companyId}, {lockedByFileReview: null});
                } else {
                    review.status.code = statusCodes['sendOfficer'];
                    review.fileReview = {
                        username: req.session.user.username,
                        name: req.session.user.name,
                        dateAssigned: new Date(),
                        assignedBy: {
                            username: req.session.user.username,
                            role: 'QA',
                            dateAssigned: new Date(),
                        },
                    };
                }
            }
            await review.save();
            return res.status(200).json({success: true, isIncomplete: isNotValid});
        } else {
            return res.status(400).end();
        }
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};

exports.submitQualityReviewRelation = async function (req, res) {
    try {
        let reviewGroup;
        let reviewGroupKey;
        let relationData;
        let sections = [];
        let relation;

        const formData = req.body.relationData ? req.body.relationData : [];


        if (req.body.type === 'natural') {
            sections = ["details", "identification", "mailingAddress", "residentialAddress", "taxResidence",
                "advisorDetails", "principalAddress", "pepDetails", "worldCheck"];
            relation = await NaturalPersonModel.findById(req.params.relationId);
        } else {
            sections = ["details", "detailsPartner", "principalAddress", "mailingAddress", "listedCompanyDetails",
                "limitedCompanyDetails", "mutualFundDetails", "foundation", "worldCheck"];
            relation = await OrganizationModel.findById(req.params.relationId);
        }

        let review = await FileReviewModel.findById(req.params.companyId);
        if (review) {
            if (req.body.group === "beneficial") {
                reviewGroupKey = "beneficialOwners";
            } else if (req.body.group === "shareholder") {
                reviewGroupKey = "shareholders";
            } else {
                reviewGroupKey = "directors";
            }

            reviewGroup = review[reviewGroupKey];
            const type = req.body.type === 'natural' ? 'natural' : 'organization';
            const index = reviewGroup[type].findIndex((rel) => rel.referenceId.toString() === req.params.relationId);
            if (index > -1) {
                relationData = reviewGroup[type][index];
            }
            if (relationData) {
                sections.forEach((section) => {

                    if (relationData[section]) {

                        let formValues;
                        if (formData && formData.length > 0){
                            formValues = formData.find((field) => {
                                return field["name"].includes(section) && field["name"].includes("validated") && !field["name"].includes("files")
                            });
                        }

                        if (formValues) {
                            relationData[section].validated = !!(formValues["value"]);
                        } else {
                            relationData[section].validated = false;
                        }

                    }
                    if (section === 'pep') {
                        section = 'pepDetails';
                    }
                });


                if (relationData && relationData.files) {
                    relationData.files.forEach((file) => {
                        const formFiles = formData.find((field) => {
                            return field["name"] && field["name"].includes("files") && field["name"].includes(file.referenceFile)
                        });
                        file.validated = !!formFiles;
                    });

                }

                if (relation.electronicIdInfo && relation.electronicIdInfo.files){
                    const electronicIdInfo = relation.electronicIdInfo;

                    electronicIdInfo.files.forEach((file) => {
                        const formFiles = formData.find((field) => {
                            return field["name"] && field["name"].includes("files") && field["name"].includes(file.id)
                        });


                        if (relationData.files){
                            const index = relationData.files.findIndex((f) => f.referenceFile === file.id);
                            if (index > -1){
                                relationData.files[index].validated = !!formFiles;
                            }
                            else{
                                relationData.files.push({
                                    referenceFile: file.id,
                                    validated: !!formFiles
                                })
                            }
                        }
                        else{
                            relationData.files = [];
                            relationData.files.push({
                                referenceFile: file.id,
                                validated: !!formFiles
                            })
                        }
                    })


                }
            }

            reviewGroup[type].set(index, relationData);
            review[reviewGroupKey] = reviewGroup;
            review.markModified(reviewGroupKey);
            await review.save();
            await relation.save();
            return res.status(200).end();
        } else {
            console.log("error, not found relation");
            return res.status(400).end();
        }
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};

exports.submitQualityPosition = async function (req, res) {
    try {
        let sections = ["details", "identification", "residentialAddress", "principalAddress", "mailingAddress",
            "advisorDetails", "taxResidence", "worldCheck"];
        let review = await FileReviewModel.findById(req.params.reviewId);
        if (review.fileReview.username.toLowerCase() === req.session.user.username.toLowerCase()) {
            const organization = await OrganizationModel.findById(req.params.organizationId);

            if (!organization) {
                return res.status(404).end();
            }
            const orgPosition = organization.positions.find((p) => p && p._id.toString() === req.params.positionId);

            let positionReview = await NaturalPersonModel.findById(orgPosition.referenceId);
            const positionIndex = review.positions.findIndex((pos) => pos && pos.referenceId.toString() === req.params.positionId);

            if (positionIndex === -1) {
                return res.status(404).end();
            }

            let position = review.positions[positionIndex];
            sections.forEach((section) => {
                if (position[section]) {
                    position[section].validated = !!(req.body[section] && req.body[section].validated);
                }
            });

            sections = ["identification", "worldCheck"];
            const bodyFiles = req.body.files;
            sections.forEach((section) => {
                if (positionReview[section] && positionReview[section].files) {
                    positionReview[section].files.forEach((file) => {
                        file.validated = !!(bodyFiles && bodyFiles[section] && bodyFiles[section][file.id]);
                    });

                }
                positionReview.markModified(section);
            });

            review.positions[positionIndex] = position;
            review.markModified("positions");
            await positionReview.save();
            await review.save();
            return res.status(200).end();
        } else {
            return res.status(404).end();
        }
    } catch (e) {
        console.log(e);
        return res.status(500).end();
    }
};

async function createFileReviewArchive(review) {
    try {
        const clientReview = await ClientReviewModel.findById(review.clientId);

        let naturalRelations = [];
        let organizationRelations = [];
        let positions = [];

        let fileReviewArchive = new FileReviewArchiveModel({
            fileReviewId: review._id,
            client: clientReview,
            naturalRelations: [],
            organizationRelations: [],
            positions: [],
            createdAt: new Date(),
            updatedAt: new Date(),
            partitionkey: 'filereviewarchive'
        });

        let naturalIds = clientReview.naturalRelations.map((r) => r.referenceId);
        let organizationsIds = clientReview.organizationRelations.map((r) => r.referenceId);
        let positionIds = [];

        naturalIds = naturalIds ? [...new Set(naturalIds)] : [];
        organizationsIds = organizationsIds ? [...new Set(organizationsIds)] : [];

        if (naturalIds) {
            naturalRelations = await NaturalPersonModel.find({_id: {$in: naturalIds}});
        }

        if (organizationsIds) {
            organizationRelations = await OrganizationModel.find({_id: {$in: organizationsIds}});

            if (organizationRelations) {
                organizationRelations.forEach((org) => {
                    if (org.positions) {
                        const pos = org.positions.map((p) => p.referenceId);
                        positionIds = [...positionIds, ...pos];
                    }
                });

                positionIds = positionIds ? [...new Set(positionIds)] : [];
            }
        }

        if (positionIds) {
            positions = await NaturalPersonModel.find({_id: {$in: positionIds}});
        }

        fileReviewArchive.naturalRelations = naturalRelations;
        fileReviewArchive.organizationRelations = organizationRelations;
        fileReviewArchive.positions = positions;

        await fileReviewArchive.save();
        return fileReviewArchive;

    } catch (e) {
        console.log(e);
    }

}
