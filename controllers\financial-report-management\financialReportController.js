const FinancialReportModel = require('../../models/financialreport');
const ImportProcessInfoModel = require('../../models/importprocessinfo');
const CompanyModel = require('../../models/company').schema;
const MasterClientCodeModel = require("../../models/masterClientCode")
const MessageModel = require('../../models/message')
const ConfigurationModel = require('../../models/configuration');
const messageController = require("../../controllers/client-management/messageController");
const uploadController = require('../../controllers/financial-report-management/uploadReportFilesController');
const pdfController = require('../../controllers/financial-report-management/financialReportPdfController');
const exportReportController = require('../../controllers/financial-report-management/exportReportController');
const { STANDARD_DATE_FORMAT, STANDARD_DATETIME_FORMAT, CURRENCIES } = require('../../utils/constants');
const {
  AR_DEFAULT_SORTING_ITEMS,
  AR_COMPANY_DEFAULT_SORTING_ITEMS,
  REPORT_STATUS,
  REPORT_REQUEST_INFO_STATUS,
  REPORT_FORM_PAGES,
  FORM_COMPLETE_INCOME_EXPENSES_INPUT_NUMBER_VALIDATION_ERRORS,
  FORM_COMPLETE_ASSETS_LBT_INPUT_NUMBER_VALIDATION_ERRORS,
  ACCOUNTING_SERVICE_TYPES
} = require('../../utils/financialReportConstants');
const utils = require('../../utils/utils');
const httpConstants = require('http2').constants;
const moment = require("moment");
const xlsx = require("xlsx");
const { v4: uuidv4 } = require('uuid');
const { getContainerClient } = require('../../utils/azureStorage');
const intoStream = require("into-stream");


/**
 * Render the view for accounting records dashboard
 * @function getDashboard
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @param {function} next HTTP request function to continue the request when the process fail.
 * @return 
 */
exports.getDashboard = function (req, res, next) {
  try {

    const showAccountingSearchModule = req.session.authentication.isAccountingStandard || req.session.authentication.isAccountingSuperUser ||
      req.session.authentication.isAccountingProductOwner || req.session.authentication.isAccountingAccountant || req.session.authentication.isAccountingManager;
    const showQueueOfficerModule = req.session.authentication.isAccountingProductOwner || req.session.authentication.isAccountingAccountant || req.session.authentication.isAccountingManager;
    const showAccountingImportModule = req.session.authentication.isAccountingProductOwner || req.session.authentication.isAccountingManager;
    const showAccountingCompanyModule = req.session.authentication.isAccountingStandard || req.session.authentication.isAccountingSuperUser ||
      req.session.authentication.isAccountingProductOwner || req.session.authentication.isAccountingAccountant || req.session.authentication.isAccountingManager;

    res.render("financial-report-management/index", {
      user: req.session.user,
      title: "Accounting Records Dashboard",
      authentication: req.session.authentication,
      showAccountingSearchModule: showAccountingSearchModule,
      showQueueOfficerModule: showQueueOfficerModule,
      showAccountingImportModule: showAccountingImportModule,
      showAccountingCompanyModule: showAccountingCompanyModule

    });
  } catch (e) {
    console.log(e);
    next(e);
  }
};

/**
 * Render the view for accounting records search
 * @function getPaginatedSearch
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @param {function} next HTTP request function to continue the request when the process fail.
 * @return 
 */
exports.getPaginatedSearch = async function (req, res, next) {
  try {
    const authenticationGroups = req.session.authentication;
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;
    const skip = (page - 1) * pageSize;

    const sortByItem = AR_DEFAULT_SORTING_ITEMS.find((item) => item.code === req.query.sortBy);
    const sortType = parseInt(req.query.sortType) || -1;
    let sort = {};

    if (sortByItem) {
      sort[sortByItem.dbField] = sortType
    } else {
      sort["createdAt"] = sortType
    }

    const { dbQuery, viewFilters } = parseFinancialReportSearchQuery(req.query);

    const searchProjection = {
      _id: 1,
      createdBy: 1,
      companyData: 1,
      masterClientCode: 1,
      status: 1,
      createdAt: 1,
      submittedAt: 1,
      reportDetails: 1,
      payment: 1,
      financialPeriod: 1,
      reopened: 1,
      financialPeriodChanges: 1,
      requestedInformation: 1,
      officerDetails: 1,
      files: 1
      //companyInfo: 1
    };

    let matchStage = {
      ...dbQuery
    };
    if (req.query.moduleState === 'ACTIVE' || req.query.moduleState === 'INACTIVE') {

      const isModuleActive = req.query.moduleState === 'ACTIVE';

      matchStage['companyData.accountingRecordsModule.active'] = isModuleActive;
    }

    // Calculate total Documents 
    const totalDocuments = await FinancialReportModel.aggregate([
      //{
      //$lookup: {
      //  from: 'companies',
      //  localField: 'companyData.code',
      //  foreignField: 'code',
      //  as: 'companyInfo',
      //},
      //},
      //{ $unwind: '$companyInfo' },
      { $match: matchStage },
      {
        $count: 'total'
      }
    ])
    const total = totalDocuments.length > 0 ? totalDocuments[0].total : 0
    // Calculate the total of pages
    const totalPages = Math.ceil(total / pageSize);
    // GET total of paginated items
    const financialReportResults = await FinancialReportModel.aggregate([

      //{
      //  $lookup: {
      //    from: 'companies', 
      //    localField: 'companyData.code', 
      //    foreignField: 'code', 
      //    as: 'companyInfo', 
      //  },
      //},
      //{ $unwind: '$companyInfo' },
      { $match: matchStage },
      { $sort: sort },
      { $skip: skip },
      { $limit: pageSize },
      { $project: searchProjection }
    ]);

    const actionStatusList = [REPORT_STATUS.CONFIRMED, REPORT_STATUS.UNDER_REVIEW, REPORT_STATUS.PAID, REPORT_STATUS.REQUEST_HELP, REPORT_STATUS.HELP_PROGRESS, REPORT_STATUS.HELP_COMPLETED]

    const dbResults = await Promise.all(financialReportResults.map(async function (report) {
      const allowActions = actionStatusList.includes(report.status);
      const showDetails = (report.reopened?.details?.length > 0 || report.requestedInformation?.details?.length > 0 || report.financialPeriodChanges?.details?.length > 0 || report.officerDetails?.details?.length > 0)
        ? true : false;

      const limitResetToSave = report.financialPeriod.end ? moment(report.financialPeriod.end).add(9, 'months') : null;
      const currentDate = moment(new Date());

      let allowResetChangePeriod = true;

      if (!authenticationGroups.isAccountingProductOwner && authenticationGroups.isAccountingSuperUser) {
        if (!limitResetToSave || (limitResetToSave && currentDate.isAfter(limitResetToSave))) {
          allowResetChangePeriod = false;
        }
      }

      let reportStatusLbl = report.status;

      if (report.reportDetails &&
        (report.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_COMPLETE || report.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE) &&
        report.status === REPORT_STATUS.CONFIRMED) {
        reportStatusLbl = "COMPLETED";
      }

      const pastReports = await FinancialReportModel.countDocuments({ 'companyData.code': report.companyData.code, status: { $ne: REPORT_STATUS.DELETED } })

      //need to identity if the current report for this company is the latest
      let enableChangePeriod = false
      if (pastReports === 1) {
        //if there is only 1 submission, we know for sure that this is the first and current submission, so
        //change financial period should be allowed
        enableChangePeriod = true;
      } else {
        //in case there are more submission, we need to see if the current one is the latest one
        const latestReport = await FinancialReportModel.findOne({ 'companyData.code': report.companyData.code, status: { $ne: REPORT_STATUS.DELETED } }, { _id: 1 }, { sort: { 'financialPeriod.end': -1 } })
        console.log(latestReport);
        console.log(report._id);
        if (latestReport._id.toString() == report._id.toString()) {
          enableChangePeriod = true;

        }
        console.log(enableChangePeriod)
      }
      return {
        _id: report._id,
        email: report.createdBy,
        companyName: report.companyData.name,
        code: report.companyData.code,
        masterClientCode: report.masterClientCode,
        incorporationCode: report.companyData.incorporationcode,
        incorporationDate: report.companyData.incorporationdate,
        status: reportStatusLbl,
        createdAt: report.createdAt,
        submittedAt: report.submittedAt,
        financialPeriodStart: report.financialPeriod ? report.financialPeriod.start : null,
        financialPeriodEnd: report.financialPeriod ? report.financialPeriod.end : null,
        referralOffice: report.companyData.referral_office,
        isExemptCompany: report.reportDetails?.isExemptCompany,
        accountingModule: report.companyData.accountingRecordsModule.active,
        isPaid: report.payment && report.payment.paidAt ? true : false,
        enableResetToSaved: allowActions && allowResetChangePeriod,
        enableChangePeriod: enableChangePeriod && allowResetChangePeriod && report.status !== REPORT_STATUS.IN_PENALTY,
        enableInfoRequest: allowActions,
        enableRequestCancel: report.status === REPORT_STATUS.REQUEST_INFO,
        enablePdfDownload: allowActions || report.status === REPORT_STATUS.REQUEST_INFO || report.status === REPORT_STATUS.IN_PENALTY,
        showInfoDetails: showDetails,
        isFirstReport: pastReports === 1,
        showAttachmentsDownload: report.files &&
          (report.files.exemptEvidenceFiles?.length > 0 || report.files.copyResolutionFiles?.length > 0 ||
            report.files.accountingRecordFiles?.length > 0 || report.files.annualReturnFiles?.length > 0)
      };
    }))

    const paginatedResponse = {
      "pageNumber": page,
      "pageCount": totalPages,
      "pageSize": pageSize,
      "totalItemCount": total,
      "hasPrevious": page > 1,
      "hasNext": page < totalPages,
      "sorting": {
        sortBy: req.query.sortBy,
        type: sortType,
        options: AR_DEFAULT_SORTING_ITEMS
      },
      "data": dbResults
    }

    res.render("financial-report-management/search-paginated", {
      user: req.session.user,
      title: "Accounting Records Search",
      result: paginatedResponse,
      filters: viewFilters,
      authentication: req.session.authentication,
      STANDARD_DATE_FORMAT,
      permissions: {
        resetToSavedActions: authenticationGroups.isAccountingSuperUser || authenticationGroups.isAccountingProductOwner ||
          authenticationGroups.isAccountingAccountant || authenticationGroups.isAccountingManager,
        exportFiles: authenticationGroups.isAccountingSuperUser || authenticationGroups.isAccountingProductOwner || authenticationGroups.isAccountingStandard ||
          authenticationGroups.isAccountingAccountant || authenticationGroups.isAccountingManager,
        changePeriod: authenticationGroups.isAccountingSuperUser || authenticationGroups.isAccountingProductOwner ||
          authenticationGroups.isAccountingAccountant || authenticationGroups.isAccountingManager,
        requestInfoActions: authenticationGroups.isAccountingProductOwner || authenticationGroups.isAccountingAccountant || authenticationGroups.isAccountingManager,
        downloadAttachmentsActions: authenticationGroups.isAccountingProductOwner || authenticationGroups.isAccountingAccountant || authenticationGroups.isAccountingManager,
        downloadSummaryPDF: authenticationGroups.isAccountingStandard || authenticationGroups.isAccountingSuperUser || authenticationGroups.isAccountingProductOwner ||
          authenticationGroups.isAccountingAccountant || authenticationGroups.isAccountingManager,
      }

    });
  } catch (e) {
    console.log(e);
    next(e);
  }

};


exports.exportXlsxFromPaginatedSearch = async function (req, res, next) {
  try {
    const filters = req.body;

    const sortByItem = AR_DEFAULT_SORTING_ITEMS.find((item) => item.code === filters.sortBy);
    const sortType = parseInt(filters.sortType) || -1;
    let sort = {};

    if (sortByItem) {
      sort[sortByItem.dbField] = sortType
    } else {
      sort["createdAt"] = sortType
    }

    const { dbQuery } = parseFinancialReportSearchQuery(filters);

    const searchProjection = {
      _id: 1,
      createdBy: 1,
      companyData: 1,
      masterClientCode: 1,
      status: 1,
      createdAt: 1,
      submittedAt: 1,
      reportDetails: 1,
      payment: 1,
      financialPeriod: 1
    };

    const financialReportResults = await FinancialReportModel.aggregate([
      { $match: dbQuery },
      { $sort: sort },
      { $project: searchProjection },
    ]);

    const exportXlsx = await exportReportController.generatePaginatedSearchXLSX(financialReportResults);
    if (exportXlsx === null) {
      const err = new Error('There was an error generating the file.');
      err.status = httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR;
      return next(err);
    }

    res.attachment(`AR_SEARCH_${moment().format("MM_DD_YYYY_HH_mm")}.xlsx`);
    res.send(exportXlsx);
    return res;
  } catch (e) {
    console.log("err ", e);
    console.log(e);
    return next(e);
  }
}



/**
 * Render the view for accounting records officer requests queue
 * @function getPaginatedSearch
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @param {function} next HTTP request function to continue the request when the process fail.
 * @return 
 */
exports.getPaginatedOfficerRequests = async function (req, res, next) {
  try {
    const authenticationGroups = req.session.authentication;
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;
    const skip = (page - 1) * pageSize;

    const sortByItem = AR_DEFAULT_SORTING_ITEMS.find((item) => item.code === req.query.sortBy);
    const sortType = parseInt(req.query.sortType) || -1;
    let sort = {};

    if (sortByItem) {
      sort[sortByItem.dbField] = sortType
    } else {
      sort["submittedAt"] = sortType
    }

    const { dbQuery, viewFilters } = parseOfficerQueueQuery(req.query, req.session.user.username);

    const searchProjection = {
      _id: 1,
      companyData: 1,
      masterClientCode: 1,
      status: 1,
      files: 1,
      createdAt: 1,
      submittedAt: 1,
      reportDetails: 1,
      financialPeriod: 1,
      officerDetails: 1,
      "companyAccountingModule": "$company.accountingRecordsModule"
    };

    // Calculate total Documents 
    const totalDocuments = await FinancialReportModel.countDocuments(dbQuery)
    // Calculate the total of pages
    const totalPages = Math.ceil(totalDocuments / pageSize);
    // GET total of paginated items
    const financialReportResults = await FinancialReportModel.aggregate([
      { $match: dbQuery },
      { $sort: sort },
      { $skip: skip },
      { $limit: pageSize },
      {
        $lookup: {
          from: "companies", // Name of the company collection
          localField: "companyData.code",
          foreignField: "code",
          as: "company"
        }
      },
      { $unwind: "$company" },
      { $project: searchProjection },
    ]);

    const actionStatusList = [
      REPORT_STATUS.CONFIRMED,
      REPORT_STATUS.IN_PENALTY,
      REPORT_STATUS.UNDER_REVIEW,
      REPORT_STATUS.PAID,
      REPORT_STATUS.REQUEST_INFO,
      REPORT_STATUS.REQUEST_HELP,
      REPORT_STATUS.HELP_PROGRESS,
      REPORT_STATUS.HELP_COMPLETED
    ]


    const dbResults = financialReportResults.map((report) => {
      const enableAttachmentsDownload = report.files && (report.files.annualReturnFiles?.length > 0 || report.files.accountingRecordFiles?.length > 0 || report.files.exemptEvidenceFiles?.length > 0 || report.files.copyResolutionFiles?.length > 0);
      const enablePenaltyAssign = report.status === REPORT_STATUS.IN_PENALTY && report.companyAccountingModule?.isReported === true;
      return {
        _id: report._id,
        companyName: report.companyData.name,
        incorporationNumber: report.companyData.incorporationcode,
        masterClientCode: report.masterClientCode,
        status: report.status,
        createdAt: report.createdAt,
        submittedAt: report.submittedAt,
        financialPeriodStart: report.financialPeriod ? report.financialPeriod.start : null,
        financialPeriodEnd: report.financialPeriod ? report.financialPeriod.end : null,
        canPick: report.status === REPORT_STATUS.REQUEST_HELP || (enablePenaltyAssign && report.officerDetails?.currentOfficer === null),
        canOpen: report.status === REPORT_STATUS.HELP_PROGRESS,
        enablePenaltyAssign: enablePenaltyAssign,
        enablePenaltyUnassign: report.status === REPORT_STATUS.IN_PENALTY && (report.officerDetails?.currentOfficer && report.officerDetails?.details.length > 0),
        enableAttachmentsDownload: enableAttachmentsDownload,
        enableApprovalFlow: report.status === REPORT_STATUS.UNDER_REVIEW && enableAttachmentsDownload,
        enablePdfDownload: actionStatusList.includes(report.status),
        allowReassignment: report.status === REPORT_STATUS.REQUEST_HELP || report.status === REPORT_STATUS.HELP_PROGRESS,
        currentOfficer: report.officerDetails?.currentOfficer || ""
      };
    })

    const paginatedResponse = {
      "pageNumber": page,
      "pageCount": totalPages,
      "pageSize": pageSize,
      "totalItemCount": totalDocuments,
      "hasPrevious": page > 1,
      "hasNext": page < totalPages,
      "sorting": {
        sortBy: req.query.sortBy,
        type: sortType,
        options: AR_DEFAULT_SORTING_ITEMS
      },
      "data": dbResults
    }
    const statistics = await getOfficerQueueStatistics(req.session.user.username);

    let filterUsers = await FinancialReportModel.distinct("officerDetails.currentOfficer");

    if (!filterUsers.includes(req.session.user.username)) {
      filterUsers = [req.session.user.username, ...filterUsers]
    }

    res.render("financial-report-management/requests-list", {
      user: req.session.user,
      title: "Accounting Records Officer Requests",
      result: paginatedResponse,
      statistics,
      filters: viewFilters,
      authentication: req.session.authentication,
      STANDARD_DATE_FORMAT,
      filterUsers,
      permissions: {
        searchAll: authenticationGroups.isAccountingManager || authenticationGroups.isAccountingProductOwner,
        searchExemptedCompanies: authenticationGroups.isAccountingManager || authenticationGroups.isAccountingProductOwner || authenticationGroups.isAccountingAccountant,
        assignRequest: authenticationGroups.isAccountingManager || authenticationGroups.isAccountingProductOwner,
        aproveRequest: authenticationGroups.isAccountingManager || authenticationGroups.isAccountingProductOwner || authenticationGroups.isAccountingAccountant,
      }
    });
  } catch (e) {
    console.log(e);
    next(e);
  }

};


exports.getCompanyDashboard = async function (req, res, next) {
  try {
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;
    const skip = (page - 1) * pageSize;

    const sortByItem = AR_COMPANY_DEFAULT_SORTING_ITEMS.find((item) => item.code === req.query.sortBy);

    const sortType = parseInt(req.query.sortType) || 1;
    let sort = {};

    if (sortByItem) {
      sort[sortByItem.dbField] = sortType
    } else {
      sort["code"] = sortType
    }

    const { dbQuery, viewFilters } = parseCompanyDashboardQuery(req.query);

    const lookupFinancialReports = {
      from: "financialreportsbspls",
      localField: "code",
      foreignField: "companyData.code",
      as: "financialReports",
    };

    const searchProjection = {
      _id: 1,
      code: 1,
      name: 1,
      referral_office: 1,
      masterclientcode: 1,
      incorporationcode: 1,
      incorporationdate: 1,
      accountingRecordsModule: 1,
      financialReports: "$financialReports",
      accountingRecordsReportModule: 1
    };

    let totalDocuments = 0;
    let accountingCompaniesResults = []

    // Calculate total Documents
    totalDocuments = await CompanyModel.countDocuments(dbQuery);

    // GET total of paginated items
    accountingCompaniesResults = await CompanyModel.aggregate([
      { $match: dbQuery },
      { $sort: sort },
      { $skip: skip },
      { $limit: pageSize },
      { $lookup: lookupFinancialReports },
      { $project: searchProjection },
    ]);

    // Calculate the total of pages
    const totalPages = Math.ceil(totalDocuments / pageSize);

    const config = await ConfigurationModel.findOne({});
    const dbResults = accountingCompaniesResults.map((company) => mapCompanyDashboardItem(company, config))

    const paginatedResponse = {
      "pageNumber": page,
      "pageCount": totalPages,
      "pageSize": pageSize,
      "totalItemCount": totalDocuments,
      "hasPrevious": page > 1,
      "hasNext": page < totalPages,
      "sorting": {
        sortBy: req.query.sortBy,
        type: sortType,
        options: AR_COMPANY_DEFAULT_SORTING_ITEMS
      },
      "data": dbResults
    }



    res.render("financial-report-management/company-dashboard", {
      user: req.session.user,
      title: "Accounting Company Dashboard",
      result: paginatedResponse,
      filters: viewFilters,
      authentication: req.session.authentication,
      STANDARD_DATE_FORMAT,
      STANDARD_DATETIME_FORMAT,
      lastUpdateDeadlineFunction: config.financialReportConfiguration?.lastUpdateCompanyCompliantStatusFunction || null,
      permissions: {
        allowReportUnreport: req.session.authentication.isAccountingProductOwner || req.session.authentication.isAccountingManager,
        allowSendEmail: req.session.authentication.isAccountingProductOwner || req.session.authentication.isAccountingManager,
        showInfo: req.session.authentication.isAccountingProductOwner || req.session.authentication.isAccountingSuperUser ||
          req.session.authentication.isAccountingAccountant || req.session.authentication.isAccountingManager,
        exportFiles: req.session.authentication.isAccountingProductOwner || req.session.authentication.isAccountingSuperUser ||
          req.session.authentication.isAccountingAccountant || req.session.authentication.isAccountingManager,
      }
    });
  } catch (e) {
    console.log(e);
    next(e);
  }

}

exports.exportXlsxFromCompanyDashboardSearch = async function (req, res, next) {
  try {
    const filters = req.body;

    const sortByItem = AR_COMPANY_DEFAULT_SORTING_ITEMS.find((item) => item.code === filters.sortBy);

    const sortType = parseInt(filters.sortType) || 1;
    let sort = {};

    if (sortByItem) {
      sort[sortByItem.dbField] = sortType
    } else {
      sort["code"] = sortType
    }

    const { dbQuery } = parseCompanyDashboardQuery(filters);
    console.log("dbQuery:", dbQuery)

    const lookupFinancialReports = {
      from: "financialreportsbspls",
      localField: "code",
      foreignField: "companyData.code",
      as: "financialReports",
    };

    const searchProjection = {
      _id: 1,
      code: 1,
      name: 1,
      referral_office: 1,
      masterclientcode: 1,
      incorporationcode: 1,
      incorporationdate: 1,
      accountingRecordsModule: 1,
      financialReports: "$financialReports",
      accountingRecordsReportModule: 1
    };

    let accountingCompaniesResults = []
    console.log(Object.keys(dbQuery).length)
    // Calculate total Documents
    if (Object.keys(dbQuery).length > 0) {

      // GET total of paginated items
      accountingCompaniesResults = await CompanyModel.aggregate([
        { $match: dbQuery },
        { $sort: sort },
        { $lookup: lookupFinancialReports },
        { $project: searchProjection },
      ]);
    }

    const config = await ConfigurationModel.findOne({});
    const dbResults = accountingCompaniesResults.map((company) => mapCompanyDashboardItem(company, config))

    const exportXlsx = await exportReportController.generateAccountingCompanyDashboardSearchXLSX(dbResults);
    if (exportXlsx === null) {
      const err = new Error('There was an error generating the file.');
      err.status = httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR;
      return next(err);
    }

    res.attachment(`AR_COMPANY_SEARCH_${moment().format("MM_DD_YYYY_HH_mm")}.xlsx`);
    res.send(exportXlsx);
    return res;
  } catch (e) {
    console.log("err ", e);
    console.log(e);
    return next(e);
  }
}

/**
 * Render the view for accounting records search
 * @function getReportFormPage
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @param {function} next HTTP request function to continue the request when the process fail.
 * @return 
 */
exports.getReportFormPage = async function (req, res, next) {
  try {
    let report = await FinancialReportModel.findById(req.params.reportId);
    if (!report) {
      const err = new Error('Financial Report not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }
    res.render("financial-report-management/report-form", {
      user: req.session.user,
      title: "Accounting records - Edit report",
      report: report,
      authentication: req.session.authentication,
      currencies: CURRENCIES,
      STANDARD_DATE_FORMAT
    });
  } catch (e) {
    console.log(e);
    next(e);
  }

};

/**
 * Render the view for accounting records search
 * @function getReportFormPage
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @param {function} next HTTP request function to continue the request when the process fail.
 * @return 
 */
exports.saveReportFormPage = async function (req, res) {
  try {
    let report = await FinancialReportModel.findById(req.params.reportId);

    if (!report) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        message: "Financial Report not found"
      });
    }

    const validFormPagesNumbers = [REPORT_FORM_PAGES.INCOME_EXPENSES_PAGE, REPORT_FORM_PAGES.ASSETS_LIABILITIES_PAGE]
    if (!req.body.currentStepForm || !validFormPagesNumbers.includes(req.body.currentStepForm)) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        message: "Action not allowed for this financial report, please try again later"
      });
    }

    let updateValues = {};

    const formErrors = validateCompleteFormValues(req.body.currentStepForm, req.body || {});

    if (formErrors.length > 0) {
      return res.json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        message: "Invalid values",
        formErrors: formErrors
      });
    }

    if (req.body.currentStepForm === REPORT_FORM_PAGES.INCOME_EXPENSES_PAGE) {
      updateValues = {
        currency: req.body.completeReportCurrency,
        completeDetails: parseCompleteDetailsIncomeAndExpenses(req.body, report)
      }
    }

    if (req.body.currentStepForm === REPORT_FORM_PAGES.ASSETS_LIABILITIES_PAGE) {
      updateValues = {
        status: req.body.actionType === "COMPLETE" ? REPORT_STATUS.HELP_COMPLETED : REPORT_STATUS.HELP_PROGRESS,
        completeDetails: parseCompleteDetailsAssetsAndLiabilities(req.body, report),
        modifiedBy: req.session.user.username
      }
    }
    await FinancialReportModel.findByIdAndUpdate(report._id, updateValues, { new: true });


    res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: `The financial report information has been ${req.body.actionType === "COMPLETE" ? "submitted" : "saved"} successfully`,
    });
  } catch (e) {
    console.log(e);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }

};


/**
 * Reset to saved a financial report.
 * @function processReportResetToSaved
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the result of the reset to save operation for the finacial report
 */
exports.processReportResetToSaved = async function (req, res) {
  try {
    let report = await FinancialReportModel.findById(req.params.reportId);
    let newStartDate;
    let newEndDate;

    if (!report) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Financial Report not found"
      });
    }
    const allowActionsStatusList = [REPORT_STATUS.CONFIRMED, REPORT_STATUS.UNDER_REVIEW, REPORT_STATUS.PAID, REPORT_STATUS.REQUEST_HELP, REPORT_STATUS.HELP_PROGRESS, REPORT_STATUS.HELP_COMPLETED]
    if (!allowActionsStatusList.includes(report.status)) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Action not allowed for this financial report, please try again later"
      });
    }

    if (!req.body.reason || req.body.reason === "") {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please provide the reason to re-open the submission"
      });
    }
    else if (req.body.reason && req.body.reason.length < 50) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please provide more information."
      });
    }

    if (req.body.changePeriodDate === null || req.body.changePeriodDate === undefined) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please make a selection to correct the financial period"
      });
    }

    let updateCompanyInitialPeriod = false;

    if (req.body.changePeriodDate === true) {
      if (!req.body.newFinancialStartDate || !req.body.newFinancialEndDate) {
        return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
          status: httpConstants.HTTP_STATUS_BAD_REQUEST,
          error: "Please provide the correct financial start/end dates"
        });
      }
      newStartDate = moment(req.body.newFinancialStartDate).utc().toDate();
      newEndDate = moment(req.body.newFinancialEndDate).utc().toDate();

      if (newEndDate <= newStartDate) {
        return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
          status: httpConstants.HTTP_STATUS_BAD_REQUEST,
          error: "End of financial period must be greater than the start of the financial period"
        });
      }

      const maxDate = new Date(newStartDate);
      maxDate.setFullYear(newStartDate.getFullYear() + 1);

      if (newEndDate >= maxDate) {
        return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
          status: httpConstants.HTTP_STATUS_BAD_REQUEST,
          error: "End of financial period must be maximum 12 months after the start of the financial period"
        });
      }

      const companyReports = await FinancialReportModel.find({
        "companyData.code": report.companyData.code,
        "companyData.masterclientcode": report.companyData.masterclientcode,
        "_id": { "$ne": report._id },
        "financialPeriod.end": { "$lt": report.financialPeriod.end }
      }, {
        _id: 1,
        financialPeriod: 1
      })

      if (companyReports.length === 0) {
        updateCompanyInitialPeriod = true;
      }
    }

    let updateValues = {};
    if (report.reportDetails.isExemptCompany ||
      report.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_COMPLETE ||
      report.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ||
      report.status === REPORT_STATUS.REQUEST_HELP
    ) {
      updateValues = {
        status: report.reportDetails?.isExemptCompany === true ? "SAVED" : "RE-OPEN",
        reopened: report.reopened?.details ? report.reopened : { details: [] },
        modifiedBy: req.session.user.username
      }
    } else {
      updateValues = {
        status: REPORT_STATUS.REQUEST_HELP,
        reopened: report.reopened?.details ? report.reopened : { details: [] },
        modifiedBy: req.session.user.username
      }
    }

    if (parseFloat(report.version) === 1 && (report.reportDetails?.serviceType === ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_COMPLETE ||
      report.reportDetails?.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_COMPLETE ||
      report.reportDetails?.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_DROP)
    ) {
      const completeDetails = {
        income: {
          total: null,
          costOfSales: null,
        },
        expenses: {
          operatingExpenses: null,
          totalOtherExpenses: null,
          incomeTax: null,
          totalOfExpenses: null,
        },
        assets: {
          cashAmount: null,
          loansAndReceivables: null,
          investmentsAssetsAmount: null,
          fixedAssetsAmount: null,
          intangibleAssetsAmount: null,
          total: null,
          totalOtherAssets: null,
        },
        liabilities: {
          accountsPayable: null,
          longTermDebts: null,
          total: null,
          totalOtherLiabilities: null,
        },
        shareholderEquity: null,
        grossProfit: null,
        netIncome: null,
      }
      updateValues.completeDetails = completeDetails;
      updateValues.version = "2.0";
    }

    updateValues.reopened.details.push({
      reopenedAt: new Date(),
      reopenedBy: req.session.user.username,
      reason: req.body.reason,
      changeFinancialPeriodDates: req.body.changePeriodDate,
      oldStartDate: req.body.changePeriodDate === true ? report.financialPeriod.start : undefined,
      oldEndDate: req.body.changePeriodDate === true ? report.financialPeriod.end : undefined,
      newStartDate: req.body.changePeriodDate === true ? req.body.newFinancialStartDate : undefined,
      newEndDate: req.body.changePeriodDate === true ? req.body.newFinancialEndDate : undefined,
    });


    if (req.body.changePeriodDate === true) {
      updateValues.financialPeriod = {
        start: req.body.newFinancialStartDate,
        end: req.body.newFinancialEndDate
      }
    }

    await FinancialReportModel.findByIdAndUpdate(report._id, updateValues);

    if (req.body.changePeriodDate === true && updateCompanyInitialPeriod === true) {
      await CompanyModel.findOneAndUpdate({
        "code": report.companyData.code,
        "masterclientcode": report.companyData.masterclientcode
      },
        {
          "$set": {
            "accountingRecordsModule.firstFinancialPeriodStart": updateValues.financialPeriod.start,
            "accountingRecordsModule.firstFinancialPeriodEnd": updateValues.financialPeriod.end,
          }
        });
    }

    res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "The financial report has been reset to saved successfully",
    });

  }
  catch (e) {
    console.log(e);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
}

/**
 * Reset to saved multiple financial reports.
 * @function processBulkReportResetToSaved
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the log file of the list of entries re-opened
 */
exports.processBulkReportResetToSaved = async function (req, res) {
  try {

    if (!req.body.reason || req.body.reason === "") {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please provide the reason to re-open the submissions"
      });
    }
    else if (req.body.reason && req.body.reason.length < 50) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please provide more information in the note for the client."
      });
    }

    let reports = await FinancialReportModel.find({ _id: { "$in": req.body.entryIds } }, { _id: 1, companyData: 1, status: 1, reopened: 1, reportDetails: 1 });

    if (reports.length === 0) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Financial Reports not found"
      });
    }

    const contentMessage = 'Dear Client,\n\n' +
      'Your request to re-open the accounting record submission for editing has been processed. The submission has reverted to "RE-OPEN" status in the submission overview.\n' +
      'To edit the submission, please select click the attached URL below. A note from your Trident Trust Officer can be viewed by clicking the information icon. Once you are finished editing, please ensure that you have submitted the amended submission prior to logging out or closing the browser.\n\n' +
      'Thank you,\n' +
      'Trident Economic Substance Team';

    let logfileRows = [["ID", "COMPANY", "DATE", "ACTION"]]

    const validStatus = [REPORT_STATUS.CONFIRMED, REPORT_STATUS.UNDER_REVIEW, REPORT_STATUS.PAID, REPORT_STATUS.REQUEST_HELP, REPORT_STATUS.HELP_PROGRESS, REPORT_STATUS.HELP_COMPLETED]

    for (let i = 0; i < reports.length; i++) {
      const report = reports[i];

      if (!validStatus.includes(report.status)) {
        logfileRows.push([report._id.toString(), report.companyData?.code || "", new Date(), 'INVALID ACTION']);
        continue;
      }

      let reopenedObject = !report.reopened?.details ? { details: [] } : report.reopened;

      reopenedObject.details.push({
        reopenedAt: new Date(),
        reopenedBy: req.session.user.username,
        reason: req.body.reason,
        changeFinancialPeriodDates: false,
        oldStartDate: undefined,
        oldEndDate: undefined,
        newStartDate: undefined,
        newEndDate: undefined,
      });

      const updateValues = {
        status: report.reportDetails?.isExemptCompany === true ? "SAVED" : "RE-OPEN",
        reopened: reopenedObject,
        modifiedBy: req.session.user.username
      }

      const updated = await FinancialReportModel.findByIdAndUpdate(report._id, updateValues, { new: true });

      if (updated) {
        const urls = [{
          url: `${process.env.CLIENTPORTAL_APP_HOST}/masterclients/${report.companyData.masterclientcode}/financial-reports/companies/${report.companyData.code}`,
          name: 'Company submissions',
        }];

        const emailSubject = "Accounting Records Request - " + report.companyData.code + "\r\n" +
          "Re-open submission - Change Required";

        await messageController.createAnnouncementByMCC(report.companyData.masterclientcode,
          "Accounting Records Request – Change required",
          emailSubject,
          contentMessage,
          urls,
          []);
        logfileRows.push([report._id.toString(), report.companyData?.code || "", new Date(), 'SUCCESS']);
      } else {
        logfileRows.push([report._id.toString(), report.companyData?.code || "", new Date(), 'ERROR UPDATING']);
      }
    }


    const workbook = xlsx.utils.book_new();
    const ws = xlsx.utils.aoa_to_sheet(logfileRows)
    xlsx.utils.book_append_sheet(workbook, ws, 'Sheet1');
    const xlsxData = xlsx.write(workbook, { bookType: 'xlsx', type: 'base64' });


    res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "The bulk reset to saved has been done successfully",
      data: {
        xlsxData: xlsxData,
        filename: "ac_rts_bulk_log_" + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx'
      }
    });
  } catch (err) {
    console.log(err);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
};

/**
 * Reset to saved multiple financial reports.
 * @function processBulkReportAssignOfficer
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the log file of the list of entries re-opened
 */
exports.processBulkReportAssignOfficer = async function (req, res) {
  try {

    if (req.body.newOfficer === "" || !utils.validateEmailFormat(req.body.newOfficer)) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please enter a valid email officer"
      });
    }

    let reports = await FinancialReportModel.find({ _id: { "$in": req.body.reportIds } }, { _id: 1, companyData: 1, status: 1, officerDetails: 1 });

    if (reports.length === 0) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Financial Reports not found"
      });
    }

    let logfileRows = [["ID", "COMPANY", "DATE", "ACTION"]]

    const validStatus = [REPORT_STATUS.REQUEST_HELP, REPORT_STATUS.HELP_PROGRESS, REPORT_STATUS.IN_PENALTY]

    for (let i = 0; i < reports.length; i++) {
      const report = reports[i];

      if (!validStatus.includes(report.status)) {
        logfileRows.push([report._id.toString(), report.companyData?.code || "", new Date(), 'INVALID ACTION']);
        continue;
      }

      if (report.status === REPORT_STATUS.IN_PENALTY) {
        const company = await CompanyModel.findOne({ code: report.companyData.code, masterclientcode: report.companyData.masterclientcode }, { _id: 1, accountingRecordsModule: 1 });
        if (!company?.accountingRecordsModule?.isReported !== true) {
          logfileRows.push([report._id.toString(), report.companyData?.code || "", new Date(), 'INVALID ACTION: unable to assign an officer, the company in penalty has not been reported yet']);
          continue;
        }
      }

      const officerDetailsList = report.officerDetails?.details ? report.officerDetails.details : []

      officerDetailsList.push({
        username: req.body.newOfficer,
        assignedBy: req.session.user.username,
        assignedAt: new Date(),
      })

      const updateValues = {
        status: report.status === REPORT_STATUS.IN_PENALTY ? report.status : REPORT_STATUS.HELP_PROGRESS,
        modifiedBy: req.session.user.username,
        officerDetails: {
          currentOfficer: req.body.newOfficer,
          details: officerDetailsList
        }
      }
      const updated = await FinancialReportModel.findByIdAndUpdate(report._id, updateValues, { new: true });

      if (updated) {
        logfileRows.push([report._id.toString(), report.companyData?.code || "", new Date(), 'SUCCESS']);
      } else {
        logfileRows.push([report._id.toString(), report.companyData?.code || "", new Date(), 'ERROR UPDATING']);
      }
    }


    const workbook = xlsx.utils.book_new();
    const ws = xlsx.utils.aoa_to_sheet(logfileRows)
    xlsx.utils.book_append_sheet(workbook, ws, 'Sheet1');
    const xlsxData = xlsx.write(workbook, { bookType: 'xlsx', type: 'base64' });


    res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "The bulk assign has been done successfully",
      data: {
        xlsxData: xlsxData,
        filename: "ac_assign_bulk_log_" + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx'
      }
    });
  } catch (err) {
    console.log(err);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
};

exports.processBulkReport = async function (req, res) {
  try {
    if (req.body.isReported === undefined || !req.body.companiesIds) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please enter valid data"
      });
    }

    let companies = await CompanyModel.find({ _id: { "$in": req.body.companiesIds } }, { _id: 1, code: 1, masterclientcode: 1, accountingRecordsModule: 1, incorporationcode: 1, name: 1 });
    if (companies.length === 0) {
      return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
        status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
        error: "Invalid Id's provided"
      });
    }

    let logfileRows = [["ID", "COMPANY", "DATE", "ACTION"]];
    const config = await ConfigurationModel.findOne({});


    for (let i = 0; i < companies.length; i++) {
      const company = companies[i];

      if (req.body.isReported) {
        if (company.accountingRecordsModule.isReported === true || !company.accountingRecordsModule.compliantStatus || company.accountingRecordsModule.compliantStatus === 'COMPLIANT') {
          logfileRows.push([company._id.toString(), company.name, new Date(), 'INVALID ACTION: company is already reported or has COMPLIANT status']);
          continue;
        }

        company.accountingRecordsModule.isReported = true
        company.accountingRecordsModule.reportedHistory.push({
          reportedAt: new Date(),
          reportedBy: req.session.user.username,
          isReported: true
        })
        await CompanyModel.findOneAndUpdate({ _id: company._id }, { accountingRecordsModule: company.accountingRecordsModule })
        logfileRows.push([company._id.toString(), company.name, new Date(), 'REPORTED']);
      }
      else {
        if (company.accountingRecordsModule.isReported === false || !company.accountingRecordsModule.compliantStatus || company.accountingRecordsModule.compliantStatus === 'COMPLIANT') {
          logfileRows.push([company._id.toString(), company.name, new Date(), 'INVALID ACTION: company is already unreported or has COMPLIANT status']);
          continue;

        }

        company.accountingRecordsModule.isReported = false;
        company.accountingRecordsModule.reportedHistory.push({
          reportedAt: new Date(),
          reportedBy: req.session.user.username,
          isReported: false
        })
        company.accountingRecordsModule.compliantStatus = 'COMPLIANT';

        // Recalculate deadline when unreporting a company (bulk operation)
        try {
          console.log(`Recalculating deadline for company ${company.code} after bulk unreporting`);

          // Fetch financial reports for the company
          const financialReports = await FinancialReportModel.find({
            'companyData.code': company.code,
            'companyData.masterclientcode': company.masterclientcode
          }, {
            _id: 1,
            status: 1,
            financialPeriod: 1,
            submittedAt: 1
          });

          // Calculate new deadline using the same logic as the function app
          const deadline = getAccountingDeadline(company.accountingRecordsModule, financialReports);

          if (deadline && deadline.newAccountingDeadline) {
            const currentDate = moment().utc();

            // Update deadline if it's different from current
            const currentDeadlineFormatted = company.accountingRecordsModule.currentDeadline ?
              moment(company.accountingRecordsModule.currentDeadline).utc().format('YYYY-MM-DD h') : null;
            const newDeadlineFormatted = deadline.newAccountingDeadline.format('YYYY-MM-DD h');

            if (!currentDeadlineFormatted || currentDeadlineFormatted !== newDeadlineFormatted) {
              company.accountingRecordsModule.currentDeadline = deadline.newAccountingDeadline.toDate();
              company.accountingRecordsModule.currentFilingDeadline = deadline.newFilingDeadline.toDate();

              // Calculate new penalty amount based on new deadline
              const daysToDeadline = deadline.newAccountingDeadline.diff(currentDate, 'days');
              let penalty = 0;

              if (daysToDeadline < 0) {
                penalty = getDeadlinePenalty(daysToDeadline, config.financialReportConfiguration?.penaltyRanges);
              }

              company.accountingRecordsModule.currentPenaltyAmount = penalty;

              logfileRows.push([company._id.toString(), company.name, new Date(), `DEADLINE UPDATED: ${deadline.newAccountingDeadline.toDate()}, Days: ${daysToDeadline}, Penalty: ${penalty}`]);
            }
          } else {
            logfileRows.push([company._id.toString(), company.name, new Date(), 'WARNING: Could not recalculate deadline - missing confirmed financial period']);
          }
        } catch (deadlineError) {
          console.error(`Error recalculating deadline for company ${company.code}:`, deadlineError);
          logfileRows.push([company._id.toString(), company.name, new Date(), `ERROR: Failed to recalculate deadline - ${deadlineError.message}`]);
        }

        if (company.accountingRecordsModule?.inPenalty === true) {
          company.accountingRecordsModule.inPenalty = false;

          let inPenaltyReport;

          if (company.accountingRecordsModule?.currentPenaltyReport) {
            inPenaltyReport = await FinancialReportModel.findById(company.accountingRecordsModule?.currentPenaltyReport, {
              _id: 1, status: 1, reportDetails: 1, reopened: 1, payment: 1, officerDetails: 1, penaltyDetails: 1
            })
          } else {
            inPenaltyReport = await FinancialReportModel.findOne({
              status: REPORT_STATUS.IN_PENALTY,
              "companyData.code": company.code,
              "companyData.masterclientcode": company.masterclientcode,
            }, { _id: 1, status: 1, reportDetails: 1, reopened: 1, payment: 1, officerDetails: 1, penaltyDetails: 1 })
          }

          if (inPenaltyReport) {
            // validate if the report in penalty have an officer assigned
            if (inPenaltyReport.officerDetails?.currentOfficer !== null && inPenaltyReport.officerDetails?.currentOfficer !== '') {
              logfileRows.push([company._id.toString(), company.name, new Date(), 'INVALID ACTION: the company in penalty has an officer assigned, so it cannot be unreported']);
              continue;
            }

            let newStatus = inPenaltyReport.status;
            let penalty = 0;

            const deadlineMomentDate = moment(company.accountingRecordsModule?.currentDeadline).utc();
            const deadlineDays = deadlineMomentDate.diff(moment().utc(), 'days');
            const todayPenalty = getDeadlinePenalty(deadlineDays, config.financialReportConfiguration?.penaltyRanges);

            penalty = company.accountingRecordsModule?.currentPenaltyAmount === todayPenalty ? company.accountingRecordsModule?.currentPenaltyAmount : todayPenalty

            const penaltyDetails = {
              penaltyStartedAt: inPenaltyReport.penaltyDetails?.penaltyStartedAt,
              penaltyCompletedAt: moment().utc(),
              penaltyAmount: penalty,
              penaltyDays: deadlineDays
            }

            if (inPenaltyReport.payment?.paidAt && inPenaltyReport.payment?.reference && inPenaltyReport.reopened?.details?.length > 0) {
              newStatus = REPORT_STATUS.PAID;
            }
            else if (inPenaltyReport.reportDetails.isExemptCompany === true) {
              newStatus = REPORT_STATUS.UNDER_REVIEW;
            }
            else {
              newStatus = inPenaltyReport.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_COMPLETE ||
                inPenaltyReport.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_DROP ?
                REPORT_STATUS.REQUEST_HELP :
                REPORT_STATUS.CONFIRMED
            }

            await FinancialReportModel.findByIdAndUpdate(inPenaltyReport._id, {
              $set: {
                status: newStatus,
                penaltyDetails: penaltyDetails
              }
            });
          }

          company.accountingRecordsModule.currentPenaltyReport = null;
        }
        await CompanyModel.findOneAndUpdate({ _id: company._id }, { accountingRecordsModule: company.accountingRecordsModule })
        logfileRows.push([company._id.toString(), company.name, new Date(), 'UNREPORTED']);
      }
    }

    const workbook = xlsx.utils.book_new();
    const ws = xlsx.utils.aoa_to_sheet(logfileRows)
    xlsx.utils.book_append_sheet(workbook, ws, 'Sheet1');
    const xlsxData = xlsx.write(workbook, { bookType: 'xlsx', type: 'base64' });

    return res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: `The companies ${req.body.isReported ? 'has been reported successfully' : 'are no longer reported'}. <br> Please verify the details in the results file.`,
      resultFile: {
        xlsxData: xlsxData,
        filename: (req.body.isReported ? "ac_bulk_report_log_" : "ac_bulk_unreport_log_") + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx'
      }
    });

  } catch (e) {
    console.log(e);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
}

/**
 * Update fiscal period dates of a report
 * @function updateFinancialReportPeriod
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the result of the process
 */
exports.updateFinancialReportPeriod = async function (req, res) {
  try {
    let report = await FinancialReportModel.findById(req.params.reportId);
    let companiesReport = await FinancialReportModel.countDocuments({ 'companyData.name': report.companyData.name, 'companyData.code': report.companyData.code })
    let company = await CompanyModel.findOne({ 'code': report.companyData.code, 'name': report.companyData.name, 'incorporationCode': report.companyData.incorporationCode })
    let newStartDate;
    let newEndDate;
    let startDate;

    if (!report) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Financial Report not found"
      });
    }

    if (!req.body.newFinancialStartDate || !req.body.newFinancialEndDate) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please select financial period start/end dates"
      });
    }

    startDate = moment(report.financialPeriod.start).utc().toDate()

    newStartDate = moment(req.body.newFinancialStartDate, STANDARD_DATE_FORMAT).utc().toDate();
    newEndDate = moment(req.body.newFinancialEndDate, STANDARD_DATE_FORMAT).utc().toDate();


    //if the incorporation date < 1-1-2023 AND this is the first submission, then the start date must be in 2023
    if (newStartDate.toISOString() != startDate.toISOString()) {
      if (moment(company.incorporationdate).isBefore('01/01/2023') && companiesReport == 1 && !moment(newStartDate, 'MM/DD/YYYY').isBetween(moment('01/01/2023', 'MM/DD/YYYY'), moment('12/31/2023', 'MM/DD/YYYY'), null, '[]')) {
        return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
          status: httpConstants.HTTP_STATUS_BAD_REQUEST,
          error: "The Start Date must be within the 2023 year"
        });
      } else {
        if (companiesReport == 1 && moment(company.incorporationdate).isAfter('01/01/2023')) {
          return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
            status: httpConstants.HTTP_STATUS_BAD_REQUEST,
            error: "Updating the start date is not allowed"
          });
        }
      }

    }

    if (newEndDate.toISOString() <= newStartDate.toISOString()) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "End of financial period must be greater than the start of the financial period"
      });
    }

    let oneYearAfterStartDate = moment(newStartDate).add(12, 'months').toDate();

    if (newEndDate.toISOString() >= oneYearAfterStartDate.toISOString()) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "End of financial period must be within 12 months from the start of the financial period"
      });
    }

    let updateValues = {
      financialPeriodChanges: report.financialPeriodChanges?.details ? report.financialPeriodChanges : { details: [] },
      financialPeriod: {
        start: newStartDate,
        end: newEndDate
      },
      modifiedBy: req.session.user.username
    }

    updateValues.financialPeriodChanges.details.push({
      dateChanged: new Date(),
      changedBy: req.session.user.username,
      oldStartDate: report.financialPeriod.start,
      oldEndDate: report.financialPeriod.end,
      newStartDate: newStartDate,
      newEndDate: newEndDate
    });

    let updateCompanyInitialPeriod = false;

    const companyReports = await FinancialReportModel.find({
      "companyData.code": report.companyData.code,
      "companyData.masterclientcode": report.companyData.masterclientcode,
      "_id": { "$ne": report._id },
      "financialPeriod.end": { "$lt": report.financialPeriod.end }
    }, {
      _id: 1,
      financialPeriod: 1
    })

    if (companyReports.length === 0) {
      updateCompanyInitialPeriod = true;
    }

    await FinancialReportModel.findByIdAndUpdate(report._id, updateValues);

    if (updateCompanyInitialPeriod === true) {
      await CompanyModel.findOneAndUpdate({
        "code": report.companyData.code,
        "masterclientcode": report.companyData.masterclientcode
      },
        {
          "$set": {
            "accountingRecordsModule.firstFinancialPeriodStart": updateValues.financialPeriod.start,
            "accountingRecordsModule.firstFinancialPeriodEnd": updateValues.financialPeriod.end,
          }
        });
    }

    res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "The financial report has been updated successfully",
    });
  } catch (err) {
    console.log(err);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
};


/**
 * Get the reopened, rfi details from financial report.
 * @function getReportInformationDetails
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the reportId, company and the reopened, financial period changes data
 */
exports.getReportInformationDetails = async function (req, res) {
  try {
    const report = await FinancialReportModel.findById(req.params.reportId);

    if (!report) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Financial Report not found"
      });
    }

    let requestedInfo = report.requestedInformation?.details || [];

    if (requestedInfo.length > 0 && report.clientResponseInformation?.details?.length > 0) {
      requestedInfo = requestedInfo.map((rfi) => {
        const rfiData = rfi.toObject();
        rfiData.client_response = report.clientResponseInformation.details.filter((clientResponse) => clientResponse.requestId === rfi.id);
        return rfiData;
      });

      requestedInfo.sort((firstRFI, nextRFI) => nextRFI.requestedAt.getTime() - firstRFI.requestedAt.getTime());
    }

    const reopenedInfo = report.reopened && report.reopened.details?.length > 0 ? report.reopened.details : [];
    if (reopenedInfo.length > 0) {
      reopenedInfo.sort((currentReopen, nextReopen) => nextReopen.reopenedAt.getTime() - currentReopen.reopenedAt.getTime());

      const lastReopened = reopenedInfo[0];
      if (report.status !== "RE-OPEN" && !lastReopened.resubmittedAt) {
        lastReopened.resubmittedAt = report.submittetAt;
        lastReopened.resubmittedBy = report.submittedBy;
        reopenedInfo[0] = lastReopened;
      }

    }
    res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      reportData: {
        reportId: report._id,
        company: report.companyData.code,
        financialPeriodChangesInfo: report.financialPeriodChanges?.details || [],
        requestedInfo,
        reopenedInfo,
        officerDetails: report.officerDetails
      }
    });
  } catch (e) {
    console.log("err ", e);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
};


/**
 * Assign report to other officer or auto assign
 * @function assignReportToOfficer
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the response of the process.
 */
exports.assignReportToOfficer = async function (req, res) {
  try {
    const report = await FinancialReportModel.findById(req.params.reportId);

    if (!report) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Financial Report not found"
      });
    }
    const validStatus = req.body.autoAssign === true ? [REPORT_STATUS.REQUEST_HELP, REPORT_STATUS.IN_PENALTY] :
      [REPORT_STATUS.REQUEST_HELP, REPORT_STATUS.HELP_PROGRESS, REPORT_STATUS.IN_PENALTY]

    if (!validStatus.includes(report.status)) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "This financial report cannot be picked at this time"
      });
    }

    if (req.body.autoAssign !== true && (req.body.newOfficer === "" || !utils.validateEmailFormat(req.body.newOfficer))) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please enter a valid email officer"
      });
    }

    if (report.status === REPORT_STATUS.IN_PENALTY) {
      const company = await CompanyModel.findOne({ code: report.companyData.code, masterclientcode: report.companyData.masterclientcode }, { _id: 1, accountingRecordsModule: 1 });
      if (company?.accountingRecordsModule?.isReported !== true) {
        return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
          status: httpConstants.HTTP_STATUS_BAD_REQUEST,
          error: "Unable to pick, this financial report is already assigned"
        });
      }

      if (req.body.autoAssign === true && report.officerDetails.currentOfficer !== null) {
        return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
          status: httpConstants.HTTP_STATUS_BAD_REQUEST,
          error: "Unable to assign an officer, the company in penalty has not been reported yet"
        });
      }
    }

    const newOfficerEmail = req.body.newOfficer ? req.body.newOfficer : req.session.user.username;
    const officerDetailsList = report.officerDetails?.details ? report.officerDetails.details : []


    officerDetailsList.push({
      username: newOfficerEmail,
      assignedBy: req.session.user.username,
      assignedAt: new Date(),
    })

    const updateValues = {
      status: report.status === REPORT_STATUS.IN_PENALTY ? report.status : REPORT_STATUS.HELP_PROGRESS,
      officerDetails: {
        currentOfficer: newOfficerEmail,
        details: officerDetailsList
      },
      modifiedBy: req.session.user.username
    }
    await FinancialReportModel.findByIdAndUpdate(report._id, updateValues);

    res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "The financial report has been assigned successfully",
      openReport: updateValues.status !== REPORT_STATUS.IN_PENALTY ? true : false
    });

  } catch (e) {
    console.log("err ", e);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
}


/**
 * Unassign a report in penalty from an officer 
 * @function unassignInPenaltyReport
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the response of the process.
 */
exports.unassignInPenaltyReport = async function (req, res) {
  try {
    const report = await FinancialReportModel.findById(req.params.reportId);

    if (!report) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Financial Report not found"
      });
    }

    if (report.status !== REPORT_STATUS.IN_PENALTY) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "This financial report cannot be unasigned at this time"
      });
    }

    if (report.officerDetails?.currentOfficer === null || report.officerDetails?.currentOfficer === '') {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "This financial report has not an officer assigned"
      });
    }

    const officerDetailsList = report.officerDetails?.details ? report.officerDetails.details : []


    officerDetailsList.push({
      username: 'UNASSIGNED',
      assignedBy: req.session.user.username,
      assignedAt: new Date(),
    })

    const updateValues = {
      officerDetails: {
        currentOfficer: null,
        details: officerDetailsList
      },
      modifiedBy: req.session.user.username
    }
    await FinancialReportModel.findByIdAndUpdate(report._id, updateValues);

    res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "The financial report has been unassigned successfully",
    });

  } catch (e) {
    console.log("err ", e);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
}


/**
 * Decline a report and reopened
 * @function declineReportRequest
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the result of the reset to save operation for the finacial report
 */
exports.declineReportRequest = async function (req, res) {
  try {
    let report = await FinancialReportModel.findById(req.params.reportId);

    if (!report) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Financial Report not found"
      });
    }
    const allowActionsStatusList = [REPORT_STATUS.REQUEST_HELP, REPORT_STATUS.HELP_PROGRESS]
    if (!allowActionsStatusList.includes(report.status)) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Action not allowed for this financial report, please try again later"
      });
    }

    if (!req.body.declineReason || req.body.declineReason === "") {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please provide the reason to decline the report"
      });
    }


    let updateValues = {
      status: "RE-OPEN",
      reopened: report.reopened?.details ? report.reopened : { details: [] },
      modifiedBy: req.session.user.username
    }

    updateValues.reopened.details.push({
      reopenedAt: new Date(),
      reopenedBy: req.session.user.username,
      reason: "The report with status " + report.status + " has been declined for the following reason: " + req.body.declineReason,
      changeFinancialPeriodDates: false,
      oldStartDate: undefined,
      oldEndDate: undefined,
      newStartDate: undefined,
      newEndDate: undefined
    });


    await FinancialReportModel.findByIdAndUpdate(report._id, updateValues);

    res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "The financial report has been declined and re-open successfully",
    });

  }
  catch (e) {
    console.log(e);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
}


/**
 * Create a new request information object for a report and change status to INFORMATION REQUEST.
 * @function startInfoRequest
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the response of the process.
 */
exports.startInfoRequest = async function (req, res) {
  try {
    const report = await FinancialReportModel.findById(req.params.reportId);

    if (!report) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Financial Report not found"
      });
    }

    const validStatus = [REPORT_STATUS.CONFIRMED, REPORT_STATUS.UNDER_REVIEW, REPORT_STATUS.PAID, REPORT_STATUS.REQUEST_HELP, REPORT_STATUS.HELP_PROGRESS, REPORT_STATUS.HELP_COMPLETED]

    if (!validStatus.includes(REPORT_STATUS.REQUEST_HELP)) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "This financial report does not permit information requests at the moment"
      });
    }

    let currentPendingRfi = false;
    const requestedInformationList = report.requestedInformation?.details || [];

    if (requestedInformationList.length > 0) {
      currentPendingRfi = requestedInformationList.some((r) => r.status !== 'CANCELLED' && r.status !== "RETURNED")
    }

    if (currentPendingRfi === true) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "There is an information request already pending."
      });
    }

    if (!req.body.requestComment || req.body.requestComment === "") {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please specify the information that the client has to provide."
      });
    }

    if (!req.body.deadLineDate || req.body.deadLineDate === "") {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please enter the request deadline."
      });
    }
    const rfiFiles = req.session.requestedTempRFIFiles && req.session.requestedTempRFIFiles[req.params.reportId] ?
      req.session.requestedTempRFIFiles[req.params.reportId] : [];

    let attachments = [];
    if (rfiFiles?.length > 3) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "The maximum of documents to send in the request information is three, please remove one."
      });
    }
    else if (rfiFiles?.length > 0 && rfiFiles.length <= 3) {
      const fileTypeId = uuidv4();
      rfiFiles.forEach((f) => {
        f.fileTypeId = fileTypeId;

        attachments.push({
          fileId: f.fileId,
          fileTypeId: f.fileTypeId,
          fieldName: f.fieldname,
          originalName: f.originalname,
          encoding: f.encoding,
          mimeType: f.mimetype,
          blobName: f.blobName,
          container: f.container,
          blob: f.blob,
          blobType: f.blobType,
          size: f.size,
          etag: f.etag,
          url: f.url,
        });
      })

    }

    const newRequestInfo = {
      username: req.session.user.username,
      requestedAt: new Date(),
      deadlineAt: moment(req.body.deadLineDate, 'MM/DD/YYYY').utc().format('YYYY-MM-DD'),
      comment: req.body.requestComment,
      status: REPORT_REQUEST_INFO_STATUS.REQUESTED,
      files: attachments
    };

    // create announcement

    const content = formatRfiEmailContent(
      report.companyData.name,
      moment(report.financialPeriod.end).utc().format('MMMM DD, YYYY'),
      moment(req.body.deadLineDate).utc().format('MMMM DD, YYYY'),
    )

    // todo send email
    console.log("content ", content)

    const requestedInfo = report.requestedInformation?.details || [];
    requestedInfo.push(newRequestInfo);

    const newValues = {
      previousStatus: report.status,
      status: REPORT_STATUS.REQUEST_INFO,
      requestedInformation: { details: requestedInfo },
      modifiedBy: req.session.user.username
    };

    await FinancialReportModel.findByIdAndUpdate(report._id, newValues);
    req.session.requestedTempRFIFiles = {};
    res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "The request information has been send successfully"
    });
  } catch (e) {
    console.log("err ", e);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
};

/**
 * Create a new request information object for a report and change status to INFORMATION REQUEST.
 * @function startInfoRequest
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the response of the process.
 */
exports.cancelInfoRequest = async function (req, res) {
  try {
    const report = await FinancialReportModel.findById(req.params.reportId);

    if (!report) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Financial Report not found"
      });
    }
    if (report.status !== REPORT_STATUS.REQUEST_INFO) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "The action you're attempting is invalid in the context of this financial report"
      });
    }

    if (!req.body.reason || req.body.reason === "") {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please provide the reason for canceling the current information request"
      });
    }

    let updateValues = {
      status: report.previousStatus,
      previousStatus: REPORT_STATUS.REQUEST_INFO,
      requestedInformation: report.requestedInformation,
      clientResponseInformation: report.clientResponseInformation?.details ? report.clientResponseInformation : { details: [] },
      modifiedBy: req.session.user.username
    };
    const requestedInformationList = report.requestedInformation?.details || [];

    const requestedInfoIndex = requestedInformationList.findIndex((rfi) => rfi.status === REPORT_REQUEST_INFO_STATUS.REQUESTED);
    if (requestedInfoIndex > -1) {
      const requestedInfo = requestedInformationList[requestedInfoIndex];
      requestedInfo.status = REPORT_REQUEST_INFO_STATUS.CANCELLED;
      const cancelInfo = {
        request_id: requestedInfo.id,
        username: req.session.user.username,
        returnedAt: new Date(),
        comment: req.body.reason,
        isCanceled: true,
        files: []
      };
      requestedInformationList[requestedInfoIndex] = requestedInfo;


      updateValues.requestedInformation = { details: requestedInformationList };
      updateValues.clientResponseInformation.details.push(cancelInfo);

    }

    await FinancialReportModel.findByIdAndUpdate(report._id, updateValues);
    res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "The request information has been cancelled successfully"
    });
  } catch (e) {
    console.log("err ", e);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
}

/**
 * Return the list of the files uploaded and saved temporarily in the session for a financial report RFI.
 * @function getReportTempRFIListFiles
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the list of files uploaded.
 */
exports.getReportTempRFIListFiles = async function (req, res) {
  try {
    const report = await FinancialReportModel.findById(req.params.reportId);

    if (!report) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Financial Report not found"
      });
    }

    let filesToReturn = [];

    if (req.session && req.session.requestedTempRFIFiles && req.session.requestedTempRFIFiles[req.params.reportId]) {
      filesToReturn = req.session.requestedTempRFIFiles[req.params.reportId];
    }
    return res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      files: filesToReturn
    });
  } catch (e) {
    console.log("err ", e);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
}

/**
 * Save in the session temporarily a information request document.
 * @function saveTemporalRfiReportFile
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the result of the upload process.
 */
exports.saveTemporalRfiReportFile = async function (req, res) {
  try {
    const report = await FinancialReportModel.findById(req.params.reportId);

    if (!report) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Financial Report not found"
      });
    }

    let sessData = req.session;
    if (!sessData.requestedTempRFIFiles) {
      sessData.requestedTempRFIFiles = {}
    }

    let uploadedFiles = req.files['fileUploaded'];
    let newFiles = [];

    if (uploadedFiles && uploadedFiles.length > 0) {
      for (let i = 0; i < uploadedFiles.length; i++) {
        const itemToUpload = uploadedFiles[i];

        if (itemToUpload.mimetype !== 'application/pdf') {
          console.log("Incorrect file type");
          return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
            status: httpConstants.HTTP_STATUS_BAD_REQUEST,
            message: "Incorrect file type"
          });
        }
        uploadController.moveUploadFRFile(req.params.reportId, itemToUpload, 'rfi-file').catch((reason => {
          if (reason) {
            console.log(reason);
            return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
              status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
              message: "Unexpected error uploading the document, please try again."
            });
          }
        }));

        let newFile = {
          fileId: uuidv4(),
          fileTypeId: null,
          fieldname: itemToUpload.fieldname.replace(/fileUploaded/i, 'rfi-file'),
          blob: itemToUpload.blob.replace(/fileUploaded/i, 'rfi-file'),
          blobName: itemToUpload.blobName.replace(/fileUploaded/i, 'rfi-file'),
          url: itemToUpload.url.replace(/fileUploaded/i, req.params.reportId + '/' + 'rfi-file'),
          originalname: itemToUpload.originalname,
          encoding: itemToUpload.encoding,
          mimetype: itemToUpload.mimetype,
          container: itemToUpload.container,
          blobType: itemToUpload.blobType,
          size: itemToUpload.size,
          etag: itemToUpload.etag
        };
        newFiles.push(newFile);
      }

      if (sessData.requestedTempRFIFiles && sessData.requestedTempRFIFiles[req.params.reportId]) {
        sessData.requestedTempRFIFiles[req.params.reportId] = [...sessData.requestedTempRFIFiles[req.params.reportId], ...newFiles];
      } else {
        sessData.requestedTempRFIFiles[req.params.reportId] = newFiles;
      }

    }
    return res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "File uploaded successfully"
    });

  } catch (e) {
    console.log("err ", e);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
}

/**
 * Delete a RFI file from a financial report or the session requestedTempRFIFiles.
 * @function deleteRFIReportFile
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the response of the delete process.
 */
exports.deleteRFIReportFile = async function (req, res) {
  try {
    const report = await FinancialReportModel.findById(req.params.reportId);
    let fileDeleted = false;

    if (!report) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Financial Report not found"
      });
    }


    if (req.body.requestId) {
      const requestedInformationList = report.requestedInformation?.details || [];
      const rfiInfoIndex = requestedInformationList.length > 0 ? requestedInformationList.findIndex((requestInfo) =>
        requestInfo.id === req.params.requestId) : -1;

      if (rfiInfoIndex > -1) {
        let rfiInfo = requestedInformationList[rfiInfoIndex];

        const fileIndex = rfiInfo.files?.length > 0 ? rfiInfo.files.findIndex((f) => f.fileId === req.body.fileId) : -1;
        if (fileIndex > -1) {
          rfiInfo.files.splice(fileIndex, 1);
          report.requestedInformation.details[rfiInfoIndex] = rfiInfo;
          report.markModified('requestedInformation');
          report.modifiedBy = req.session.user.username
          await report.save();
          fileDeleted = true;
        }
      }
    }

    if (!fileDeleted && req.session && req.session.requestedTempRFIFiles) {
      let tempFiles = req.session.requestedTempRFIFiles[req.params.reportId];
      if (tempFiles && tempFiles.length > 0) {
        const fileIndex = tempFiles.findIndex((f) => f.fileId && f.fileId === req.params.fileId);
        if (fileIndex > -1) {
          tempFiles.splice(fileIndex, 1);
          req.session.requestedTempRFIFiles[req.params.reportId] = tempFiles;
          fileDeleted = true;
        }
      }
    }


    if (fileDeleted) {
      return res.json({ status: httpConstants.HTTP_STATUS_OK, message: 'File deleted successfully' });
    }
    else {
      return res.json({ status: httpConstants.HTTP_STATUS_BAD_REQUEST, message: 'Error deleting the file' });
    }
  } catch (e) {
    console.log("err ", e);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
}

/**
 * Generate Summary PDF for financial report
 * @function downloadSummaryPdf
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return PDF file attached in the response
 */
exports.downloadSummaryPdf = async function (req, res) {
  try {
    const report = await FinancialReportModel.findById(req.params.reportId);

    return pdfController.generateFinancialReportPdf(report, res);
  } catch (error) {
    console.log("E ", error);
    return res.render('error', { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
  }
};

/**
 * Mark exempt company report as approved/rts by officer
 * @function approveReportForCompanyExempted
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return 
 */
exports.approveReportForCompanyExempted = async function (req, res) {
  try {
    const report = await FinancialReportModel.findById(req.params.reportId);

    if (!report) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Financial Report not found"
      });
    }

    if (req.body.isApproved === 'true') {
      report.status = REPORT_STATUS.CONFIRMED
      if (report.reportDetails.isExemptCompany) {
        report.exemptionApprovalValues = {
          email: req.session.user.username,
          approvedAt: new Date()
        }
      } else {
        report.changeFinancialPeriodRequestApprovalValues = {
          email: req.session.user.username,
          approvedAt: new Date()
        }
      }
    } else {
      console.log(report.reportDetails.isExemptCompany)
      if (report.reportDetails.isExemptCompany) {

        report.status = REPORT_STATUS.SAVED
      } else {
        const details = {
          reopenedAt: new Date(),
          reopenedBy: req.session.user.username,
          resubmittedAt: null,
          resubmittedBy: null,
          reason: req.body.reason,
          changeFinancialPeriodDates: false
        }

        report.status = REPORT_STATUS.RE_OPEN
        if (report.reopened && report.reopened?.details) {
          report.reopened.details.push(details)
        } else {
          report.reopened = { details: [details] }
        }
      }
      report.modifiedBy = req.session.user.username
    }

    await report.save()
    return res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "Report saved successfully"
    });

  } catch (e) {
    console.log("err ", e);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
}

exports.getImporter = function (req, res) {
  res.render("financial-report-management/import", { title: "Import File", STANDARD_DATE_FORMAT });
};

exports.getImporterHistory = async function (req, res, next) {
  try {
    const importProcessRecords = await ImportProcessInfoModel.aggregate([
      { "$match": { fileName: { "$regex": /^FinancialReport-/ } } },
      { "$sort": { createdAt: -1 } },
      { "$limit": 100 }
    ])

    const importFiles = [];

    for await (const importRecord of importProcessRecords) {

      importFiles.push({
        name: importRecord.fileName,
        importFilePath: importRecord.importFilePath,
        resultFilePath: importRecord.resultFilePath,
        status: importRecord.status,
        createdAt: moment(importRecord.createdAt).utc().format('YYYY-MM-DD HH:mm'),
        updatedAt: moment(importRecord.updatedAt).utc().format('YYYY-MM-DD HH:mm')
      });
    }

    res.render("financial-report-management/import-history", { title: "Import Reports ", importFiles });

  } catch (e) {
    console.log(e);
    next(e);
  }
}

exports.uploadImportReportFile = async function (req, res) {
  try {
    if (!req.files) {
      res.json({ error: "Files not found" });
      return;
    }
    const file = req.files.fileUploaded;
    const data = new Uint8Array(file.data);
    const workbook = xlsx.read(data, {
      type: "array",
      cellText: false,
      cellDates: true,
    });

    const worksheet = workbook.Sheets[workbook.SheetNames[0]];

    const rows = xlsx.utils.sheet_to_json(worksheet, {
      blankrows: false,
      header: ['column1', 'column2', 'column3', 'column4', 'column5', 'column6', 'column7', 'column8', 'column9',
        'column10', 'column11', 'column12', 'column13', 'column14', 'column15', 'column16', 'column17', 'column18', 'column19',
        'column20', 'column21', 'column22', 'column23', 'column24', 'column25', 'column26', 'column27', 'column28', 'column29'],
      raw: true,
      rawNumbers: true
    });

    if (rows.length < 1) {
      return res.json({ status: 400, error: "The file is empty" });
    }

    let title = rows.shift();
    if (title.column1 !== "Company name" ||
      title.column2 !== "Incorporation number" ||
      title.column3 !== "Incorporation date" ||
      title.column4 !== "Financial period start" ||
      title.column5 !== "Financial period end" ||
      title.column6 !== "Revenue" ||
      title.column7 !== "Cost of Sales" ||
      title.column8 !== "GROSS PROFIT" ||
      title.column9 !== "Operating Expenses" ||
      title.column10 !== "Other Expenses" ||
      title.column11 !== "Income Tax Expense" ||
      title.column12 !== "TOTAL EXPENSES" ||
      title.column13 !== "NET INCOME" ||
      title.column14 !== "Cash and cash equivalents" ||
      title.column15 !== "Loans and receivables" ||
      title.column16 !== "Investments and other financial assets" ||
      title.column17 !== "Tangible fixed assets" ||
      title.column18 !== "Intangible assets" ||
      title.column19 !== "Other assets" ||
      title.column20 !== "TOTAL ASSETS" ||
      title.column21 !== "Accounts Payable" ||
      title.column22 !== "Long-term debts" ||
      title.column23 !== "Other liabilities" ||
      title.column24 !== "TOTAL LIABILITIES" ||
      title.column25 !== "SHAREHOLDERS EQUITY" ||
      title.column26 !== "Name person doing declaration" ||
      title.column27 !== "Relation to entity" ||
      title.column28 !== "Phone number" ||
      title.column29 !== "Email"
    ) {
      return res.json({ status: 400, error: "Invalid data template!" });

    }


    const containerClient = getContainerClient(
      process.env.AZURE_STORAGE_CONTAINER_FINANCIAL_REPORTS_IMPORTS,
      process.env.AZURE_STORAGE_ACCOUNT,
      process.env.AZURE_STORAGE_ACCESS_KEY
    );

    const date = moment(new Date()).format('YYYY-MM-DD_HH-mm')
    const blobName = "pending-imports/FinancialReport-" + date + ".xlsx"
    const blob = {
      "name": blobName,
      "stream": intoStream(file.data),
      "size": file.size
    }

    const uploadResponse = await uploadStreamFileToContainer(containerClient, blob)

    const importLogObj = {
      fileName: blobName.split('pending-imports/')[1],
      importFilePath: blobName,
      totalRows: rows.length,
      importedBy: req.session.user.username,
      successRows: 0,
      errorRows: 0,
      retries: 0,
    }

    await ImportProcessInfoModel.create(importLogObj)

    if (uploadResponse.success === true) {
      return res.json({ status: 200, message: "Reports data has been uploaded successfully." })
    } else {
      return res.json({ status: 500, message: "An error ocurred uploading the import file." })
    }


  } catch (e) {
    console.log(e);
    return res.json({ error: "Internal error" });
  }
}

exports.downloadArchiveFile = async function (req, res, next) {
  try {
    const containerClient = getContainerClient(
      process.env.AZURE_STORAGE_CONTAINER_FINANCIAL_REPORTS_IMPORTS,
      process.env.AZURE_STORAGE_ACCOUNT,
      process.env.AZURE_STORAGE_ACCESS_KEY
    );

    const blobClient = containerClient.getBlobClient(req.query.pathname);

    if (req.query.pathname.length > 0) {
      blobClient.download().then((downloadResponse) => {
        if (downloadResponse.contentLength === 0) {
          return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).send({ "message": "File not found" });
        }
        downloadResponse.readableStreamBody.pipe(res);
      }).catch((error) => {
        console.error("Error downloading blob:", error);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).send({ "message": "Error downloading file" });
      });
    } else {
      const err = new Error('File not found');
      err.status = 404;
      return next(err);
    }
  } catch (e) {
    console.log("error ", e);
    const err = new Error('Internal Server Error');
    err.status = 502;
    return next(err);
  }
}

exports.reportCompany = async function (req, res) {
  try {

    const company = await CompanyModel.findOne({ _id: req.params.companyId })
    const config = await ConfigurationModel.findOne({});

    if (!company) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).send({ "message": "Error Company not found" });
    }

    const reportedObj = {
      isReported: req.body.isReported,
      reportedBy: req.session.user.username,
      reportedAt: new Date()
    }

    if (!company.accountingRecordsModule.reportedHistory) {
      company.accountingRecordsModule.reportedHistory = [];
    }

    if (req.body.isReported) {
      if (company.accountingRecordsModule.compliantStatus !== 'NON-COMPLIANT') {
        return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
          status: httpConstants.HTTP_STATUS_BAD_REQUEST,
          message: "The company is COMPLIANT, so can't be reported"
        });
      } else {
        company.accountingRecordsModule.isReported = true
        company.accountingRecordsModule.reportedHistory.push(reportedObj)
      }
    } else {
      if (company.accountingRecordsModule.isReported !== true) {
        return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
          status: httpConstants.HTTP_STATUS_BAD_REQUEST,
          message: "The company is not reported, so can't not be unreported"
        });
      } else {
        company.accountingRecordsModule.isReported = false
        company.accountingRecordsModule.reportedHistory.push(reportedObj)
        company.accountingRecordsModule.compliantStatus = 'COMPLIANT'

        // Recalculate deadline when unreporting a company
        try {
          console.log(`Recalculating deadline for company ${company.code} after unreporting`);

          // Fetch financial reports for the company
          const financialReports = await FinancialReportModel.find({
            'companyData.code': company.code,
            'companyData.masterclientcode': company.masterclientcode
          }, {
            _id: 1,
            status: 1,
            financialPeriod: 1,
            submittedAt: 1
          });

          console.log(`Found ${financialReports.length} financial reports for company ${company.code}`);

          // Calculate new deadline using the same logic as the function app
          const deadline = getAccountingDeadline(company.accountingRecordsModule, financialReports);

          if (deadline && deadline.newAccountingDeadline) {
            const currentDate = moment().utc();

            // Update deadline if it's different from current
            const currentDeadlineFormatted = company.accountingRecordsModule.currentDeadline ?
              moment(company.accountingRecordsModule.currentDeadline).utc().format('YYYY-MM-DD h') : null;
            const newDeadlineFormatted = deadline.newAccountingDeadline.format('YYYY-MM-DD h');

            if (!currentDeadlineFormatted || currentDeadlineFormatted !== newDeadlineFormatted) {
              console.log(`Updating deadline from ${currentDeadlineFormatted} to ${newDeadlineFormatted}`);

              company.accountingRecordsModule.currentDeadline = deadline.newAccountingDeadline.toDate();
              company.accountingRecordsModule.currentFilingDeadline = deadline.newFilingDeadline.toDate();

              // Calculate new penalty amount based on new deadline
              const daysToDeadline = deadline.newAccountingDeadline.diff(currentDate, 'days');
              let penalty = 0;

              if (daysToDeadline < 0) {
                penalty = getDeadlinePenalty(daysToDeadline, config.financialReportConfiguration?.penaltyRanges);
              }

              company.accountingRecordsModule.currentPenaltyAmount = penalty;

              console.log(`New deadline: ${deadline.newAccountingDeadline.toDate()}, Days to deadline: ${daysToDeadline}, Penalty: ${penalty}`);
            } else {
              console.log(`Deadline unchanged: ${newDeadlineFormatted}`);
            }
          } else {
            console.log(`Could not calculate deadline for company ${company.code} - missing confirmed financial period end date`);
          }
        } catch (deadlineError) {
          console.error(`Error recalculating deadline for company ${company.code}:`, deadlineError);
          // Continue with unreporting even if deadline calculation fails
        }

        if (company.accountingRecordsModule.inPenalty === true) {
          company.accountingRecordsModule.inPenalty = false;

          let inPenaltyReport;

          if (company.accountingRecordsModule?.currentPenaltyReport) {
            inPenaltyReport = await FinancialReportModel.findById(company.accountingRecordsModule?.currentPenaltyReport, {
              _id: 1, status: 1, reportDetails: 1, reopened: 1, payment: 1, officerDetails: 1, penaltyDetails: 1
            })
          } else {
            inPenaltyReport = await FinancialReportModel.findOne({
              status: REPORT_STATUS.IN_PENALTY,
              "companyData.code": company.code,
              "companyData.masterclientcode": company.masterclientcode,
            }, { _id: 1, status: 1, reportDetails: 1, reopened: 1, payment: 1, officerDetails: 1, penaltyDetails: 1 })
          }

          if (inPenaltyReport) {
            // validate if the report in penalty have an officer assigned
            if (inPenaltyReport.officerDetails?.currentOfficer !== null && inPenaltyReport.officerDetails?.currentOfficer !== '') {
              return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
                status: httpConstants.HTTP_STATUS_BAD_REQUEST,
                message: "The company in penalty has an officer assigned, so it cannot be unreported"
              });
            }

            let newStatus = inPenaltyReport.status;
            let penalty = 0;

            const deadlineMomentDate = moment(company.accountingRecordsModule?.currentDeadline).utc();
            const deadlineDays = deadlineMomentDate.diff(moment().utc(), 'days');
            const todayPenalty = getDeadlinePenalty(deadlineDays, config.financialReportConfiguration?.penaltyRanges);

            penalty = company.accountingRecordsModule?.currentPenaltyAmount === todayPenalty ? company.accountingRecordsModule?.currentPenaltyAmount : todayPenalty

            const penaltyDetails = {
              penaltyStartedAt: inPenaltyReport.penaltyDetails?.penaltyStartedAt,
              penaltyCompletedAt: moment().utc(),
              penaltyAmount: penalty,
              penaltyDays: deadlineDays
            }


            if (inPenaltyReport.payment?.paidAt && inPenaltyReport.payment?.reference && inPenaltyReport.reopened?.details?.length > 0) {
              newStatus = REPORT_STATUS.PAID;
            }
            else if (inPenaltyReport.reportDetails.isExemptCompany === true) {
              newStatus = REPORT_STATUS.UNDER_REVIEW;
            }
            else {
              newStatus = inPenaltyReport.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_COMPLETE ||
                inPenaltyReport.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_DROP ?
                REPORT_STATUS.REQUEST_HELP :
                REPORT_STATUS.CONFIRMED
            }


            await FinancialReportModel.findByIdAndUpdate(inPenaltyReport._id, {
              $set: {
                status: newStatus,
                penaltyDetails: penaltyDetails
              }
            });

          }

          company.accountingRecordsModule.currentPenaltyReport = null;
        }
      }
    }

    if (company.accountingRecordsModule.reportedHistory.length > 0) {
      await company.save()
    }


    return res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "The company has been updated sucessfully"
    });
  } catch (e) {
    console.log(e);
    return res.json({ error: "Internal error" });
  }
}

exports.showCompanyInformation = async function (req, res) {
  try {
    const company = await CompanyModel.findOne({ _id: req.params.companyId })

    if (!company) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).send({ "message": "Error Company not found" });
    }

    const financialReports = await FinancialReportModel.find({ 'companyData.code': company.code },
      { financialPeriod: 1, submittedAt: 1, status: 1 })
      .sort({ 'financialPeriod.end': 1 })

    const emails = await MessageModel.find({
      'masterClientCodes': { $in: [company.masterclientcode] },
      'sendToAll': { $exists: false }
    })


    const data = {
      financialReports: financialReports,
      companyData: company,
      emails: emails
    }

    return res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      data: data
    });

  } catch (e) {
    console.log(e);
    return res.json({ error: "Internal error" });
  }
}

exports.sendEmail = async function (req, res) {
  try {

    if (!req.body.content || !req.body.subject || !req.body.masterClientCodes || !req.body.emailSubject) {
      return res.status(400).json({
        status: 400,
        error: "Missing required fields"
      })
    }

    console.log(req.body.masterClientCodes)

    let masterClients;

    masterClients = await MasterClientCodeModel.find({ code: { "$in": req.body.masterClientCodes } }, { code: 1 });
    masterClients = masterClients.map((mcc) => {
      return mcc.code
    });

    const hoursPeriod = 60 * 60 * 1000;
    const currentDate = new Date();
    const scheduleAt = new Date(Math.ceil(currentDate.getTime() / hoursPeriod) * hoursPeriod)

    let newMessage = new MessageModel({
      subject: req.body.subject,
      emailSubject: req.body.emailSubject ? req.body.emailSubject : req.body.subject,
      content: req.body.content,
      masterClientCodes: masterClients,
      status: 'SCHEDULED',
      scheduleMessage: false,
      scheduledAt: scheduleAt
    });

    await newMessage.save();

    return res.status(200).json({
      status: 200,
      message: "Announcement created successfully"
    })



  } catch (e) {
    console.log(e);
    return res.json({ error: "Internal error" });
  }
}

async function uploadStreamFileToContainer(containerClient, blob) {
  return await new Promise((resolve, reject) => {
    const blockBlobClient = containerClient.getBlockBlobClient(blob.name);
    blockBlobClient.uploadStream(blob.stream, blob.size).then((result) => {
      resolve({ "success": true, "result": result });
    }).catch((error) => {
      reject({ "success": false, "error": error })
    });
  })
}

/**
 * Generate the query to search on db and the filters object to return to the the view of accounting records search
 * @function parseFinancialReportSearchQuery
 * @param {object}  filters filters object from view.
 * @return dbEntry, viewFilters
 */
function parseFinancialReportSearchQuery(filters) {
  const company = filters.companyName;
  const incorporationNumber = filters.incorporationNumber
  const mcc = filters.masterclientcode;
  const referralOffice = filters.referralOffice;
  const status = Object.keys(filters).length === 0 ? "ALL EXCEPT DELETED" : (filters.status || "ALL");
  let serviceType = filters.serviceType || "";
  let submittedDateRange = filters.submittedDateRange;
  let incorporatedDateRange = filters.incorporatedDateRange;
  let financialPeriodEndDateRange = filters.financialPeriodEndDateRange;
  let paymentDateRange = filters.paymentDateRange;
  const isExemptCompany = filters.isExemptCompany;
  const filterAlreadySubmitted = filters.alreadySubmitted || "ALL";
  const allowReopen = filters.allowReopen || "ALL";


  let dbQuery = {}
  let viewFilters = filters;

  if (company && company.length > 2) {
    dbQuery["companyData.name"] = { $regex: company, $options: "i" };
  }

  if (incorporationNumber && incorporationNumber.length > 2) {
    dbQuery["companyData.incorporationcode"] = { $regex: incorporationNumber, $options: "i" };
  }

  if (mcc && mcc.length > 2) {
    dbQuery["masterClientCode"] = { $regex: mcc, $options: "i" };
  }

  if (referralOffice && referralOffice.length > 2) {
    dbQuery["companyData.referral_office"] = { $regex: referralOffice, $options: "i" };
  }

  if (isExemptCompany && isExemptCompany == "YES") {
    serviceType = "";
    dbQuery["reportDetails.isExemptCompany"] = true;
  }


  if (status !== "ALL" && status !== "ALL EXCEPT DELETED") {

    viewFilters.status = status;
    let statusQuery = Array.isArray(status) ? status : [status];
    statusQuery = statusQuery.filter((s) => s !== "COMPLETED");


    if (status.includes(REPORT_STATUS.CONFIRMED) && !status.includes("COMPLETED")) {
      dbQuery["reportDetails.isExemptCompany"] = true;
    }
    else if (status.includes("COMPLETED") && !status.includes(REPORT_STATUS.CONFIRMED)) {
      statusQuery.push(REPORT_STATUS.CONFIRMED)
      dbQuery["reportDetails.serviceType"] = { "$in": [ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_COMPLETE, ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE] };
    }
    dbQuery["status"] = { "$in": statusQuery };


  }
  else if (status === "ALL EXCEPT DELETED") {
    dbQuery["status"] = { "$ne": "DELETED" };
    viewFilters.status = [
      REPORT_STATUS.SAVED, REPORT_STATUS.IN_PROGRESS, REPORT_STATUS.CONFIRMED, REPORT_STATUS.RE_OPEN, REPORT_STATUS.REQUEST_HELP,
      REPORT_STATUS.HELP_PROGRESS, REPORT_STATUS.HELP_COMPLETED, REPORT_STATUS.REQUEST_INFO, REPORT_STATUS.UNDER_REVIEW, REPORT_STATUS.IN_PENALTY, "COMPLETED"];
  }

  if (serviceType !== "") {
    if (Array.isArray(serviceType)) {
      dbQuery["reportDetails.serviceType"] = { "$in": serviceType };
      viewFilters.serviceType = serviceType
    } else {
      dbQuery["reportDetails.serviceType"] = serviceType;
      viewFilters.serviceType = [serviceType];
    }
  }

  if (submittedDateRange && submittedDateRange !== "") {
    // format range date is "YYYY-MM-DD - YYYY-MM-DD"
    submittedDateRange = submittedDateRange.split("-").map((date) => date.trim());
    let startDate;
    let endDate;
    if (submittedDateRange.length > 1) {
      startDate = moment(submittedDateRange[0], "MM/DD/YYYY").toDate();
      endDate = moment(submittedDateRange[1], "MM/DD/YYYY").toDate();
      viewFilters.submittedDateRange = { start: submittedDateRange[0], end: submittedDateRange[1] };
    } else {
      startDate = moment(submittedDateRange[0], "MM/DD/YYYY").toDate();
      endDate = moment(submittedDateRange[0], "MM/DD/YYYY").add(1, 'days').toDate();
      viewFilters.submittedDateRange = { start: submittedDateRange[0], end: submittedDateRange[0] };
    }
    dbQuery["submittedAt"] = { $gte: startDate, $lte: endDate }

  }

  if (incorporatedDateRange && incorporatedDateRange !== "") {
    // format range date is "YYYY-MM-DD - YYYY-MM-DD"
    incorporatedDateRange = incorporatedDateRange.split("-").map((date) => date.trim());
    let startDate;
    let endDate;
    if (incorporatedDateRange.length > 1) {
      startDate = moment(incorporatedDateRange[0], "MM/DD/YYYY").toDate();
      endDate = moment(incorporatedDateRange[1], "MM/DD/YYYY").toDate();
      viewFilters.incorporatedDateRange = { start: incorporatedDateRange[0], end: incorporatedDateRange[1] };
    } else {
      startDate = moment(incorporatedDateRange[0], "MM/DD/YYYY").toDate();
      endDate = moment(incorporatedDateRange[0], "MM/DD/YYYY").add(1, 'days').toDate();
      viewFilters.incorporatedDateRange = { start: incorporatedDateRange[0], end: incorporatedDateRange[0] };
    }
    dbQuery["companyData.incorporationdate"] = { $gte: startDate, $lte: endDate }

  }

  if (financialPeriodEndDateRange && financialPeriodEndDateRange !== "") {
    // format range date is "YYYY-MM-DD - YYYY-MM-DD"
    financialPeriodEndDateRange = financialPeriodEndDateRange.split("-").map((date) => date.trim());
    let startDate;
    let endDate;
    if (financialPeriodEndDateRange.length > 1) {
      startDate = moment(financialPeriodEndDateRange[0], "MM/DD/YYYY").toDate();
      endDate = moment(financialPeriodEndDateRange[1], "MM/DD/YYYY").toDate();
      viewFilters.financialPeriodEndDateRange = { start: financialPeriodEndDateRange[0], end: financialPeriodEndDateRange[1] };
    } else {
      startDate = moment(financialPeriodEndDateRange[0], "MM/DD/YYYY").toDate();
      endDate = moment(financialPeriodEndDateRange[0], "MM/DD/YYYY").add(1, 'days').toDate();
      viewFilters.financialPeriodEndDateRange = { start: financialPeriodEndDateRange[0], end: financialPeriodEndDateRange[0] };
    }
    dbQuery["financialPeriod.end"] = { $gte: startDate, $lte: endDate }

  }

  if (paymentDateRange && paymentDateRange !== "") {
    // format range date is "YYYY-MM-DD - YYYY-MM-DD"
    paymentDateRange = paymentDateRange.split("-").map((date) => date.trim());
    let startDate;
    let endDate;
    if (paymentDateRange.length > 1) {
      startDate = moment(paymentDateRange[0], "MM/DD/YYYY").toDate();
      endDate = moment(paymentDateRange[1], "MM/DD/YYYY").toDate();
      viewFilters.paymentDateRange = { start: paymentDateRange[0], end: paymentDateRange[1] };
    } else {
      startDate = moment(paymentDateRange[0], "MM/DD/YYYY").toDate();
      endDate = moment(paymentDateRange[0], "MM/DD/YYYY").add(1, 'days').toDate();
      viewFilters.financialPeriodEndDateRange = { start: financialPeriodEndDateRange[0], end: financialPeriodEndDateRange[0] };
    }
    dbQuery["payment.paidAt"] = { $gte: startDate, $lte: endDate }

  }

  if (filterAlreadySubmitted !== "ALL") {
    if (filterAlreadySubmitted === "YES") {
      dbQuery["submittedAt"] = { "$exists": true, "$ne": null };
    }

    if (filterAlreadySubmitted === "NO") {
      dbQuery["submittedAt"] = { "$in": [null, undefined] };
    }
  }

  if (allowReopen !== "ALL") {
    if (allowReopen === "YES") {
      dbQuery["status"] = { "$in": ["SUBMITTED", "PAID", "HELP COMPLETED"] }
    }
    if (allowReopen === "NO") {
      dbQuery["status"] = { "$nin": ["SUBMITTED", "PAID", "HELP COMPLETED"] }
    }
  }

  return { dbQuery, viewFilters };
}


/**
 * Generate the query to search on db and the filters object to return to the the view of accounting records officer queue
 * @function parseFinancialReportSearchQuery
 * @param {object}  filters filters object from view.
 * @param {string}  user email of the trident officer.
 * @return dbEntry, viewFilters
 */
function parseOfficerQueueQuery(filters, user) {
  const companyName = filters.companyName;
  const mcc = filters.masterclientcode;
  const filterUser = filters.user || "";
  let states = filters.states || [];
  let submittedDateRange = filters.submittedDateRange;

  let statusQuery = []

  let dbQuery = {}
  let viewFilters = filters;
  const isLoadFirstTime = Object.keys(filters).length === 0;

  if (companyName && companyName.length > 2) {
    dbQuery["companyData.name"] = { $regex: companyName, $options: "i" };
  }

  if (mcc && mcc.length > 2) {
    dbQuery["masterClientCode"] = { $regex: mcc, $options: "i" };
  }

  if (submittedDateRange && submittedDateRange !== "") {
    // format range date is "YYYY-MM-DD - YYYY-MM-DD"
    submittedDateRange = submittedDateRange.split("-").map((date) => date.trim());
    let startDate;
    let endDate;
    if (submittedDateRange.length > 1) {
      startDate = moment(submittedDateRange[0], "MM/DD/YYYY").toDate();
      endDate = moment(submittedDateRange[1], "MM/DD/YYYY").toDate();
      viewFilters.submittedDateRange = { start: submittedDateRange[0], end: submittedDateRange[1] };
    } else {
      startDate = moment(submittedDateRange[0], "MM/DD/YYYY").toDate();
      endDate = moment(submittedDateRange[0], "MM/DD/YYYY").add(1, 'days').toDate();
      viewFilters.submittedDateRange = { start: submittedDateRange[0], end: submittedDateRange[0] };
    }
    dbQuery["submittedAt"] = { $gte: startDate, $lte: endDate }

  }


  viewFilters.user = filterUser;

  if (states.length > 0) {
    if (states.includes("UNASSIGNED")) {
      statusQuery.push({
        "$or": [
          { "status": { "$in": [REPORT_STATUS.REQUEST_HELP, REPORT_STATUS.UNDER_REVIEW] } },
          { "$and": [{ "status": REPORT_STATUS.IN_PENALTY }, { "officerDetails.currentOfficer": { "$in": [null, undefined] } }] }
        ]
      })
    }


    if (states.includes("ASSIGNED")) {
      const userCondition = filterUser !== "ALL" && filterUser !== "" ? filterUser : { "$exists": true };
      statusQuery.push({
        "$and": [
          { "status": { "$in": [REPORT_STATUS.HELP_PROGRESS, REPORT_STATUS.IN_PENALTY] } },
          { "officerDetails.currentOfficer": userCondition }
        ]
      })
    }

    if (states.includes("ASSIGNED_ME")) {
      statusQuery.push({
        "$and": [
          { "status": { "$in": [REPORT_STATUS.HELP_PROGRESS, REPORT_STATUS.IN_PENALTY] } },
          { "officerDetails.currentOfficer": user }
        ]
      })
    }


    if (states.includes("COMPLETED")) {
      const userCondition = filterUser !== "ALL" && filterUser !== "" ? filterUser : { "$exists": true };
      statusQuery.push({
        "$and": [
          { "status": REPORT_STATUS.HELP_COMPLETED },
          { "officerDetails.currentOfficer": userCondition }
        ]
      })
    }

    if (states.includes("COMPLETED_ME")) {
      statusQuery.push({
        "$and": [
          { "status": REPORT_STATUS.HELP_COMPLETED },
          { "officerDetails.currentOfficer": user }
        ]
      })
    }

    if (states.includes("CONFIRMED_EXEMPTED")) {
      statusQuery.push({
        "$and": [
          { "status": REPORT_STATUS.CONFIRMED },
          { "reportDetails.isExemptCompany": true }
        ]
      })
    }

    viewFilters.states = states;
    if (statusQuery.length > 0) {
      statusQuery.length === 1 ? dbQuery = { ...dbQuery, ...statusQuery[0] } : dbQuery["$or"] = statusQuery;
    }
  } else {
    if (filterUser !== "") {
      if (filterUser === "ALL") {
        dbQuery["officerDetails.currentOfficer"] = { "$exists": true };
      } else {
        dbQuery["officerDetails.currentOfficer"] = filterUser;
      }
    }


    if (Object.keys(dbQuery).length === 0 && isLoadFirstTime) {
      dbQuery["status"] = { "$in": [REPORT_STATUS.HELP_PROGRESS, REPORT_STATUS.HELP_COMPLETED, REPORT_STATUS.IN_PENALTY] };
      dbQuery["officerDetails.currentOfficer"] = user;
      viewFilters.states = ["ASSIGNED_ME", "COMPLETED_ME"];
    } else {
      dbQuery["status"] = { "$in": [REPORT_STATUS.REQUEST_HELP, REPORT_STATUS.HELP_PROGRESS, REPORT_STATUS.HELP_COMPLETED, REPORT_STATUS.UNDER_REVIEW, REPORT_STATUS.IN_PENALTY] };
    }
  }

  return { dbQuery, viewFilters };
}


/**
 * Generate the query to search on db and the filters object to return to the the view of accounting records officer queue
 * @function parseCompanyDashboardQuery
 * @param {object}  filters filters object from view.
 * @return dbEntry, viewFilters
 */
function parseCompanyDashboardQuery(filters) {
  const companyName = filters.companyName;
  const companyCode = filters.companyCode;
  const mcc = filters.masterClientCode;
  const incorporationCode = filters.incorporationCode;

  const currentDate = new Date();

  const filterStatus = filters.status || "BOTH";
  const moduleState = filters.moduleState || "ALL";
  const incorporatedDateRangeStart = filters.incorporatedDateRangeStart ? new Date(filters.incorporatedDateRangeStart) : null;
  const incorporatedDateRangeEnd = filters.incorporatedDateRangeEnd ? new Date(filters.incorporatedDateRangeEnd) : null;
  const daysToDeadlineRangeStart = filters.daysToDeadlineStart ? moment(currentDate).startOf('day').add(parseInt(filters.daysToDeadlineStart), 'days').toDate() : null;
  const daysToDeadlineRangeEnd = filters.daysToDeadlineEnd ? moment(currentDate).startOf('day').add(parseInt(filters.daysToDeadlineEnd) + 1, 'days').toDate() : null;
  const deadlineFrom = filters.deadlineFrom ? new Date(filters.deadlineFrom) : null;
  const deadlineTo = filters.deadlineTo ? new Date(filters.deadlineTo) : null;
  const inPenalty = filters.inPenalty || "ALL";

  let dbQuery = {}
  let viewFilters = filters;

  if (companyName && companyName.length > 2) {
    dbQuery["name"] = { $regex: companyName, $options: "i" };
  }

  if (companyCode && companyCode.length > 2) {
    dbQuery["code"] = { $regex: companyCode, $options: "i" };
  }

  if (mcc && mcc.length > 2) {
    const escapedMCC = mcc.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, "\\$&");
    dbQuery["masterclientcode"] = { $regex: escapedMCC, $options: "i" };
  }

  if (incorporationCode && incorporationCode.length > 2) {
    dbQuery["incorporationcode"] = { $regex: incorporationCode, $options: "i" };
  }

  if (incorporatedDateRangeStart) {
    if (!dbQuery["incorporationdate"]) {
      dbQuery["incorporationdate"] = {};
    }
    dbQuery["incorporationdate"]["$gte"] = incorporatedDateRangeStart;
  }
  if (incorporatedDateRangeEnd) {
    if (!dbQuery["incorporationdate"]) {
      dbQuery["incorporationdate"] = {};
    }
    dbQuery["incorporationdate"]["$lte"] = incorporatedDateRangeEnd;
  }

  if (daysToDeadlineRangeStart) {
    if (!dbQuery["accountingRecordsModule.currentDeadline"]) {
      dbQuery["accountingRecordsModule.currentDeadline"] = {};
    }
    dbQuery["accountingRecordsModule.currentDeadline"]["$gte"] = daysToDeadlineRangeStart;
  }
  if (daysToDeadlineRangeEnd) {
    if (!dbQuery["accountingRecordsModule.currentDeadline"]) {
      dbQuery["accountingRecordsModule.currentDeadline"] = {};
    }
    dbQuery["accountingRecordsModule.currentDeadline"]["$lte"] = daysToDeadlineRangeEnd;
  }

  if (deadlineFrom) {
    if (!dbQuery["accountingRecordsModule.currentDeadline"]) {
      dbQuery["accountingRecordsModule.currentDeadline"] = {};
    }
    dbQuery["accountingRecordsModule.currentDeadline"]["$gte"] = deadlineFrom;
  }
  if (deadlineTo) {
    if (!dbQuery["accountingRecordsModule.currentDeadline"]) {
      dbQuery["accountingRecordsModule.currentDeadline"] = {};
    }
    dbQuery["accountingRecordsModule.currentDeadline"]["$lte"] = deadlineTo;
  }

  if (filterStatus !== "BOTH") {
    if (filterStatus === "COMPLIANT") {
      dbQuery["accountingRecordsModule.compliantStatus"] = 'COMPLIANT';
    }

    if (filterStatus === "NON-COMPLIANT") {
      dbQuery["accountingRecordsModule.compliantStatus"] = 'NON-COMPLIANT';
    }
    if (filterStatus === "EMPTY") {
      dbQuery["accountingRecordsModule.compliantStatus"] = { "$in": [null, "", undefined] };
    }
  }

  if (moduleState !== "ALL") {
    if (moduleState === "ACTIVE") {
      dbQuery["accountingRecordsModule.active"] = true;
    }

    if (moduleState === "INACTIVE") {
      dbQuery["accountingRecordsModule.active"] = { "$ne": true };
    }
  }

  if (inPenalty !== "ALL") {
    if (inPenalty === "YES") {
      dbQuery["accountingRecordsModule.inPenalty"] = true;
    } else {
      dbQuery["accountingRecordsModule.inPenalty"] = { "$ne": true };
    }
  }

  return { dbQuery, viewFilters };
}

/**
 * Generate the query to search on db and the filters object to return to the the view of accounting records officer queue
 * @function getOfficerQueueStatistics
 * @param {string}  user email of the trident officer.
 * @return JSON object with the counts of the statistics
 */
async function getOfficerQueueStatistics(user) {

  const aggregateResultCounts = await FinancialReportModel.aggregate([
    {
      $group: {
        _id: null,
        newRequestHelpReports: {
          $sum: {
            $cond: [
              {
                $or: [
                  { $eq: ["$status", REPORT_STATUS.REQUEST_HELP] },
                  { $eq: ["$status", REPORT_STATUS.UNDER_REVIEW] }
                ]
              },
              1,
              0
            ]
          }
        },
        assignedReportsMe: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ["$status", REPORT_STATUS.HELP_PROGRESS] },
                  { $eq: ["$officerDetails.currentOfficer", user] }
                ]
              },
              1,
              0
            ]
          }
        },
        completedReportsMe: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ["$status", REPORT_STATUS.HELP_COMPLETED] },
                  { $eq: ["$officerDetails.currentOfficer", user] }
                ]
              },
              1,
              0
            ]
          }
        },
        assignedReports: {
          $sum: {
            $cond: [
              { $eq: ["$status", REPORT_STATUS.HELP_PROGRESS] },
              1,
              0
            ]
          }
        },
        completedReports: {
          $sum: {
            $cond: [
              { $eq: ["$status", REPORT_STATUS.HELP_COMPLETED] },
              1,
              0
            ]
          }
        },
        exemptedUnderReview: {
          $sum: {
            $cond: [
              { $eq: ["$status", REPORT_STATUS.UNDER_REVIEW] },
              1,
              0
            ]
          }
        },
        exemptedConfirmed: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ["$status", REPORT_STATUS.CONFIRMED] },
                  { $eq: ["$reportDetails.isExemptCompany", true] }
                ]
              },
              1,
              0
            ]
          }
        },
        totalInPenalty: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ["$status", REPORT_STATUS.IN_PENALTY] },
                ]
              },
              1,
              0
            ]
          }
        },
      },
    }
  ]);

  const result = aggregateResultCounts[0];


  return {
    totalUnassigned: result.newRequestHelpReports,
    totalAssignedMe: result.assignedReportsMe,
    totalCompletedMe: result.completedReportsMe,
    totalAssigned: result.assignedReports,
    totalCompleted: result.completedReports,
    totalExemptedUnderReview: result.exemptedUnderReview,
    totalExemptedConfirmed: result.exemptedConfirmed,
    totalInPenalty: result.totalInPenalty
  };
}


/**
 * Generate the email body for request info notification
 * @function formatRfiEmailContent
 * @param {companyName}  companyName name of the company
 * @param {financialPeriodEnd}  financialPeriodEnd end of fiscal period
 * @param {deadline}  deadline date limit to response the request info
 * @return string
 */
function formatRfiEmailContent(companyName, financialPeriodEnd, deadline) {
  return "Dear Client, \n\n" +
    "Request for information - " + companyName + " \n\n" +
    "Please be advised that the International Tax Authority (ITA) is requesting additional information in relation to the economic substance " +
    "submission which was filed for the Entity for the financial period ending" + financialPeriodEnd + ".\n" +
    "The deadline for responding to the ITA’s request is " + deadline + ".\n" +
    "The details of the ITA request can be viewed in the Companies module, or you can reach it directly by clicking the attached URL below. \n\n" +
    "Thank you\n" +
    "Trident Economic Substance Team";
}

/**
 * Generate the email body for request info notification
 * @function parseCompleteDetailsIncomeAndExpenses
 * @param {formValues}  formValues report values from form page
 * @param {currentReport}  currentReport report information from db
 * @return string
 */
function parseCompleteDetailsIncomeAndExpenses(formValues, currentReport) {
  // income values
  const totalRevenue = utils.parseStringNumberToFloat(formValues.revenue);
  const costOfSales = utils.parseStringNumberToFloat(formValues.costOfSales);
  const grossProfit = totalRevenue - costOfSales

  /// expenses values
  const operatingExpenses = utils.parseStringNumberToFloat(formValues.operatingExpenses);
  const incomeTax = utils.parseStringNumberToFloat(formValues.incomeTax);
  const totalOtherExpenses = utils.parseStringNumberToFloat(formValues.totalOtherExpenses);

  const totalExpenses = operatingExpenses + incomeTax + totalOtherExpenses;
  const netIncome = grossProfit - totalExpenses
  const newValues = {
    income: {
      total: totalRevenue,
      costOfSales: costOfSales
    },
    expenses: {
      incomeTax: incomeTax,
      operatingExpenses: operatingExpenses,
      totalOtherExpenses: totalOtherExpenses,
      totalOfExpenses: totalExpenses
    },
    assets: currentReport.completeDetails?.assets ?? {},
    liabilities: currentReport.completeDetails?.liabilities ?? {},
    shareholderEquity: currentReport.completeDetails?.shareholderEquity ?? null,
    grossProfit: grossProfit,
    netIncome: netIncome
  };
  return newValues;
}

/**
 * Generate the email body for request info notification
 * @function parseCompleteDetailsAssetsAndLiabilities
 * @param {formValues}  formValues report values from form page
 * @param {currentReport}  currentReport report information from db
 * @return string
 */
function parseCompleteDetailsAssetsAndLiabilities(formValues, currentReport) {
  // assets values 
  const cashAmount = utils.parseStringNumberToFloat(formValues.cashAmount);
  const loansAndReceivables = utils.parseStringNumberToFloat(formValues.loansAndReceivables);
  const investmentsAssetsAmount = utils.parseStringNumberToFloat(formValues.investmentsAssetsAmount);
  const fixedAssetsAmount = utils.parseStringNumberToFloat(formValues.fixedAssetsAmount);
  const intangibleAssetsAmount = utils.parseStringNumberToFloat(formValues.intangibleAssetsAmount);
  const totalOtherAssets = utils.parseStringNumberToFloat(formValues.totalOtherAssets);

  // liabilities values
  const accountsPayable = utils.parseStringNumberToFloat(formValues.accountsPayable);
  const longTermDebts = utils.parseStringNumberToFloat(formValues.longTermDebts);
  const totalOtherLiabilities = utils.parseStringNumberToFloat(formValues.totalOtherLiabilities);


  const totalAssets = cashAmount + loansAndReceivables + investmentsAssetsAmount + fixedAssetsAmount + intangibleAssetsAmount + totalOtherAssets;
  const totalLiabilities = accountsPayable + longTermDebts + totalOtherLiabilities;

  const totalShareholdersEquity = (totalAssets - totalLiabilities);

  const newValues = {
    income: currentReport.completeDetails?.income ?? {},
    expenses: currentReport.completeDetails?.expenses ?? {},
    assets: {
      cashAmount: cashAmount,
      loansAndReceivables: loansAndReceivables,
      investmentsAssetsAmount: investmentsAssetsAmount,
      fixedAssetsAmount: fixedAssetsAmount,
      intangibleAssetsAmount: intangibleAssetsAmount,
      totalOtherAssets: totalOtherAssets,
      total: totalAssets,
    },
    liabilities: {
      accountsPayable: accountsPayable,
      longTermDebts: longTermDebts,
      totalOtherLiabilities: totalOtherLiabilities,
      total: totalLiabilities,
    },
    shareholderEquity: totalShareholdersEquity,
    grossProfit: currentReport.completeDetails.grossProfit ?? null,
    netIncome: currentReport.completeDetails.netIncome ?? null,
  };

  return newValues;
}

/**
 * Validate the form values entered by the user
 * @function validateCompleteFormValues
 * @param {step}  step report form page
 * @param {formValues}  formValues report form values
 * @return Array<string>
 */
function validateCompleteFormValues(step, formValues) {
  let errors = [];

  if (step === REPORT_FORM_PAGES.INCOME_EXPENSES_PAGE) {
    errors = getErrorsByInputNumberList(FORM_COMPLETE_INCOME_EXPENSES_INPUT_NUMBER_VALIDATION_ERRORS, formValues)
  } else {
    errors = getErrorsByInputNumberList(FORM_COMPLETE_ASSETS_LBT_INPUT_NUMBER_VALIDATION_ERRORS, formValues)
  }
  return errors;
}

/**
 * Validate the number inputs and return the list of message errors
 * @function getErrorsByInputNumberList
 * @param {listOfInputs}  listOfInputs  list of numeric inputs
 * @param {formValues}  formValues report input values
 * @return Array<string>
 */
function getErrorsByInputNumberList(listOfInputs, formValues) {
  const errors = [];

  listOfInputs.forEach((inputObj) => {
    const inputVal = formValues[inputObj.input];
    const isInvalidNumber = utils.isInvalidNumber(inputVal);

    if (isInvalidNumber) {
      errors.push(inputObj.error);
    } else {
      if (inputObj.validation === "num-positive" && parseFloat(inputVal) < 0) {
        errors.push(inputObj.error);
      }

      if (inputObj.validation === "num-negative" && parseFloat(inputVal) > 0) {
        errors.push(inputObj.error);
      }
    }
  })
  return errors;
}

/**
 * map data to render in company dashboard table
 * @function mapCompanyDashboardItem
 * @param {company}  company company data with financial reports
 * @return object
 */
function mapCompanyDashboardItem(company, config) {
  const financialReports = company.financialReports;
  let initialFPDate;
  let confirmedPeriod;
  let deadlineMomentDate;
  let daysToDeadline;
  let penalty;
  let isExemptCompany = "NOT AVAILABLE";
  let lastReport;

  if (company.accountingRecordsModule?.firstFinancialPeriodEnd) {
    initialFPDate = company.accountingRecordsModule?.firstFinancialPeriodEnd;
    confirmedPeriod = company.accountingRecordsModule?.firstFinancialPeriodEnd;
  }

  let inPenaltyReportsAssigned = false;
  if (financialReports.length > 0) {
    const invalidStatus = [REPORT_STATUS.SAVED, REPORT_STATUS.IN_PROGRESS, REPORT_STATUS.DELETED]
    const submittedReports = financialReports.filter((r) => !invalidStatus.includes(r.status));

    const penaltyReportsAssigned = financialReports.filter((r) => r.status === REPORT_STATUS.IN_PENALTY && (r.officerDetails?.currentOfficer !== null && r.officerDetails?.currentOfficer !== ''));

    if (penaltyReportsAssigned.length > 0) {
      inPenaltyReportsAssigned = true;
    }

    if (submittedReports.length > 0) {
      submittedReports.sort((a, b) => b.financialPeriod?.end - a.financialPeriod?.end);
      let lastFinancialReport = submittedReports[0];
      confirmedPeriod = lastFinancialReport.financialPeriod.end;
      isExemptCompany = lastFinancialReport.reportDetails?.isExemptCompany === true ? "YES" : "NO";
    }
  }

  if (confirmedPeriod && company.accountingRecordsModule?.currentDeadline) {
    deadlineMomentDate = moment(company.accountingRecordsModule?.currentDeadline).utc();
  }

  if (deadlineMomentDate) {
    daysToDeadline = deadlineMomentDate.diff(moment().utc(), 'days');
    const todayPenalty = getDeadlinePenalty(daysToDeadline, config.financialReportConfiguration?.penaltyRanges);
    // check if is the same penalty, could be different in case the report is submitted and the azure function not ran yet again to update the values in company.
    penalty = daysToDeadline < 0 ?
      (company.accountingRecordsModule?.currentPenaltyAmount === todayPenalty ? company.accountingRecordsModule?.currentPenaltyAmount : todayPenalty) :
      "";
  }

  if (company.accountingRecordsModule?.reportedHistory?.length > 0) {
    lastReport = company.accountingRecordsModule.reportedHistory[company.accountingRecordsModule.reportedHistory.length - 1]
  }

  let unreportAvailable = company.accountingRecordsModule?.isReported === true;
  if (company.accountingRecordsModule?.inPenalty === true && inPenaltyReportsAssigned) {
    unreportAvailable = false;
  }

  return {
    _id: company._id,
    name: company.name,
    referralOffice: company.referral_office,
    code: company.code,
    status: company.accountingRecordsModule.compliantStatus,
    masterClientCode: company.masterclientcode,
    incorporationCode: company.incorporationcode,
    incorporationDate: company.incorporationdate,
    initialFPDate: initialFPDate,
    confirmedPeriod: confirmedPeriod ? confirmedPeriod : "NOT STARTED",
    isExemptCompany: isExemptCompany,
    deadline: deadlineMomentDate ? deadlineMomentDate.toDate() : null,
    daysToDeadline: daysToDeadline,
    penalty: penalty,
    isReported: lastReport?.isReported ?? false,
    reportingAvailable: company.accountingRecordsModule.compliantStatus === 'NON-COMPLIANT',
    unreportAvailable: unreportAvailable,
  }
}


/**
 * get the penalty by the deadline overdue days
 * @function getDeadlinePenalty
 * @param {overdueDays}  overdueDays number of days past the deadline date
 * @return number
 */
function getDeadlinePenalty(overdueDays, penaltyRanges) {

  const positiveOverdueDays = Math.abs(overdueDays)
  penaltyRanges.sort((a, b) => a.startRange - b.startRange);
  const lastRange = penaltyRanges[penaltyRanges.length - 1];

  if (overdueDays > lastRange.startRange) {
    return lastRange.amount;
  }

  const selectedRange = penaltyRanges.find((p) => positiveOverdueDays >= p.startRange && positiveOverdueDays <= p.endRange);

  if (selectedRange) {
    return selectedRange.amount;
  }

  return 0;
}

/**
 * Calculate accounting deadline for a company based on its financial reports
 * @function getAccountingDeadline
 * @param {Object} accountingModule - Company's accounting records module
 * @param {Array} financialReports - Array of financial reports for the company
 * @return {Object} Object containing newAccountingDeadline and newFilingDeadline
 */
function getAccountingDeadline(accountingModule, financialReports) {
  if (!accountingModule || !accountingModule?.firstFinancialPeriodEnd) {
    return null;
  }

  let returnObject = {
    newAccountingDeadline: null,
    newFilingDeadline: null
  };

  const availableReports = financialReports.filter((r) => r.status !== "DELETED");

  let confirmedPeriodEnd = accountingModule.firstFinancialPeriodEnd;
  let confirmedPeriodStart = accountingModule.firstFinancialPeriodStart;
  let currentAccountingDeadline = null;

  const PENDING_STATUS = ["SAVED", "IN PROGRESS", "IN PENALTY"];

  // get current deadline saved in accountingModule
  if (accountingModule?.currentDeadline && accountingModule?.currentDeadline !== null) {
    currentAccountingDeadline = moment(accountingModule.currentDeadline).utc();
  }

  // if not reports created then deadline is confirmed end period + 9 months
  if (availableReports.length === 0) {
    if (moment(confirmedPeriodStart).isBefore(moment("2024-01-01").utc().startOf('day'))) {
      returnObject.newAccountingDeadline = moment(confirmedPeriodEnd).utc().add(18, 'months').add(4, 'hours');
      returnObject.newFilingDeadline = moment(confirmedPeriodEnd).utc().add(18, 'months').add(4, 'hours');
    } else {
      returnObject.newAccountingDeadline = moment(confirmedPeriodEnd).utc().add(9, 'months').add(4, 'hours');
      returnObject.newFilingDeadline = moment(confirmedPeriodEnd).utc().add(9, 'months').add(4, 'hours');
    }
  }
  // if company has only 1 report created
  else if (availableReports.length === 1) {
    const existingReport = availableReports[0];
    const pendingSubmitReport = PENDING_STATUS.includes(existingReport.status);

    // if report is still pending to confirm submit then deadline is report end period + 9 months
    if (pendingSubmitReport) {
      if (moment(existingReport.financialPeriod.start).utc().isBefore(moment("2024-01-01").utc().startOf('day'))) {
        returnObject.newAccountingDeadline = moment(existingReport.financialPeriod.end).utc().add(18, 'months').add(4, 'hours');
        returnObject.newFilingDeadline = moment(existingReport.financialPeriod.end).utc().add(18, 'months').add(4, 'hours');
      } else {
        returnObject.newAccountingDeadline = moment(existingReport.financialPeriod.end).utc().add(9, 'months').add(4, 'hours');
        returnObject.newFilingDeadline = moment(existingReport.financialPeriod.end).utc().add(9, 'months').add(4, 'hours');
      }
    }
    // if is confirmed submission then deadline must be report end period + 1 year + 9 months
    else {
      const nextFPStartDate = moment(existingReport.financialPeriod.end).utc().add('1', 'days');
      const nextFPEndDate = moment(existingReport.financialPeriod.end).add(1, 'years');
      if (moment(nextFPStartDate).utc().isBefore(moment("2024-01-01").utc().startOf('day'))) {
        returnObject.newAccountingDeadline = moment(nextFPEndDate).utc().add(18, 'months').add(4, 'hours');
        returnObject.newFilingDeadline = moment(nextFPEndDate).utc().add(18, 'months').add(4, 'hours');
      } else {
        returnObject.newAccountingDeadline = moment(nextFPEndDate).utc().add(18, 'months').add(4, 'hours');
        returnObject.newFilingDeadline = moment(nextFPEndDate).utc().add(18, 'months').add(4, 'hours');
      }
    }
  }
  // if company has more than 1 reports created
  else {
    // get the last valid submitted report
    const submittedReports = availableReports.filter((r) => r.submittedAt && !PENDING_STATUS.includes(r.status));
    submittedReports.sort((a, b) => b.financialPeriod?.end - a.financialPeriod?.end);
    let lastFinancialReport = submittedReports[0];

    // the deadline should be the last submitted report end period + 1 year + 9 months
    const nextFPStartDate = moment(lastFinancialReport.financialPeriod.end).utc().add('1', 'days');
    const nextFPEndDate = moment(lastFinancialReport.financialPeriod.end).add(1, 'years');
    if (moment(nextFPStartDate).utc().isBefore(moment("2024-01-01").utc().startOf('day'))) {
      returnObject.newAccountingDeadline = moment(nextFPEndDate).utc().add(18, 'months').add(4, 'hours');
      returnObject.newFilingDeadline = moment(nextFPEndDate).utc().add(18, 'months').add(4, 'hours');
    } else {
      returnObject.newAccountingDeadline = moment(nextFPEndDate).utc().add(18, 'months').add(4, 'hours');
      returnObject.newFilingDeadline = moment(nextFPEndDate).utc().add(18, 'months').add(4, 'hours');
    }
  }

  return returnObject;
}
