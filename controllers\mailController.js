const nodemailer = require('nodemailer')

//send confirmationcode via email
const transporter = nodemailer.createTransport(
    {
        host: process.env.SMTP_SERVER,
        port: process.env.SMTP_PORT ? process.env.SMTP_PORT :
            process.env.SMTP_SECURE === 'true' ? 465 : 587,
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASSWORD
        } 
    });       
    
    
exports.send = function(to, subject, text, html)
{
    const mailOptions = {
        from: process.env.SMTP_FROM, // sender address (who sends)
        to: to, // list of receivers (who receives)
        subject: subject, // Subject line
        text: text, // plaintext body
        html: html // html body
    };

    // send mail with defined transport object
    transporter.sendMail(mailOptions, function(error){
        if(error){
            console.log("email error ", error);
        }
    });  
}

exports.asyncSend = async function(to, subject, text, html)
{
    const mailOptions = {
        from: process.env.SMTP_FROM, // sender address (who sends)
        to: to, // list of receivers (who receives)
        subject: subject, // Subject line
        text: text, // plaintext body
        html: html // html body
    };

    // send mail with defined transport object

    return await transporter.sendMail(mailOptions).catch(function (reason) {
        return reason;
    }); 
};
