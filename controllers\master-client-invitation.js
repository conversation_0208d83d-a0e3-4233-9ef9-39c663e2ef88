const fs = require('fs');



exports.generateEmail = function (code, owner) {
    let htmlString = fs.readFileSync('invitation-email.html', {encoding: 'utf-8'}).replace('##MCC##', code).replace('##EMAIL##', owner);


    let textString = 'Private Client Portal \n' +
        'To complete the secure registration process and activate your account, please use the following credentials:\n' +
        'Your master client code : (' + code + ')\n' +
        'Your registered email address : (' + owner + ')\n' +
        'Registration Process:\n' +
        'Proceed to the address displayed on this email: https://clientportal.tridenttrust.com \n' +
        'Click on Register and enter your master client code and email address as indicated above (case sensitive).\n' +
        'A secure email will be generated with the final activation link to activate your account and create a password.\n' +
        'Additionally, there is an extra level of security: a 2-factor authentication code is required for further activation, and you may select to have the requisite codes generated via email or a downloaded authenticator app to your mobile device. ' +
        'A new code will be generated for each login session to the portal. If using an authentication app, we recommend the Google Authenticator App, which can be downloaded from the Play Store or Apple Store (iPhone). The downloaded app will be used to' +
        'scan the QR code prompted on the portal upon first login. Once the app and portal are successfully synced, the app will generate a 6-digit code for activation of the account.' + 
        'Support\n' +
        'For assistance with classification or data submission, please contact your usual Trident representative or email <NAME_EMAIL>\n' +
        'Data Protection\n' +
        'As Registered Agent of your Entity, Trident will collect and process the Personal Data of individuals related to the Entity (i.e. directors, employees etc.) once such Personal Data is submitted using Trident’s data collection portal.\n' +
        'This processing is required for compliance with regulatory requirements by both ourselves as the Registered Agent and the Entity and may include the transfer of Personal Data to BVI and foreign competent authorities where applicable.\n' +
        'Any processing of Personal Data by Trident will be done in accordance with the requirements of the General Data Protection Regulation (EU) 2016/679, the Trident Trust Group Fair Processing Notice and any applicable BVI law.\n';

    return {textString, htmlString};
}
