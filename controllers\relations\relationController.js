const {
  filereview: FileReviewModel,
  naturalReview: naturalReview,
  organizationReview: organizationReview,
  positionReview: positionReview,
  fileTypeValidation: fileTypeValidation
} = require('../../models/filereview');
const NaturalPersonModel = require('../../models/naturalperson');
const OrganizationModel = require('../../models/organization');
const ClientReviewModel = require('../../models/client');
const BeneficialOwnerModel = require('../../models/beneficialowners');
const ConfigModel = require('../../models/config');
const {v4: uuidv4} = require('uuid');
const uploadController = require('../uploadController');
const {countries} = require('../../utils/constants');
const MailController = require('../mailController');
const MailFormatter = require('../mailFormatController');
const idPalController = require('../idPalController');
const ObjectId = require('mongoose').Types.ObjectId;
const moment = require("moment");
let tempUploads;

exports.tempUploads = tempUploads;

function clearTempUploadFiles(req) {
  req.session.relationFiles = {};
}

function setFormValuesToRelation(relation, relationReviewObject, form, sections, uploadedFiles = null) {
  try {
    sections.forEach((sectionKey) => {
      const formSection = form[sectionKey];
      if (relationReviewObject) {
        relationReviewObject[sectionKey] = {
          complete: !!(form[sectionKey] && form[sectionKey].correct),
        }
      }

      if (formSection) {
        const reviewSection = relation[sectionKey] ? relation[sectionKey] : {};
        const sectionKeys = Object.keys(formSection);

        sectionKeys.forEach((key) => {
          if (key === "files") {
            if (reviewSection[key]) {
              const files = reviewSection[key];
              const fileKeys = Object.keys(formSection[key]);

              fileKeys.forEach((fileKey) => {
                if (files[fileKey]) {
                  files[fileKey].present = !!formSection[key][fileKey].present;
                  files[fileKey].explanation = formSection[key][fileKey].explanation ?
                    formSection[key][fileKey].explanation : '';
                }
              });
              reviewSection[key] = files;
            }
          } else {
            reviewSection[key] = formSection[key]
          }
        });


        let files = reviewSection["files"];
        if (files) {
          files.forEach((file, index) => {
            if (!file.id) {
              file.id = uuidv4();
            }
            const tempFiles = uploadedFiles ? uploadedFiles[sectionKey] : {};

            if (tempFiles) {
              let newFiles = tempFiles[index] ? tempFiles[index] : [];
              if (newFiles.length) {
                file.uploadFiles = file.uploadFiles.concat(newFiles);
                file.present = true;
              }

            }
          });

          reviewSection["files"] = files;
        }
        relation[sectionKey] = reviewSection;
      }
    });
  } catch (e) {
    console.log(e);
  }
  return {relation: relation, relationReviewObject: relationReviewObject};
}

exports.getReviewRelations = async function (companyId, getPositions = false) {
  try {
    let fileReview = await FileReviewModel.findById(companyId);
    let beneficialOwners = [];
    let shareholders = [];
    let directors = [];
    let percentages = [];
    let positions = [];
    let pendingElectronicIdRelations = [];
    let totalPercentage = 0;
    let electronicIdCanceledList = [];
    let electronicIdList = [];

    const clientReview = await ClientReviewModel.findById(fileReview.clientId);
    let naturals = clientReview.naturalRelations.map((r) => r.referenceId);
    let organizations = clientReview.organizationRelations.map((r) => r.referenceId);

    if (naturals) {
      naturals = [...new Set(naturals)];
    }
    if (organizations) {
      organizations = [...new Set(organizations)];
    }
    const naturalList = await NaturalPersonModel.find({
      _id: {$in: naturals},
    });

    if (naturalList.length > 0){
      pendingElectronicIdRelations = naturalList.filter((n) => n.electronicIdInfo &&
        (n.electronicIdInfo.status === "NOT STARTED" || n.electronicIdInfo === "NOT SENT" ||
          n.electronicIdInfo === "PENDING INVITATION"));
      electronicIdCanceledList = naturalList.filter((n) => n.electronicIdInfo && n.electronicIdInfo.status === "CANCELLED")
        .map((n) => { return n._id.toString()});
      electronicIdList = naturalList.filter((n) => n.electronicIdInfo && n.electronicIdInfo.isElectronicId)
        .map((n) => { return n._id.toString()});

    }

    const orgList = await OrganizationModel.find({_id: {$in: organizations}});

    let beneficialRelations = [...clientReview.naturalRelations.filter((r) => r.group === "beneficial"),
      ...clientReview.organizationRelations.filter((r) => r.group === "beneficial")];
    if (beneficialRelations) {
      beneficialRelations = beneficialRelations.map((r) => r.referenceId.toString());
    }

    let shareholderRelations = [...clientReview.naturalRelations.filter((r) => r.group === "shareholder"),
      ...clientReview.organizationRelations.filter((r) => r.group === "shareholder")];
    if (shareholderRelations) {
      percentages = shareholderRelations.filter((r) => r.percentage && r.percentage !== '');
      shareholderRelations = shareholderRelations.map((r) => r.referenceId.toString());
    }

    let directorRelations = [...clientReview.naturalRelations.filter((r) => r.group === "director"),
      ...clientReview.organizationRelations.filter((r) => r.group === "director")];
    if (directorRelations) {
      directorRelations = directorRelations.map((r) => r.referenceId.toString());
    }

    const relationList = naturalList.concat(orgList);
    if (relationList.length) {
      if (beneficialRelations) {
        beneficialOwners = relationList.filter((relation) => beneficialRelations.includes(relation._id.toString()));
      }

      if (shareholderRelations) {
        shareholders = relationList.filter((relation) => shareholderRelations.includes(relation._id.toString()));
        shareholders.forEach((shareholder) => {
          const hasPercentage = percentages.find((percentage) => percentage.referenceId.toString() === shareholder._id.toString());
          if (hasPercentage) {
            shareholder["additional"] = {"percentage": hasPercentage.percentage};
            totalPercentage += Number(hasPercentage.percentage);
          }
        })
      }

      if (directorRelations) {
        directors = relationList.filter((relation) => directorRelations.includes(relation._id.toString()));
      }

      if (getPositions && orgList.length) {
        const organizationPositions = orgList.filter((org) => org.positions.length > 0);

        if (organizationPositions.length) {
          let positionIds = [];
          organizationPositions.forEach((orgPos) => {
            const uniqueIds = [...new Set(orgPos.positions.map(pos => pos.referenceId))];
            if (uniqueIds.length) {
              positionIds = [...positionIds, ...uniqueIds];
            }
          });

          const positionReviews = await NaturalPersonModel.find({_id: {$in: positionIds}});

          if (positionReviews) {
            organizationPositions.forEach((orgPos) => {
              const posMapped = orgPos.positions.map((p) => {
                return {
                  id: p._id,
                  referenceId: p.referenceId,
                  position: positionReviews.find((pr) => pr._id.toString() === p.referenceId.toString()),
                  type: p.type,
                  organizationId: orgPos._id
                }
              });

              if (posMapped.length) {
                positions = [...positions, ...posMapped];
              }
            });
          }

        }

      }

    }
    return {
      id: companyId,
      file: fileReview,
      beneficialOwners: beneficialOwners,
      shareholders: shareholders,
      directors: directors,
      totalPercentage: totalPercentage,
      hasElectronicIdNotStarted: pendingElectronicIdRelations.length > 0,
      electronicIdCanceledList: electronicIdCanceledList,
      electronicIdList: electronicIdList,
      positionList: positions
    };

  } catch (error) {
    console.log(error);
  }
};

exports.getRelationById = async function (req, res) {
  try {
    let relation;
    let responseRelation;
    if (req.query.type === 'natural') {
      relation = await NaturalPersonModel.findById(req.params.peekIndex, function (err) {
        if (err) console.log('error peeking relation');
      });

    } else {
      relation = await OrganizationModel.findById(req.params.peekIndex, function (err) {
        if (err) console.log('error peeking relation');
      }).populate('positions');
    }

    if (relation) {
      responseRelation = {
        id: relation._id,
        group: relation.group,
        positions: relation.positions,
        type: relation.type,
        files: relation.file_types,
        fileReview: relation.file_review,
        principalResidential: relation.info_types.find((question) => question.external === 'Principal Address'),
        mailingAddress: relation.info_types.find((question) => question.external === 'Mailing Address'),
        shareholderAddress: relation.info_types.find((question) => question.external === 'Shareholder Additional Details'),
      };
      if (relation.type === 'natural') {
        const naturalFields = {
          pep: relation.info_types.find((question) => question.external === 'PEP Details'),
          personal: relation.info_types.find((question) => question.external === 'Personal Details'),
          identification: relation.info_types.find((question) => question.external === 'Identification'),
          countryTax: relation.info_types.find((question) => question.external === 'Country of Tax Residence'),
          advisor: relation.info_types.find((question) => question.external === 'Advisor Details'),
          principalAddress: relation.info_types.find((question) => question.external === 'Principal Residential Address'),
        };
        responseRelation = {...responseRelation, ...naturalFields}
      } else {
        const orgFields = {
          organizationDetails: relation.info_types.find((question) => question.external === 'Organization Details'),
          listedDetails: relation.info_types.find((question) => question.external === 'Listed Company Details'),
          limitedDetails: relation.info_types.find((question) => question.external === 'Limited Company Details'),
          foundation: relation.info_types.find((question) => question.external === 'Foundation Details'),
          mutualfund: relation.info_types.find((question) => question.external === 'Mutual Fund Details'),
        };
        responseRelation = {...responseRelation, ...orgFields}
      }
    }
    return res.json({relation: responseRelation});
  } catch (error) {
    return res.status(500).end();
  }
};

exports.storeFiles = function (req, res) {
  try {
    const fileRow = req.body.row;
    let sessData = req.session;
    if (!sessData.relationFiles) {
      sessData.relationFiles = {}
    }

    const fileName = req.body.fileName.replace(/[^a-zA-Z0-9]/g, "");
    const relationFileName = "relations/" + fileName;
    const review = {
      _id: req.body.reviewId,
    };
    req.files.fileUploaded.forEach((file) => {
      uploadController.moveUpload(review, file, relationFileName).catch((reason => {
        if (reason) {
          console.log(reason);
        }
      }));
      file = {
        fileId: uuidv4(),
        fileTypeId: req.body.fileId,
        fieldName: file.fieldname.replace(/fileUploaded/i, fileName),
        originalName: file.originalname,
        encoding: file.encoding,
        mimeType: file.mimetype,
        blobName: file.blobName.replace(/fileUploaded/i, fileName),
        container: file.container,
        blob: file.blob.replace(/fileUploaded/i, fileName),
        blobType: file.blobType,
        size: file.size,
        etag: file.etag,
        url: file.url.replace(/fileUploaded/i, review._id + "/" + relationFileName),
      };

      if (sessData.relationFiles && sessData.relationFiles[req.body.fileGroup]) {
        const tempFiles = sessData.relationFiles[req.body.fileGroup];
        if (tempFiles[fileRow]) {
          tempFiles[fileRow].push(file);
        } else {
          tempFiles[fileRow] = [file];
        }
        sessData.relationFiles[req.body.fileGroup] = tempFiles;
      } else {
        // save fieldname's files
        const tempFiles = {};
        tempFiles[fileRow] = [file];
        sessData.relationFiles[req.body.fileGroup] = tempFiles;
      }

    });
    return res.status(200).end();
  } catch (e) {
    console.log(e);
    return res.status(500).end();
  }

};

exports.openNewRelation = async function (req, res, next) {
  try {
    clearTempUploadFiles(req);
    req.session.extraDetailPartnerFiles = [];
    let title = "";
    const relationGroups = req.params.relationGroups.split('-');
    let file = await FileReviewModel.findById(req.params.companyId);
    if (file && file.fileReview.username.toLowerCase() === req.session.user.username.toLowerCase()) {
      const configTemplate = await ConfigModel.findOne({});

      if (!configTemplate) {
        console.log("Configuration not found");
        res.redirect('/file-reviewer/dashboard');
      }

      const relationGroupsTitle = [];
      if (relationGroups.includes('beneficial')) {
        relationGroupsTitle.push('Beneficial Owner');
      }
      if (relationGroups.includes("shareholder")) {
        relationGroupsTitle.push('Shareholder');
      }
      if (relationGroups.includes("director")) {
        relationGroupsTitle.push('Director')
      }
      title = 'Add new ' + relationGroupsTitle.join('/ ') + ': ' + file.companyName;
      const group = req.query.group ? req.query.group : "";
      res.render('file-reviewer/review-relation/add-review-relation-component', {
        user: req.session.user,
        title: title,
        file: file,
        relation: configTemplate.relationFiles,
        reviewGroup: relationGroups,
        id: req.params.companyId,
        newRelation: true,
        group: group
      });
    } else {
      res.redirect('/file-reviewer/dashboard');
    }
  } catch (error) {
    console.log(error);
    next(error);
  }
};

exports.openSearchRelationView = async function (req, res, next) {
  try {
    clearTempUploadFiles(req);
    req.session.extraDetailPartnerFiles = [];
    let title = "";
    let type = "natural";
    let review = await FileReviewModel.findById(req.params.reviewId);
    if (review && review.fileReview.username.toLowerCase() === req.session.user.username.toLowerCase()) {

      if (req.params.type && req.params.type !== "natural") {
        type = req.params.type;
      }
      res.render('file-reviewer/review-relation/search-review-relation-component', {
        user: req.session.user,
        title: title,
        review: review,
        canCreateRelation: false,
        type: type,
        id: req.params.reviewId,
      });
    } else {
      res.redirect('/file-reviewer/dashboard');
    }
  } catch (error) {
    console.log(error);
    next(error);
  }
};

exports.getRelationsByFilters = async function (req, res, next) {
  try {
    let review = await FileReviewModel.findById(req.params.reviewId);
    let relations = [];
    let filters = {};
    let query = [];
    let beneficialOwners = [];
    let shareholders = [];
    let directors = [];
    let clientsRelations = [];
    let otherBoDirectors = [];
    if (review && review.fileReview.username.toLowerCase() === req.session.user.username.toLowerCase()) {
      if (review.beneficialOwners) {
        beneficialOwners = [...review.beneficialOwners.natural, ...review.beneficialOwners.organization];
      }
      if (review.shareholders) {
        shareholders = [...review.shareholders.natural, ...review.shareholders.organization];
      }
      if (review.directors) {
        directors = [...review.directors.natural, ...review.directors.organization];
      }
      const boQuery = [];

      if (req.params.type === "natural") {

        filters = {
          name: req.body.name ? req.body.name : '',
          birthDate: req.body.birthDate ? req.body.birthDate : '',
          nationality: req.body.nationality ? req.body.nationality : ''
        };
        if (filters.name && filters.name.length > 2) {
          const filterName = {
            "$or": [{"details.fullName": {$regex: filters.name, $options: 'i'}},
              {"details.firstName": {$regex: filters.name, $options: 'i'}},
              {"details.middleName": {$regex: filters.name, $options: 'i'}},
              {"details.lastName": {$regex: filters.name, $options: 'i'}}]
          };
          boQuery.push({"relationReferenceId": {"$exists": false}}, {"personOrEntity": {"$nin": ["Entity", "ENTITY"]}},
            {
              "$or": [{"firstName": {$regex: filters.name, $options: 'i'}},
                {"middleName": {$regex: filters.name, $options: 'i'}},
                {"lastName": {$regex: filters.name, $options: 'i'}}]
            });
          query.push(filterName);
        }
        if (filters.birthDate && filters.birthDate.length > 2) {
          query.push({"details.birthDate": new Date(req.body.birthDate)});
          boQuery.push({"dateOfBirth": new Date(req.body.birthDate)});
        }
        if (filters.nationality && filters.nationality.length > 2) {
          query.push({"details.nationality": {$regex: req.body.nationality, $options: 'i'}});
          boQuery.push({"nationality": {$regex: req.body.nationality, $options: 'i'}});
        }
        if (query.length) {
          relations = await NaturalPersonModel.find({$and: query}).limit(200);
        }

        if (boQuery.length > 0) {
          otherBoDirectors = await BeneficialOwnerModel.find({$and: boQuery}).limit(100);
        }
      } else {

        filters = {
          organizationName: req.body.organizationName ? req.body.organizationName : '',
          incorporationNumber: req.body.incorporationNumber ? req.body.incorporationNumber : '',
          businessNumber: req.body.businessNumber ? req.body.businessNumber : ''
        };
        if (filters.organizationName && filters.organizationName.length > 2) {
          query.push({"details.organizationName": {$regex: req.body.organizationName, $options: 'i'}});
          boQuery.push({"relationReferenceId": {"$exists": false}}, {"personOrEntity": {"$in": ["ENTITY", "Entity"]} },
            {"fullName": {$regex: filters.organizationName, $options: 'i'}});
        }
        if (filters.incorporationNumber && filters.incorporationNumber.length > 2) {
          query.push({"details.incorporationNumber": {$regex: req.body.incorporationNumber, $options: 'i'}});
        }
        if (filters.businessNumber && filters.businessNumber.length > 2) {
          query.push({"details.businessNumber": {$regex: req.body.businessNumber, $options: 'i'}});
        }
        if (query.length) {
          relations = await OrganizationModel.find({$and: query}).limit(200);
        }
        if (filters.organizationName && filters.organizationName.length > 2) {
          clientsRelations = await ClientReviewModel.find({
            "$and":
              [{"companyName": {$regex: filters.organizationName, $options: 'i'}},
                {"organizationId": {$exists: false, $eq: null}}]
          }).limit(200)
        }

        if (boQuery.length > 0) {
          otherBoDirectors = await BeneficialOwnerModel.find({$and: boQuery}).limit(100);
        }

      }

      if (relations.length) {
        relations = relations.map((relation) => {
          const positions = [];
          if (beneficialOwners) {
            const exists = beneficialOwners.find((rel) =>
              rel.referenceId && rel.referenceId.toString() === relation._id.toString());
            if (exists) {
              positions.push('beneficial');
            }
          }

          if (shareholders) {
            const exists = shareholders.find((rel) =>
              rel.referenceId && rel.referenceId.toString() === relation._id.toString());
            if (exists) {
              positions.push('shareholder');
            }
          }

          if (directors) {
            const exists = directors.find((rel) =>
              rel.referenceId && rel.referenceId.toString() === relation._id.toString());
            if (exists) {
              positions.push('director');
            }
          }
          relation["positionGroups"] = positions;
          return relation
        });
      }

      if (clientsRelations) {
        clientsRelations = clientsRelations.map((client) => {
          return {
            _id: client._id,
            details: {
              organizationName: client.companyName,
              incorporationNumber: '',
              businessNumber: '',
              incorporationDate: ''
            },
            positionGroups: ['client']
          }
        });
        clientsRelations.forEach((client) => {
          const index = relations.findIndex((rel) => rel.details && rel.details.organizationName === client.details.organizationName);

          if (index === -1) {
            relations.push(client);
          }
        });
      }


      if (otherBoDirectors) {
        otherBoDirectors = otherBoDirectors.map((bo) => {
          let person;
          if (bo.personOrEntity && bo.personOrEntity.toUpperCase() === "ENTITY"){
            person = {
              _id: bo._id,
              details: {
                organizationName: bo.fullName,
                incorporationNumber: '',
                businessNumber: '',
                incorporationDate: bo.dateOfBirth
              },
              type: 'other-bo-director-entity',
              positionGroups: []
            }
          }
          else{
            person = {
              _id: bo._id,
              details: {
                fullName: bo.firstName + " " + bo.lastName,
                firstName: bo.firstName,
                middleName: bo.middleName,
                lastName: bo.lastName,
                occupation: bo.officeType,
                birthDate: bo.dateOfBirth,
                nationality: bo.nationality,
                countryBirth: bo.placeOfBirth,
              },
              type: 'other-bo-director',
              positionGroups: []
            }
          }
          return person

        });
        relations = [...relations, ...otherBoDirectors];
      }

      res.render('file-reviewer/review-relation/search-review-relation-component', {
        user: req.session.user,
        review: review,
        filters: filters,
        canCreateRelation: true,
        type: req.params.type,
        relations: relations,
        id: req.params.reviewId,
      });
    } else {
      res.redirect('/file-reviewer/dashboard');
    }
  } catch (error) {
    console.log(error);
    next(error);
  }
};

exports.assignPickedRelation = async function (req, res, next) {
  try {
    let review = await FileReviewModel.findById(req.params.reviewId);


    console.log(req.body);
    let relation;
    let clientReview;
    let groups = {
      "beneficial": "beneficialOwners",
      "shareholder": "shareholders",
      "director": "directors"
    };
    if (review && review.fileReview.username.toLowerCase() === req.session.user.username.toLowerCase()) {
      clientReview = await ClientReviewModel.findById(review.clientId);
      const reviewRelationGroups = req.body.relationGroup;

      if (req.params.type === "client") {
        const clientRelation = await ClientReviewModel.findById(req.params.relationId);
        const configTemplate = await ConfigModel.findOne({});
        relation = new OrganizationModel({
          type: 'corporate',
          lockedByFileReview: req.params.reviewId,
          createdAt: new Date(),
          updatedAt: new Date(),
          details: {
            organizationName: clientRelation.companyName,
            files: configTemplate.relationFiles.corporateFiles.details,
          },
          detailsPartner: {
            files: configTemplate.relationFiles.corporateFiles.detailsPartner,
          },
          principalAddress: {
            files: configTemplate.relationFiles.corporateFiles.principalAddress,
          },
          listedCompanyDetails: {
            active: false,
          },
          limitedCompanyDetails: {
            files: configTemplate.relationFiles.corporateFiles.limitedCompany,
            active: false,
          },
          mutualFundDetails: {
            active: false,
            files: configTemplate.relationFiles.corporateFiles.mutualFund,
          },
          foundation: {
            isFoundation: false,
            files: [],
          },
          worldCheck: {
            files: configTemplate.relationFiles.corporateFiles.worldCheck,
          },
          positions: [],
          partitionkey: 'organization',
        });
        const filesArray = [...relation.details.files, ...relation.detailsPartner.files, ...relation.mutualFundDetails.files,
          ...relation.foundation.files, ...relation.worldCheck.files];
        setRelationReferences(reviewRelationGroups, groups, 'organization',review, relation._id, filesArray,
          clientReview);
        clientRelation.organizationId = relation._id;
        await clientRelation.save();
      } else if (req.params.type.includes("other-bo-director")) {
        const importedBo = await BeneficialOwnerModel.findById(req.params.relationId);
        const configTemplate = await ConfigModel.findOne({});
        if (importedBo.placeOfBirth && importedBo.placeOfBirth.length <= 2){
          const country = countries.find((c) => c.alpha_2_code.toUpperCase() === importedBo.placeOfBirth.toUpperCase());
          if (country){
            importedBo.placeOfBirth = country.name;
          }
        }

        if (importedBo.nationality){
          const country = countries.find((c) => c.nationality.some(
            (item) => item === importedBo.nationality.toLowerCase()));
          if (country){
            importedBo.nationality = country.name;
          }
        }

        if(importedBo.principalAddress && importedBo.principalAddress.country && importedBo.principalAddress.country.length <=2){
          const country = countries.find((c) => c.alpha_2_code.toUpperCase() === importedBo.principalAddress.country.toUpperCase());
          if (country){
            importedBo.principalAddress.country = country.name;
          }
        }
        let filesArray;
        let isEntity = importedBo.personOrEntity && importedBo.personOrEntity.toUpperCase() === "ENTITY";
        if (isEntity){
          relation = new OrganizationModel({
            type: 'corporate',
            lockedByFileReview: req.params.reviewId,
            createdAt: new Date(),
            updatedAt: new Date(),
            details: {
              organizationName: importedBo.fullName,
              incorporationDate: importedBo.dateOfBirth,
              incorporationCountry: importedBo.placeOfBirth,
              files: configTemplate.relationFiles.corporateFiles.details,
            },
            detailsPartner: {
              files: configTemplate.relationFiles.corporateFiles.detailsPartner,
            },
            principalAddress: {
              primaryAddress: importedBo.principalAddress && importedBo.principalAddress.primaryAddress ?
                importedBo.principalAddress.primaryAddress : '',
              secondaryAddress: importedBo.principalAddress && importedBo.principalAddress.secondaryAddress ?
                importedBo.principalAddress.secondaryAddress : '',
              country:importedBo.principalAddress && importedBo.principalAddress.country ?
                importedBo.principalAddress.country : '',
              state:importedBo.principalAddress && importedBo.principalAddress.state ?
                importedBo.principalAddress.state : '',
              postalCode:importedBo.principalAddress && importedBo.principalAddress.postalCode ?
                importedBo.principalAddress.postalCode : '',
              city:importedBo.principalAddress && importedBo.principalAddress.city ?
                importedBo.principalAddress.city : '',
              files: configTemplate.relationFiles.corporateFiles.principalAddress,
            },
            mailingAddress: {
              primaryAddress: importedBo.mailingAddress && importedBo.mailingAddress.primaryAddress ?
                importedBo.mailingAddress.primaryAddress : '',
              secondaryAddress: importedBo.mailingAddress && importedBo.mailingAddress.secondaryAddress ?
                importedBo.mailingAddress.secondaryAddress : '',
              country:importedBo.mailingAddress && importedBo.mailingAddress.country ?
                importedBo.mailingAddress.country : '',
              state:importedBo.mailingAddress && importedBo.mailingAddress.state ?
                importedBo.mailingAddress.state : '',
              postalCode:importedBo.mailingAddress && importedBo.mailingAddress.postalCode ?
                importedBo.mailingAddress.postalCode : '',
              city:importedBo.mailingAddress && importedBo.mailingAddress.city ?
                importedBo.mailingAddress.city : '',
            },
            listedCompanyDetails: {
              active: false,
            },
            limitedCompanyDetails: {
              files: configTemplate.relationFiles.corporateFiles.limitedCompany,
              active: false,
            },
            mutualFundDetails: {
              active: false,
              files: configTemplate.relationFiles.corporateFiles.mutualFund,
            },
            foundation: {
              isFoundation: false,
              files: [],
            },
            worldCheck: {
              files: configTemplate.relationFiles.corporateFiles.worldCheck,
            },
            positions: [],
            partitionkey: 'naturalperson',
          });

          filesArray = [...relation.details.files, ...relation.detailsPartner.files, ...relation.mutualFundDetails.files,
            ...relation.foundation.files, ...relation.worldCheck.files];
        }
        else{
          relation = new NaturalPersonModel({
            type: "natural",
            lockedByFileReview: req.params.reviewId,
            pep: false,
            createdAt: new Date(),
            updatedAt: new Date(),
            details: {
              files: configTemplate.relationFiles.naturalFiles.details,
              fullName: importedBo.middleName ? importedBo.firstName + " " + importedBo.middleName + " " + importedBo.lastName :
                importedBo.firstName + " " + importedBo.lastName,
              firstName: importedBo.firstName,
              middleName: importedBo.middleName,
              lastName: importedBo.lastName,
              occupation: importedBo.officeType,
              birthDate: importedBo.dateOfBirth,
              nationality: importedBo.nationality,
              countryBirth: importedBo.placeOfBirth,
            },
            identification: {
              files:configTemplate.relationFiles.naturalFiles.identification,
              identificationType: importedBo.idType ? importedBo.idType.toLowerCase() : '',
              issueCountry:  importedBo.passportCountry ? importedBo.passportCountry : '',
              expiryDate: importedBo.passportExpiration ? importedBo.passportExpiration : undefined,
              valid: false,
            },
            residentialAddress: {
            },
            mailingAddress: {
              primaryAddress: importedBo.mailingAddress && importedBo.mailingAddress.primaryAddress ? importedBo.mailingAddress.primaryAddress : '',
              secondaryAddress:importedBo.mailingAddress && importedBo.mailingAddress.secondaryAddress ? importedBo.mailingAddress.secondaryAddress : '',
              country: importedBo.mailingAddress && importedBo.mailingAddress.country ? importedBo.mailingAddress.country : '',
              state: importedBo.mailingAddress && importedBo.mailingAddress.state ? importedBo.mailingAddress.state : '',
              postalCode: importedBo.mailingAddress && importedBo.mailingAddress.postalCode ? importedBo.mailingAddress.postalCode : '',
              city: importedBo.mailingAddress && importedBo.mailingAddress.city ? importedBo.mailingAddress.city : '',
            },
            taxResidence: {
              confirmation: false,
              taxResidence: '',
            },
            advisorDetails: {
            },
            principalAddress: {
              primaryAddress: importedBo.principalAddress.primaryAddress,
              secondaryAddress: importedBo.principalAddress.secondaryAddress,
              country: importedBo.principalAddress.country,
              state: importedBo.principalAddress.state,
              postalCode: importedBo.principalAddress.postalCode,
              city: importedBo.principalAddress.city,
            },
            pepDetails: {
              files: configTemplate.relationFiles.naturalFiles.pep,
              confirmAdditionalComments: false,
            },
            worldCheck: {
              files: configTemplate.relationFiles.naturalFiles.worldCheck,
            },
            createdByImportedBo: true,
            partitionkey: 'naturalperson',
          });
          const electronicIdInfoFiles =relation.electronicIdInfo && relation.electronicIdInfo.files ?
            relation.electronicIdInfo.files : [];
            filesArray = [...relation.identification.files, ...relation.worldCheck.files, ...relation.pepDetails.files,
              ...electronicIdInfoFiles];
        }
        setRelationReferences(reviewRelationGroups, groups, isEntity ? 'organization' : 'natural',
          review, relation._id, filesArray, clientReview);


        importedBo.relationReferenceId = relation._id;
        await importedBo.save();
      } else {
        if (req.params.type === "natural") {
          relation = await NaturalPersonModel.findById(req.params.relationId);
          const electronicIdInfoFiles =relation.electronicIdInfo && relation.electronicIdInfo.files ?
            relation.electronicIdInfo.files : [];
          const filesArray = [...relation.identification.files, ...relation.worldCheck.files, ...relation.pepDetails.files,
            ...electronicIdInfoFiles];
          setRelationReferences(reviewRelationGroups, groups, 'natural',review, relation._id, filesArray, clientReview);
        } else {
          relation = await OrganizationModel.findById(req.params.relationId);
          const filesArray = [...relation.details.files, ...relation.detailsPartner.files, ...relation.mutualFundDetails.files,
            ...relation.foundation.files, ...relation.worldCheck.files];

           setRelationReferences(reviewRelationGroups, groups, 'organization',review, relation._id, filesArray,
             clientReview);
          if (relation && relation.positions) {
            let positionsId = [];
            relation.positions.forEach((pos) => {
              positionsId.push(pos.referenceId);
              const existPosition = review.positions.findIndex(
                (p) => p && p.referenceId.toString() === pos._id.toString());
              if (existPosition === -1) {
                const positionInfo = new positionReview({
                  referenceId: pos._id
                });
                review.positions.push(positionInfo);
              }

            });

            positionsId = [...new Set(positionsId)];

            await NaturalPersonModel.updateMany({_id: {$in: positionsId}, lockedByFileReview: null},
              {$set: {lockedByFileReview: review._id}});
          }

        }
        if (relation && !relation.lockedByFileReview) {
          relation.lockedByFileReview = review._id;
        }
      }
      await relation.save();
      await clientReview.save();
      await review.save();
      return res.status(200).end();
    } else {
      return res.status(404).end();
    }
  } catch (error) {
    console.log(error);
    next(error);
  }
};

exports.openRelation = async function (req, res, next) {
  try {
    clearTempUploadFiles(req);
    req.session.extraDetailPartnerFiles = [];
    let title = "";
    let relationInformation = {};
    let percentage;

    let file = await FileReviewModel.findById(req.params.companyId);
    if (file && file.fileReview.username.toLowerCase() === req.session.user.username.toLowerCase()) {
      const clientReview = await ClientReviewModel.findById(file.clientId);
      let relationReview;
      if (req.params.type === "natural") {
        percentage = clientReview.naturalRelations.find((r) =>
          r.referenceId.toString() === req.params.relationId && r.group === "shareholder");
        relationReview = await NaturalPersonModel.findById(req.params.relationId);
      } else {
        percentage = clientReview.organizationRelations.find((r) =>
          r.referenceId.toString() === req.params.relationId && r.group === "shareholder");
        relationReview = await OrganizationModel.findById(req.params.relationId);
      }

      if (relationReview) {
        let relationGroupKey;
        if (req.params.relationGroup === "beneficial") {
          title = 'Add Beneficial Owner: ' + file.companyName;
          relationGroupKey = "beneficialOwners";
        } else if (req.params.relationGroup === "shareholder") {
          title = 'Add Shareholder: ' + file.companyName;
          relationGroupKey = "shareholders";

          if (percentage) {
            relationReview['additional'] = {percentage: percentage['percentage']};
          }
        } else if (req.params.relationGroup === "director") {
          title = 'Add Director: ' + file.companyName;
          relationGroupKey = "directors";
        }
        const relationGroup = file[relationGroupKey];
        if (relationGroup) {
          if (req.params.type === "natural") {
            relationInformation = relationGroup.natural.find((r) =>
              r.referenceId.toString() === relationReview._id.toString());
          } else {
            relationInformation = relationGroup.organization.find((r) =>
              r.referenceId.toString() === relationReview._id.toString());
          }
        }

        res.render('file-reviewer/review-relation/edit-review-relation-component', {
          user: req.session.user,
          title: title,
          file: file,
          relation: relationReview,
          reviewGroup: req.params.relationGroup,
          relationInformation: relationInformation,
          id: req.params.companyId,
          newRelation: false,
        });
      } else {
        res.redirect('/file-reviewer/dashboard');
      }
    } else {
      res.redirect('/file-reviewer/dashboard');
    }
  } catch (error) {
    console.log(error);
    next(error);
  }
};

exports.getRelationGroupsByReview = async function (req, res) {
  try {
    let review = await FileReviewModel.findById(req.params.reviewId);
    let groups = [];

    let naturalsRelations = [];
    let organizationRelations = [];
    if (review && review.fileReview.username.toLowerCase() === req.session.user.username.toLowerCase()) {
      const clientReview = await ClientReviewModel.findById(review.clientId);
      if (clientReview.naturalRelations.length) {
        naturalsRelations = clientReview.naturalRelations.filter((relation) =>
          relation.referenceId.toString() === req.params.relationId);
      }
      if (clientReview.organizationRelations.length) {
        organizationRelations = clientReview.organizationRelations.filter((relation) =>
          relation.referenceId.toString() === req.params.relationId);
      }

      const relations = [...naturalsRelations, ...organizationRelations];

      if (relations) {
        groups = relations.map((rel) => rel.group);
      }

      return res.status(200).json({"success": true, "groups": groups})
    }
    return res.status(400).json({"success": false})
  } catch (e) {
    console.log(e);
    return res.status(500).end();
  }
};

exports.getTemplateFile = async function (req, res) {
  try {
    const configTemplate = await ConfigModel.findOne({});
    if (configTemplate) {
      if (req.query.fieldToSearch) {
        const templateField = configTemplate[req.query.fieldToSearch];

        if (templateField.length === 0) {
          return res.status(400).json({"success": false})
        }
        const fileGroup = templateField[req.query.group + "Files"];
        if (!fileGroup || (fileGroup && !fileGroup.lenght === 0)) {
          return res.status(400).json({"success": false})
        }
        const files = fileGroup[req.query.fileType];
        if (files) {
          const file = files[req.query.row];
          file["id"] = uuidv4();

          if (req.query.newFile) {
            req.session.extraDetailPartnerFiles.push(file);
          }

          return res.status(200).json({"success": true, "data": file})
        } else {
          return res.status(400).json({"success": false})
        }

      } else {
        return res.status(200).json({"success": true, "data": configTemplate})
      }
    }
    return res.status(400).json({"success": false})
  } catch (e) {
    console.log(e);
    return res.status(500).end();
  }
};

exports.saveRelation = async function (req, res, next) {
  try {
    const configTemplate = await ConfigModel.findOne({});
    let relationReview;
    let sections;
    let reviewRelationObject;

    let percentage = '';
    let paramsGroups = '';
    const review = await FileReviewModel.findById(req.params.companyId);
    if (req.params.relationGroups && req.params.relationGroups !== '') {
      paramsGroups = req.params.relationGroups.split('-');
    } else if (req.params.relationGroup && req.params.relationGroup !== '') {
      paramsGroups = req.params.relationGroup;
    }

    const reviewRelationGroups = {};
    if (paramsGroups.includes("beneficial")) {
      reviewRelationGroups["beneficial"] = "beneficialOwners";
    }
    if (paramsGroups.includes("shareholder")) {
      reviewRelationGroups["shareholder"] = "shareholders";
    }
    if (paramsGroups.includes("director")) {
      reviewRelationGroups["director"] = "directors";
    }


    if (req.params.relationId) {
      relationReview = req.params.type === "natural" ?
        await NaturalPersonModel.findById(req.params.relationId) :
        await OrganizationModel.findById(req.params.relationId);
    }

    if (req.params.type === "natural" || req.body.relationType === "natural") {
      sections = ["details", "identification", "residentialAddress", "mailingAddress", "taxResidence", "advisorDetails",
        "principalAddress", "pepDetails", "worldCheck"];
      reviewRelationObject = new naturalReview({});

    } else {
      sections = ["details", "detailsPartner", "principalAddress", "mailingAddress", "listedCompanyDetails",
        "limitedCompanyDetails", "mutualFundDetails", "foundation", "worldCheck"];
      reviewRelationObject = new organizationReview({});
    }

    if (!relationReview) {
      const fileGroupName = req.body.relationType + "Files";

      const fileGroupFiles = configTemplate.relationFiles[fileGroupName];
      if (req.body.relationType === "natural") {
        relationReview = new NaturalPersonModel({
          type: req.body.relationType,
          lockedByFileReview: req.params.companyId,
          pep: req.body['pepDetails'].confirmation === "YES",
          createdAt: new Date(),
          updatedAt: new Date(),
          details: {
            files: fileGroupFiles.details ?
              fileGroupFiles.details : [],
            fullName: req.body.details.fullName,
            firstName: req.body.details.firstName,
            middleName: req.body.details.middleName,
            lastName: req.body.details.lastName,
            occupation: req.body.details.occupation,
            birthDate: req.body.details.birthDate,
            nationality: req.body.details.nationality,
            countryBirth: req.body.details.countryBirth,
          },
          identification: {
            files: fileGroupFiles.identification ?
              fileGroupFiles.identification : [],
            identificationType:  req.body.identification && req.body.identification.identificationType ?
              req.body.identification.identificationType : '' ,
            issueCountry:  req.body.identification &&  req.body.identification.issueCountry ?
              req.body.identification.issueCountry : '',
            expiryDate: req.body.identification &&  req.body.identification.expiryDate ?
              req.body.identification.expiryDate : '',
            valid: !!(req.body.identification && req.body.identification.valid),
          },
          residentialAddress: {
            primaryAddress: req.body.residentialAddress.primaryAddress,
            secondaryAddress: req.body.residentialAddress.secondaryAddress,
            country: req.body.residentialAddress.country,
            state: req.body.residentialAddress.state,
            postalCode: req.body.residentialAddress.postalCode,
            city: req.body.residentialAddress.city,
          },
          mailingAddress: {
            primaryAddress: req.body.mailingAddress.primaryAddress,
            secondaryAddress: req.body.mailingAddress.secondaryAddress,
            country: req.body.mailingAddress.country,
            state: req.body.mailingAddress.state,
            postalCode: req.body.mailingAddress.postalCode,
            city: req.body.mailingAddress.city,
          },
          taxResidence: {
            confirmation: !!(req.body.taxResidence && req.body.taxResidence.confirmation),
            taxResidence: req.body.taxResidence.taxResidence,
          },
          advisorDetails: {
            firstName: req.body.advisorDetails.firstName,
            middleName: req.body.advisorDetails.middleName,
            lastName: req.body.advisorDetails.lastName,
            firmName: req.body.advisorDetails.firmName,
            phone: req.body.advisorDetails.phone,
            email: req.body.advisorDetails.email,
            nationality: req.body.advisorDetails.nationality,
            incorporationCountry: req.body.advisorDetails.incorporationCountry,
          },
          principalAddress: {
            primaryAddress: req.body.principalAddress.primaryAddress,
            secondaryAddress: req.body.principalAddress.secondaryAddress,
            country: req.body.principalAddress.country,
            state: req.body.principalAddress.state,
            postalCode: req.body.principalAddress.postalCode,
            city: req.body.principalAddress.city,
          },
          pepDetails: {
            files: fileGroupFiles.pep ? fileGroupFiles.pep : [],
            information: req.body.pepDetails.information,
            additionalComments: req.body.pepDetails.additionalComments,
            confirmAdditionalComments: false,
          },
          worldCheck: {
            files: fileGroupFiles.worldCheck ? fileGroupFiles.worldCheck : [],
          },
          partitionkey: 'naturalperson',
        });
      } else {
        relationReview = new OrganizationModel({
          type: req.body.relationType,
          lockedByFileReview: req.params.companyId,
          createdAt: new Date(),
          updatedAt: new Date(),
          details: {
            organizationName: req.body.details.organizationName,
            incorporationNumber: req.body.details.incorporationNumber,
            taxResidence: req.body.details.taxResidence,
            businessNumber: req.body.details.businessNumber,
            incorporationDate: req.body.details.incorporationDate,
            incorporationCountry: req.body.details.incorporationCountry,
            files: fileGroupFiles.details ? fileGroupFiles.details : [],
          },
          detailsPartner: {
            files: fileGroupFiles.detailsPartner ? fileGroupFiles.detailsPartner : [],
          },
          principalAddress: {
            primaryAddress: req.body.principalAddress.primaryAddress,
            secondaryAddress: req.body.principalAddress.secondaryAddress,
            country: req.body.principalAddress.country,
            state: req.body.principalAddress.state,
            postalCode: req.body.principalAddress.postalCode,
            city: req.body.principalAddress.city,
            files: fileGroupFiles.principalAddress ? fileGroupFiles.principalAddress : [],
          },
          mailingAddress: {
            primaryAddress: req.body.mailingAddress.primaryAddress,
            secondaryAddress: req.body.mailingAddress.secondaryAddress,
            country: req.body.mailingAddress.country,
            state: req.body.mailingAddress.state,
            postalCode: req.body.mailingAddress.postalCode,
            city: req.body.mailingAddress.city,
          },
          listedCompanyDetails: {
            active: !!(req.body.listedCompanyDetails && req.body.listedCompanyDetails.active),
            stockCode: req.body.listedCompanyDetails ? req.body.listedCompanyDetails.stockCode : '',
          },
          limitedCompanyDetails: {
            files: fileGroupFiles.limitedCompany ? fileGroupFiles.limitedCompany : [],
            active: !!(req.body.limitedCompanyDetails && req.body.limitedCompanyDetails.active),
            registrationNumber: req.body.limitedCompanyDetails ? req.body.limitedCompanyDetails.registrationNumber : '',
            registrationDate: req.body.limitedCompanyDetails ? req.body.limitedCompanyDetails.registrationDate : null,
          },
          mutualFundDetails: {
            active: !!(req.body.mutualFundDetails && req.body.mutualFundDetails.active),
            files: fileGroupFiles.mutualFund ? fileGroupFiles.mutualFund : [],
          },
          foundation: {
            active: !!(req.body.foundation && req.body.foundation.active),
            isFoundation: req.body.relationType === "foundation",
            country: req.body.foundation ? req.body.foundation.country : '',
            files: fileGroupFiles.foundation ? fileGroupFiles.foundation : [],
          },
          worldCheck: {
            files: fileGroupFiles.worldCheck ? fileGroupFiles.worldCheck : [],
          },
          positions: [],
          partitionkey: 'organization',
        });
      }
    }

    if (req.session.extraDetailPartnerFiles.length) {
      relationReview.detailsPartner.files = [...relationReview.detailsPartner.files, ...req.session.extraDetailPartnerFiles]
    }

    const relationObject = setFormValuesToRelation(relationReview, reviewRelationObject, req.body,
      sections, req.session.relationFiles);
    let filesArray;
    if (relationReview.type === "natural") {
      const electronicIdInfoFiles =relationReview.electronicIdInfo && relationReview.electronicIdInfo.files ?
        relationReview.electronicIdInfo.files : [];
      filesArray = [...relationReview.identification.files, ...relationReview.worldCheck.files, ...relationReview.pepDetails.files, ...electronicIdInfoFiles];
    } else {
      filesArray = [...relationReview.details.files, ...relationReview.detailsPartner.files, ...relationReview.mutualFundDetails.files,
        ...relationReview.foundation.files, ...relationReview.worldCheck.files];
    }

    filesArray.forEach((file) => {
      let typeValidation = new fileTypeValidation({referenceFile: file.id, validated: false});
      const reviewValidation = reviewRelationObject.files.findIndex((rel) => rel && rel.referenceFile === file.id);
      if (reviewValidation === -1) {
        reviewRelationObject.files.push(typeValidation);
      }
    });
    relationReview = relationObject["relation"];
    reviewRelationObject = relationObject["relationReviewObject"];

    if (relationReview.type === "natural") {
      relationReview.pep = req.body['pepDetails'] && req.body['pepDetails'].confirmation === "YES";
      relationReview.pepDetails.confirmAdditionalComments = req.body['pepDetails'] && req.body['pepDetails'].confirmAdditionalComments === "YES";
      if (!relationReview.pepDetails.confirmAdditionalComments) {
        relationReview.pepDetails.additionalComments = '';
      }
      relationReview.identification.valid = !!(req.body.identification && req.body.identification.valid);
      relationReview.taxResidence.confirmation = !!(req.body.taxResidence && req.body.taxResidence.confirmation);

      const hasElectronicId = req.body.electronicIdInfo && req.body.electronicIdInfo.isElectronicId === "YES";
      let electronicIdInfo = relationReview.electronicIdInfo || {};
      if (hasElectronicId){
        const emailType = req.body.electronicIdInfo && req.body.electronicIdInfo.emailType ? req.body.electronicIdInfo.emailType : '';
        electronicIdInfo.isElectronicId = true;
        electronicIdInfo.emailType = emailType;
        electronicIdInfo.email = emailType === "default" ?  req.body.electronicIdInfo.defaultEmail : req.body.electronicIdInfo.email;
        electronicIdInfo.status =  !electronicIdInfo.status || electronicIdInfo.status === "RECEIVED" ||
        electronicIdInfo.status === "CANCELLED"  ? 'NOT STARTED' : electronicIdInfo.status;

        const relationElectronicReference = {
          relationId: relationReview._id,
          uuid:  "",
          complete: false,
        };

        if (!review.pendingElectronicIds) {
          review.pendingElectronicIds = [relationElectronicReference];
        }
        else{
          const index = review.pendingElectronicIds.findIndex((e) =>
            e.relationId && e.relationId.toString() === relationReview._id.toString());

          if (index === -1){
            review.pendingElectronicIds.push(relationElectronicReference);
          }
        }
      }
      else{
        electronicIdInfo.isElectronicId = false;
        electronicIdInfo.status = "RECEIVED";
        electronicIdInfo.email = "";
      }
      if (!electronicIdInfo.files){
        electronicIdInfo.files = [
          {
            present : false,
            validated : false,
            _id: new ObjectId(),
            uploadFiles : [],
            internal : "Electronic ID",
            external : "Electronic ID",
            fileGroup : "identification",
            explanation : "",
            comments : "",
            id : uuidv4()

          }
        ];
      }
      relationReview.electronicIdInfo = electronicIdInfo;
    } else {
      relationReview.listedCompanyDetails.active = !!(req.body.listedCompanyDetails && req.body.listedCompanyDetails.active);
      relationReview.limitedCompanyDetails.active = !!(req.body.limitedCompanyDetails && req.body.limitedCompanyDetails.active);
      relationReview.mutualFundDetails.active = !!(req.body.mutualFundDetails && req.body.mutualFundDetails.active);

      if (req.params.type === "foundation" || req.body.relationType === "foundation") {
        relationReview.foundation.active = !!(req.body.foundation && req.body.foundation.active);
      }
    }

    if (paramsGroups.includes("shareholder")) {
      percentage = req.body.additional.percentage;
      reviewRelationObject['additional'] = {"complete": !!req.body.additional.correct}

    }

    const savedRelation = await relationReview.save();
    reviewRelationObject.referenceId = savedRelation._id;


    let clientRelation = await ClientReviewModel.findById(review.clientId);
    for (let [paramGroup, group] of Object.entries(reviewRelationGroups)) {
      const reviewGroup = review[group];

      const addRelationToClient = {
        referenceId: savedRelation._id,
        group: paramGroup
      };

      if (percentage && paramGroup === 'shareholder') {
        addRelationToClient["percentage"] = percentage;
      }
      if (reviewGroup) {
        let relationsByType = relationReview.type === "natural" ? reviewGroup.natural : reviewGroup.organization;
        const relationIndex = relationsByType.findIndex((r) => r.referenceId.toString() === reviewRelationObject.referenceId.toString());
        relationsByType = relationIndex > -1 ? relationsByType.set(relationIndex, reviewRelationObject) : relationsByType.push(reviewRelationObject);

        if (relationReview.type === "natural") {
          reviewGroup.natural = relationsByType;
          const index = clientRelation.naturalRelations.findIndex((r) =>
            r.referenceId.toString() === addRelationToClient.referenceId.toString() && r.group === paramGroup);
          if (index > -1) {
            clientRelation.naturalRelations[index] = addRelationToClient;
          } else {
            clientRelation.naturalRelations.push(addRelationToClient);
          }
          clientRelation.markModified("naturalRelations");
        } else {
          reviewGroup.organization = relationsByType;
          const index = clientRelation.organizationRelations.findIndex((r) =>
            r.referenceId.toString() === addRelationToClient.referenceId.toString() && r.group === paramGroup);
          if (index > -1) {
            clientRelation.organizationRelations[index] = addRelationToClient;
          } else {
            clientRelation.organizationRelations.push(addRelationToClient);
          }
          clientRelation.markModified("organizationRelations");
        }
        review[group] = reviewGroup;

      }
    }

    await clientRelation.save();
    await review.save();


    return res.status(200).end();

  } catch (e) {
    console.log("error: ", e);
    next(e);
  }
};

exports.deleteRelation = async function (req, res) {
  try {
    let reviewGroup;
    let reviewGroupKey;
    const relationType = req.body.type === "natural" ? "natural" : "organization";
    const relationId = req.params.relationId;
    let review = await FileReviewModel.findById(req.params.companyId);
    if (!review) {
      return res.status(404).json({"success": false, "message": "FileReview not found"})
    }
    let clientReview = await ClientReviewModel.findById(review.clientId);

    if (!clientReview) {
      return res.status(404).json({"success": false, "message": "Client information not found"})
    }

    if (req.body.group === 'beneficial') {
      reviewGroupKey = "beneficialOwners";
    } else if (req.body.group === 'shareholder') {
      reviewGroupKey = "shareholders";
    } else {
      reviewGroupKey = "directors";
    }

    reviewGroup = review[reviewGroupKey];
    if (reviewGroup) {
      const index = reviewGroup[relationType].findIndex((relation) =>
        relation.referenceId.toString() === relationId);
      if (index > -1) {
        reviewGroup[relationType].splice(index, 1);
        review[reviewGroupKey] = reviewGroup;
      }
    }

    const clientGroup = clientReview[relationType + "Relations"];
    if (clientGroup) {
      const index = clientGroup.findIndex((relation) =>
        relation.referenceId.toString() === req.params.relationId && relation.group === req.body.group);
      if (index > -1) {
        clientGroup.splice(index, 1);
        clientReview[relationType + "Relations"] = clientGroup;
      }
    }

    let validateLooked = [...clientReview.naturalRelations.filter((ref) => ref && ref.referenceId.toString() === relationId),
      ...clientReview.organizationRelations.filter((ref) => ref && ref.referenceId.toString() === relationId)];
    if (!validateLooked.length) {
      if (relationType === 'natural') {

        if (review.pendingElectronicIds && review.pendingElectronicIds.length > 0){
          const index = review.pendingElectronicIds.findIndex((pendingElectronic) =>
            pendingElectronic.relationId.toString() === req.params.relationId);
          if (index > -1) {
            review.pendingElectronicIds.splice(index, 1);
            review.markModified("pendingElectronicIds");
          }
        }

        const naturalPerson = await NaturalPersonModel.findById(relationId);
        if (naturalPerson && naturalPerson.lockedByFileReview &&
          naturalPerson.lockedByFileReview.toString() === req.params.companyId) {
          naturalPerson.lockedByFileReview = null;
          await naturalPerson.save();
        }
      } else {
        const organization = await OrganizationModel.findById(relationId);

        if (organization && organization.positions) {

          const positionIds = [...new Set(organization.positions.map(pos => pos.referenceId))];

          await NaturalPersonModel.updateMany(
            {_id: {$in: positionIds}, lockedByFileReview: ObjectId(req.params.companyId)},
            {$set: {lockedByFileReview: null}});

          organization.positions.forEach((pos) => {
            const posIndex = review.positions.findIndex(
              (p) => p && p.referenceId.toString() === pos._id.toString());

            if (posIndex > -1) {
              review.positions.splice(posIndex, 1);
            }

          });
        }
        if (organization && organization.lockedByFileReview &&
          organization.lockedByFileReview.toString() === req.params.companyId) {
          organization.lockedByFileReview = null;
        }
        await organization.save();
      }

    }
    await clientReview.save();
    review.markModified("positions");
    await review.save();
    return res.status(200).json({"success": true})
  } catch (e) {
    console.log(e);
    return res.status(500).end();
  }
};

exports.peekRelation = async function (req, res) {
  try {
    let relation;
    let relationInformation;
    let reviewGroup;
    let relationFiles = [];
    const review = await FileReviewModel.findById(req.params.companyId);
    if (!review) {
      return res.status(404).end();
    }
    if (req.query.group === "beneficial") {
      reviewGroup = review.beneficialOwners;
    } else if (req.query.group === "shareholder") {
      reviewGroup = review.shareholders;
    } else {
      reviewGroup = review.directors;
    }

    if (req.query.type === 'natural') {
      relationInformation = reviewGroup.natural.find(
        (naturalRelation) => naturalRelation.referenceId.toString() === req.query.relationId.toString());
      relation = await NaturalPersonModel.findById(req.query.relationId, function (err) {
        if (err) console.log('error peeking beneficial owner');
      });
      const electronicIdInfoFiles = relation.electronicIdInfo && relation.electronicIdInfo.files ?
        relation.electronicIdInfo.files : [];
      relationFiles = [...relation.identification.files, ...relation.pepDetails.files, ...relation.worldCheck.files,
        ...electronicIdInfoFiles];
    } else {
      relationInformation = reviewGroup.organization.find(
        (OrgRelation) => OrgRelation.referenceId.toString() === req.query.relationId.toString());
      relation = await OrganizationModel.findById(req.query.relationId, function (err) {
        if (err) console.log('error peeking corporate beneficial owner');
      }).populate('positions.referenceId', '_id details lockedByFileReview');
      relationFiles = [...relation.details.files, ...relation.detailsPartner.files, ...relation.mutualFundDetails.files,
        ...relation.foundation.files, ...relation.worldCheck.files];
    }
    for (let key of Object.keys(relation.toObject())) {
      const value = relation[key];
      if (value && value.files) {
        value.files.forEach((file) => {
          if (relationInformation && relationInformation.files) {
            const reviewValidation = relationInformation.files.find(
              (rel) => rel && rel.referenceFile === file.id && rel.validated === true);
            if (reviewValidation) {
              file["validated"] = true;
            }
          }
        });
      }
    }

    return res.json({
      success: true,
      relation: relation,
      relationInformation: relationInformation,
      relationFiles: relationFiles
    });
  } catch (error) {
    console.log(error);
    return res.status(500).end();
  }
};

exports.showRelationViewByGroup = async function (req, res, next) {
  try {
    clearTempUploadFiles(req);
    const partials = {
      natural: "partials/file-reviewer/review-relations/natural-form-component",
      corporate: "partials/file-reviewer/review-relations/corporate-form-component",
      foundation: "partials/file-reviewer/review-relations/foundation-form-component",
      trust: "partials/file-reviewer/review-relations/trust-form-component",
      limited: "partials/file-reviewer/review-relations/limited-partnership-form-component"
    };

    const configTemplate = await ConfigModel.findOne({});

    if (!configTemplate) {
      res.redirect('/file-reviewer/dashboard');
      console.log("Configuration not found");
    }

    const group = req.query.group ? req.query.group : "";
    const groupTemplate = group + "Files";
    const relation = configTemplate.relationFiles[groupTemplate];
    res.render(partials[group], {
      layout: false,
      relation: relation,
      newRelation: true,
      group: group
    });
  } catch (error) {
    console.log(error);
    next(error);
  }
};

exports.startNewElectronicIdRequest =  async function (req, res) {
  try {
    const review = await FileReviewModel.findById(req.params.reviewId);
    let status;
    let message;
    if (!review){
      return res.status(404).json({status: 404, message: "File review not found"});
    }

    let relation = await NaturalPersonModel.findById(req.params.relationId);

    if (relation === -1){
      return res.status(404).json({status: 404, message: "Relation not found"});
    }

    const electronicIdInfo = relation.electronicIdInfo || {idPalInformation: [], comments: [], files: []};

    if (req.body.email){
      electronicIdInfo.email = req.body.email;
    }
    else{
      return  res.json({status: 400, message: "The electronic E-mail is invalid"});
    }

    if (req.body.changeEmail === "true"){
      electronicIdInfo.emailType = 'free email'
    }else{
      electronicIdInfo.emailType = req.body.emailType ? req.body.emailType : 'free email';
    }

    electronicIdInfo.allowNewRequest = false;
    electronicIdInfo.isElectronicId = true;
    if (electronicIdInfo  && electronicIdInfo.email){

      const palResponse = await idPalController.generateUuid(relation._id.toString());

      if (palResponse && palResponse.status === 200){
        const relationElectronicReference = {
          relationId: relation._id,
          uuid:  palResponse.uuid,
          complete: false,
        };
        const url = process.env.CLIENTPORTAL_APP_HOST + "/idpal?uuid=" + palResponse.uuid;
        let email = MailFormatter.generateFileReviewIdPalEmail(url, req.body.emailTemplate ?
          req.body.emailTemplate : 'new-invitation-template');
        let sentEmailResult = await MailController.asyncSend(
          electronicIdInfo.email,
          review.companyName + ' - ID Verification',
          email.textString,
          email.htmlString
        );
        if (electronicIdInfo.uuid){
          const cancelUuidResponse = await idPalController.cancelUuid(electronicIdInfo.uuid);
          if (cancelUuidResponse && cancelUuidResponse.status === 200){
            electronicIdInfo.comments.push({
              username: req.session.user.username,
              comment: "A new electronic ID request was created, therefore the previous request with uuid "+electronicIdInfo.uuid +
                "  was automatically canceled at " + moment().format('DD-MM-YYYY'),
              date: new Date(),
              status: electronicIdInfo.status,
            });

            const index = review.pendingElectronicIds.findIndex((e) =>
              e.uuid === electronicIdInfo.uuid);

            if (index > -1) {
              review.pendingElectronicIds.splice(index, 1);
            }
          }

        }

        electronicIdInfo.uuid =palResponse.uuid;

        if (!review.pendingElectronicIds) {
          review.pendingElectronicIds = [relationElectronicReference];
        }
        else{
          const index = review.pendingElectronicIds.findIndex((e) =>
            e.relationId && e.relationId.toString() === relation._id.toString());

          if (index > -1){
            review.pendingElectronicIds[index] = relationElectronicReference;
          }
          else{
            review.pendingElectronicIds.push(relationElectronicReference);
          }
        }

        if (sentEmailResult.accepted) {
          electronicIdInfo.electronicIdInvitationSent = true;
          electronicIdInfo.status = "IN PROGRESS";
          electronicIdInfo.invitationDate = new Date();
          electronicIdInfo.comments.push({
            username: req.session.user.username,
            comment: req.session.user.username + " has send a reminder to " +
              electronicIdInfo.email + " at " + moment().format('DD-MM-YYYY'),
            date: new Date(),
            status: electronicIdInfo.status,
          });
          review.comments.push(({
            username:req.session.user.username,
            role: "FR",
            comment: req.session.user.username + " send new invitation to " + electronicIdInfo.email +
              " requesting electronic id information at " + moment().format('YYYY-MM-DD'),
            date: new Date(),
          }));
          status = 200;
          message= "Success";
        }
        else{
          electronicIdInfo.electronicIdInvitationSent = false;
          electronicIdInfo.status = "NOT SENT";
          electronicIdInfo.comments.push({
            username: req.session.user.username,
            comment: "Error sending email invitation",
            date: new Date(),
            status: electronicIdInfo.status,
          });

          status = 400;
          message= "Error sending email to id-pal invitation";
        }

      }

      else{
        message = "Error sending invitation";
        status = 500;
        electronicIdInfo.electronicIdInvitationSent = false;
        electronicIdInfo.status = "NOT SENT";
        electronicIdInfo.comments.push({
          username:req.session.user.username,
          comment: "Error sending invitation: " + (palResponse.message && palResponse.message.error ?
            palResponse.message.error : palResponse.message) ,
          date: new Date(),
          status: electronicIdInfo.status,
        });
      }
    }
    else{
      status = 404;
      message = "Error sending id-pal invitation: Email not found";
      electronicIdInfo.comments.push({
        username:req.session.user.username,
        comment: "Error sending id-pal invitation: Email not found",
        date: new Date(),
        status: "NOT SENT"
      })
    }
    relation.electronicIdInfo = electronicIdInfo;
    relation.markModified('electronicIdInfo');
    await relation.save();
    review.markModified('pendingElectronicIds');
    await review.save();

    res.json({status: status, message: message});
  } catch (error) {
    console.log(error);
    return res.status(500).json({status: 500, error: "Internal Server Error"});
  }
};

exports.getTempUploadFiles = function (session, key) {
  if (key) {
    if (session.relationFiles && session.relationFiles[key]) {
      return session.relationFiles[key];
    } else {
      return null;
    }
  } else {
    return session.relationFiles;
  }


};

exports.setTempUploadFiles = function (req, key, value) {
  if (req.session && req.session.relationFiles && req.session.relationFiles[key]) {
    return req.session.relationFiles[key] = value;
  }
};



exports.clearTempUploadFiles = function (req) {
  clearTempUploadFiles(req);
};

exports.cancelElectronicInfoRequest = async function (req, res) {
  try {
    let review = await FileReviewModel.findById(req.params.reviewId);
    if (!review){
      return res.status(404).json({status: 404, error: "File Review not found"});
    }

    let relation = await NaturalPersonModel.findById(req.params.relationId);

    if (!relation){
      return res.status(404).json({status: 404, error: "Relation not found"});
    }


    relation.electronicIdInfo.isElectronicId = false;
    relation.electronicIdInfo.status = "CANCELLED";
    relation.electronicIdInfo.allowNewRequest = true;
    relation.electronicIdInfo.electronicIdInvitationSent = false;



    if (relation.electronicIdInfo.uuid){
      const cancelResponse = await idPalController.cancelUuid(relation.electronicIdInfo.uuid);
      if (cancelResponse && cancelResponse.status === 200){

        const index = review.pendingElectronicIds.findIndex((e) =>
          e.uuid === relation.electronicIdInfo.uuid);

        if (index > -1) {
          review.pendingElectronicIds.splice(index, 1);
        }
        relation.electronicIdInfo.uuid = "";
      }
      else{
        return res.status(400).json({
          status: 400,
          error: "Error canceling the request to Idpal"
        })
      }
    }
    else {
      const index = review.pendingElectronicIds.findIndex((e) =>
        e.relationId && e.relationId.toString() === relation._id.toString());

      if (index > -1) {
        review.pendingElectronicIds.splice(index, 1);
      }
    }


    await relation.save();
    review.markModified('pendingElectronicIds');
    await review.save();

    return res.status(200).json({
      status: 200,
      message: "Invitations send successfully"
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({status:500, error: "Internal server error"});
  }
};

function setRelationReferences(reviewGroups, relationGroups, type, review, relationId, files,  clientReview ){
  try {
    let reviewRelationInfo;
    reviewGroups.forEach((group) => {
      reviewRelationInfo = type === 'natural' ? new naturalReview({}) : new organizationReview({});
      files.forEach((file) => {
        let typeValidation = new fileTypeValidation({referenceFile: file.id, validated: false});
        reviewRelationInfo.files.push(typeValidation);
      });
      reviewRelationInfo.referenceId = relationId;
      if (type === 'natural'){
        review[relationGroups[group]].natural.push(reviewRelationInfo);
        clientReview.naturalRelations.push({referenceId: relationId, group: group});
      }
      else{
        review[relationGroups[group]].organization.push(reviewRelationInfo);
        clientReview.organizationRelations.push({referenceId: relationId, group: group});
      }
    });
  } catch (e) {
    console.log(e);
  }

}
