const EntryModel = require("../models/entry").EntryModel;
const ArchivedEntryModel = require("../models/entry").ArchivedEntryModel;
const CompanyModel = require("../models/company").schema;
const MessageModel = require("../models/message");
const pdfController = require("../controllers/pdfController");
const uploadController = require('../controllers/uploadController');
const mailFormatController = require("../controllers/mailFormatController");
const mailController = require("../controllers/mailController");
const utils = require('../utils/utils');
const async = require("async");
const xlsx = require("xlsx");
const moment = require("moment");
const { getContainerClient } = require('../utils/azureStorage');
const archiver = require("archiver");
const ObjectId = require('mongoose').Types.ObjectId;
const httpConstants = require('http2').constants;
const {v4: uuidv4} = require('uuid');
const { STANDARD_DATE_FORMAT, STANDARD_DATETIME_FORMAT, SE_DEFAULT_SORTING_ITEMS } = require('../utils/constants');


exports.getSearch = async function (req, res, next) {
  try{
    let submissionfilter = {};
    let companyfilter = {};

    if (req.body.filter_masterclient && req.body.filter_masterclient.length > 2) {
      submissionfilter["company_data.masterclientcode"] = {
        $regex: req.body.filter_masterclient,
        $options: "i",
      };
      companyfilter["masterclientcode"] = {
        $regex: req.body.filter_masterclient,
        $options: "i",
      };
    }

    if (req.body.filter_company && req.body.filter_company.length > 2) {
      companyfilter["name"] = { $regex: req.body.filter_company, $options: "i" };
      submissionfilter["company_data.name"] = {
        $regex: req.body.filter_company,
        $options: "i",
      };
    }

    if (req.body.filter_referral && req.body.filter_referral.length > 2) {
      companyfilter["referral_office"] = {
        $regex: req.body.filter_referral,
        $options: "i",
      };
      submissionfilter["company_data.referral_office"] = {
        $regex: req.body.filter_referral,
        $options: "i",
      };
    }

    if (req.body.resident_outside_bvi && req.body.resident_outside_bvi == "Yes") {
      req.body.relevantActivities = {};
      submissionfilter["tax_residency.resident_in_BVI"] = false;
    }

    if (req.body.relevant_activities && req.body.relevant_activities.length) {
      req.body.relevantActivities = {};
      if (Array.isArray(req.body.relevant_activities)) {
        for (let relevantActivity of req.body.relevant_activities) {
          submissionfilter[
            `relevant_activities.${relevantActivity}.selected`
          ] = true;
          req.body.relevantActivities[relevantActivity] = true;
        }
      } else {
        submissionfilter[
          `relevant_activities.${req.body.relevant_activities}.selected`
        ] = true;
        req.body.relevantActivities[req.body.relevant_activities] = true;
      }
    }

    if (req.body.filter_submitted_range_start) {
      submissionfilter["submitted_at"] = {
        $gte: req.body.filter_submitted_range_start,
        $lte: req.body.filter_submitted_range_end ? req.body.filter_submitted_range_end : new Date(),
      };
    } else if (req.body.filter_submitted_range_end) {
      submissionfilter["submitted_at"] = { $lte: req.body.filter_submitted_range_end };
    }

    if (req.body.filter_incorporated_range_start) {
      companyfilter["incorporationdate"] = {
        $gte: req.body.filter_incorporated_range_start,
      };
      submissionfilter["company_data.incorporationdate"] = {
        $gte: req.body.filter_incorporated_range_start,
      };
    }
    if (req.body.filter_incorporated_range_end) {
      companyfilter["incorporationdate"] = {
        ...companyfilter["incorporationdate"],
        $lte: req.body.filter_incorporated_range_end
      };
      submissionfilter["company_data.incorporationdate"] = {
        ...submissionfilter["company_data.incorporationdate"],
        $lte: req.body.filter_incorporated_range_end
      };
    }

    if (req.body.filter_financial_period_range_start) {
      submissionfilter["entity_details.financial_period_ends"] = {
        $gte: req.body.filter_financial_period_range_start,
      };
    }
    if (req.body.filter_financial_period_range_end) {
      submissionfilter["entity_details.financial_period_ends"] = {
        ...submissionfilter["entity_details.financial_period_ends"],
        $lte: req.body.filter_financial_period_range_end
      };
    }

    if (
      !companyfilter["name"] &&
      !companyfilter["masterclientcode"] &&
      !companyfilter["referral_office"] &&
      !companyfilter["incorporationdate"]
    ) {
      //dummy filter to exclude all companies without submissions
      companyfilter["code"] = "-1";
    }

    if (
      submissionfilter["submitted_at"] ||
      (req.body.relevant_activities && req.body.relevant_activities.length)
    ) {
      companyfilter["code"] = "-1";
    }
    let limit = 100;
    let isFiltering = false;
    if (Object.keys(submissionfilter).length) {
      limit = process.env.MAX_QUERY_SIZE ? Number(process.env.MAX_QUERY_SIZE) : 5000 ;
      isFiltering = true;
    }

    if (req.body.filter_not_started === 'true') {
      CompanyModel.aggregate([
        { $match: companyfilter },
        {
          $lookup: {
            from: "archivedentries",
            localField: "code",
            foreignField: "company",
            as: "archivedentries",
          },
        },
        {
          $lookup: {
            from: "entries",
            localField: "code",
            foreignField: "company",
            as: "entries",
          },
        },
        {
          $project: {
            name: 1,
            code: 1,
            masterclientcode: 1,
            incorporationcode: 1,
            referral_office: 1,
            count: { $add: [{ $size: "$entries" }, { $size: "$archivedentries" }] },
          },
        },
        { $match: { count: 0 } },
      ]).exec()
        .then(function (companies) {
          let search_result = [];
          for (let idx = 0; idx < companies.length; idx++) {
            let singleResult = {
              entity_name: companies[idx].name,
              code: companies[idx].code,
              masterclientcode: companies[idx].masterclientcode,
              incorporationcode: companies[idx].incorporationcode,
              incorporationdate: companies[idx].incorporationdate,
              status: "NOT STARTED",
              referral_office: companies[idx].referral_office,
            };
            search_result.push(singleResult);
          }
          res.render("substance/search", {
            user: req.session.user,
            title: "Substance Search",
            result: search_result,
            filters: req.body,
            authentication: req.session.authentication
          });
        });
    } else {
      let entries = await EntryModel.find(submissionfilter, {
        _id: 1,
        created_by: 1,
        company_data: 1,
        company: 1,
        status: 1,
        createdAt: 1,
        submitted_at: 1,
        entity_details: 1,
        relevant_activities: 1,
        payment: 1,
        requested_information: 1,
        client_returned_information: 1,
        financial_period_changes: 1,
        banking_business: 1,
        insurance_business: 1,
        fund_management_business: 1,
        finance_leasing_business: 1,
        headquarters_business: 1,
        shipping_business: 1,
        holding_business: 1,
        intellectual_property_business: 1,
        service_centre_business: 1,
        supporting_details: 1,
        reopened: 1,
      }).sort({ createdAt: -1 }).limit(limit).exec();

      const archivedEntries = await ArchivedEntryModel.find(submissionfilter, {
        _id: 1,
        created_by: 1,
        company_data: 1,
        company: 1,
        status: 1,
        createdAt: 1,
        submitted_at: 1,
        entity_details: 1,
        relevant_activities: 1,
        payment: 1,
        requested_information: 1,
        client_returned_information: 1,
        financial_period_changes: 1,
        banking_business: 1,
        insurance_business: 1,
        fund_management_business: 1,
        finance_leasing_business: 1,
        headquarters_business: 1,
        shipping_business: 1,
        holding_business: 1,
        intellectual_property_business: 1,
        service_centre_business: 1,
        supporting_details: 1,
        reopened: 1,
      }).sort({ createdAt: -1 }).limit(limit).exec();

      entries = [
        ...entries,
        ...archivedEntries
      ];

      CompanyModel.aggregate([
        { $match: companyfilter },
        {
          $lookup: {
            from: "entries",
            localField: "code",
            foreignField: "company",
            as: "entries",
          },
        },
        {
          $lookup: {
            from: "archivedentries",
            localField: "code",
            foreignField: "company",
            as: "archivedentries",
          },
        },
        {
          $project: {
            name: 1,
            code: 1,
            masterclientcode: 1,
            incorporationcode: 1,
            referral_office: 1,
            requested_information: 1,
            client_returned_information: 1,
            financial_period_changes: 1,
            reopened: 1,
            banking_business: 1,
            insurance_business: 1,
            fund_management_business: 1,
            finance_leasing_business: 1,
            headquarters_business: 1,
            shipping_business: 1,
            holding_business: 1,
            intellectual_property_business: 1,
            service_centre_business: 1,
            supporting_details: 1,
            count: { $add: [{ $size: "$entries" }, { $size: "$archivedentries" }] },
          },
        },
        { $match: { count: 0 } },
      ])
        .exec()
        .then(function (companies) {
          let search_result = [];
          for (let idx = 0; idx < entries.length; idx++) {
            const requestedInformationList = entries[idx].requested_information?.details || [];
            const returnedInformationList = entries[idx].client_returned_information?.details || [];

            const lastReopen = entries[idx].reopened && entries[idx].reopened.details.length
              ? entries[idx].reopened.details[entries[idx].reopened.details.length - 1] : null;
            const lastRFI = requestedInformationList && requestedInformationList.length
              ? requestedInformationList[requestedInformationList.length - 1] : null;
            const lastRFIResponse = returnedInformationList && returnedInformationList.length
              ? returnedInformationList[returnedInformationList.length - 1] : null;


            const supportAttachments =  entries[idx].supporting_details?.support_attachments?.length > 0 ? true : false;
            let relevantActivityAttachments =  false;

            // Relevant activities
            let objectEntry = Object.entries(entries[idx].toObject());
            for (const [entryKey, entryValue] of objectEntry) {
              if (
                entryKey === "banking_business" ||
                entryKey === "insurance_business" ||
                entryKey === "fund_management_business" ||
                entryKey === "finance_leasing_business" ||
                entryKey === "headquarters_business" ||
                entryKey === "shipping_business" ||
                entryKey === "holding_business" ||
                entryKey === "intellectual_property_business" ||
                entryKey === "service_centre_business"
              ) {
                  if (entryValue.support_documents && entryValue.support_documents.length > 0) {
                    relevantActivityAttachments = true;
                  }
                }
            }

            let singleResult = {
              _id: entries[idx]._id,
              company: entries[idx].company,
              email: entries[idx].created_by,
              entity_name: entries[idx].company_data.name,
              code: entries[idx].company_data.code,
              masterclientcode: entries[idx].company_data.masterclientcode,
              incorporationcode: entries[idx].company_data.incorporationcode,
              incorporationdate: entries[idx].company_data.incorporationdate,
              status: entries[idx].status,
              createdAt: entries[idx].createdAt,
              submitted_at: entries[idx].submitted_at,
              reopened_at: lastReopen?.date_reopened || null,
              resubmitted_at: lastReopen && lastReopen.resubmitted_at ? lastReopen.resubmitted_at : null,
              rfi_at: lastRFI && lastRFI.status !== 'CANCELLED' && lastRFI.requested_at ? lastRFI.requested_at : null,
              rfi_completed_at: lastRFI && lastRFI.status !== 'CANCELLED' && lastRFIResponse && lastRFIResponse.request_id === lastRFI.id ? lastRFIResponse.returned_at : null,
              financial_period_begins: entries[idx].entity_details
                ? entries[idx].entity_details.financial_period_begins
                : null,
              financial_period_ends: entries[idx].entity_details
                ? entries[idx].entity_details.financial_period_ends
                : null,
              referral_office: entries[idx].company_data.referral_office,
              relevant_activities: entries[idx].relevant_activities,
              show_attachments_download: supportAttachments || relevantActivityAttachments,
              date_paid: entries[idx].payment
                ? entries[idx].payment.payment_received_at
                : null,
              payment_reference: entries[idx].payment
                ? entries[idx].payment.batchpayment_code
                : null,
              show_info_details: (entries[idx].reopened?.details?.length > 0 || 
                entries[idx].requested_information?.details?.length > 0 || 
                entries[idx].financial_period_changes?.details?.length > 0)
            };
            search_result.push(singleResult);
          }


          for (let idx = 0; idx < companies.length; idx++) {
            let singleResult = {
              entity_name: companies[idx].name,
              code: companies[idx].code,
              masterclientcode: companies[idx].masterclientcode,
              incorporationcode: companies[idx].incorporationcode,
              incorporationdate: companies[idx].incorporationdate,
              status: "NOT STARTED",
              referral_office: companies[idx].referral_office,
            };
            search_result.push(singleResult);
          }

          if (search_result.length > 0){
            search_result.sort((a, b) => b.submitted_at - a.submitted_at);
          }
          

          res.render("substance/search", {
            user: req.session.user,
            title: "Substance Search",
            result: search_result,
            filters: req.body,
            authentication: req.session.authentication,
            showLimitAlert: entries.length >= limit && isFiltering,
            STANDARD_DATE_FORMAT
          });
        });
    }
  }catch(e){
    console.log(e);
    next(e);
  }

};

exports.getPaginatedSubstanceSearch = async function (req, res, next) {
  try {
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;
    const skip = (page - 1) * pageSize;

    const sortByItem = SE_DEFAULT_SORTING_ITEMS.find((item) => item.code === req.query.sortBy) ;
    const sortType = parseInt(req.query.sortType) || -1;
    let sort = {};

    if (sortByItem){
      sort[sortByItem.dbField] = sortType
    }else{
      sort["createdAt"]= sortType
    }

    const { dbQuery, viewFilters} = parseSubstanceSearchQuery(req.query);

    const entriesProjection = {
      _id: 1,
      created_by: 1,
      company_data: 1,
      company: 1,
      status: 1,
      createdAt: 1,
      submitted_at: 1,
      entity_details: 1,
      relevant_activities: 1,
      payment: 1,
      requested_information: 1,
      client_returned_information: 1,
      financial_period_changes: 1,
      banking_business: 1,
      insurance_business: 1,
      fund_management_business: 1,
      finance_leasing_business: 1,
      headquarters_business: 1,
      shipping_business: 1,
      holding_business: 1,
      intellectual_property_business: 1,
      service_centre_business: 1,
      supporting_details: 1,
      reopened: 1,
    };

    // Calculate total Documents 
    const totalDocuments = await EntryModel.countDocuments(dbQuery)
    // Calculate the total of pages
    const totalPages = Math.ceil(totalDocuments / pageSize);
    // GET total of paginated items
    const entriesResult = await EntryModel.aggregate([
      { $match: dbQuery }, 
      { $sort: sort }, 
      { $skip: skip }, 
      { $limit: pageSize },
      { $project: entriesProjection }, 
    ]);

    const dbResults = entriesResult.map((entry) => {
      const requestedInformationList = entry.requested_information?.details || [];
      const returnedInformationList = entry.client_returned_information?.details || [];

      const lastReopen = entry.reopened && entry.reopened.details?.length
        ? entry.reopened.details[entry.reopened.details.length - 1] : null;
      const lastRFI = requestedInformationList && requestedInformationList.length
        ? requestedInformationList[requestedInformationList.length - 1] : null;
      const lastRFIResponse = returnedInformationList && returnedInformationList.length
        ? returnedInformationList[returnedInformationList.length - 1] : null;

      const supportAttachments = entry.supporting_details?.support_attachments?.length > 0 ? true : false;
      let relevantActivityAttachments =  false;

      // Relevant activities
      let objectEntry = Object.entries(entry);
      for (const [entryKey, entryValue] of objectEntry) {
        if (
          entryKey === "banking_business" ||
          entryKey === "insurance_business" ||
          entryKey === "fund_management_business" ||
          entryKey === "finance_leasing_business" ||
          entryKey === "headquarters_business" ||
          entryKey === "shipping_business" ||
          entryKey === "holding_business" ||
          entryKey === "intellectual_property_business" ||
          entryKey === "service_centre_business"
        ) {
            if (entryValue.support_documents && entryValue.support_documents.length > 0) {
              relevantActivityAttachments = true;
            }
          }
      }

      return {
        _id: entry._id,
        company: entry.company,
        email: entry.created_by,
        entity_name: entry.company_data.name,
        code: entry.company_data.code,
        masterclientcode: entry.company_data.masterclientcode,
        incorporationcode: entry.company_data.incorporationcode,
        incorporationdate: entry.company_data.incorporationdate,
        status: entry.status,
        createdAt: entry.createdAt,
        submitted_at: entry.submitted_at,
        reopened_at: lastReopen?.date_reopened || null,
        resubmitted_at: lastReopen && lastReopen.resubmitted_at ? lastReopen.resubmitted_at : null,
        rfi_at: lastRFI && lastRFI.status !== 'CANCELLED' && lastRFI.requested_at ? lastRFI.requested_at : null,
        rfi_completed_at: lastRFI && lastRFI.status !== 'CANCELLED' && lastRFIResponse && lastRFIResponse.request_id === lastRFI.id ? lastRFIResponse.returned_at : null,
        financial_period_begins: entry.entity_details ? entry.entity_details.financial_period_begins : null,
        financial_period_ends: entry.entity_details ? entry.entity_details.financial_period_ends : null,
        referral_office: entry.company_data.referral_office,
        relevant_activities: entry.relevant_activities,
        show_attachments_download: supportAttachments || relevantActivityAttachments,
        date_paid: entry.payment ? entry.payment.payment_received_at : null,
        payment_reference: entry.payment ? entry.payment.batchpayment_code : null,
        show_info_details: (entry.reopened?.details?.length > 0 || entry.requested_information?.details?.length > 0 ||
          entry.financial_period_changes?.details?.length > 0),
        canResetToSaved: entry.status === "SUBMITTED" || entry.status === "PAID"
      };
    })

    const paginatedResponse = {
      "pageNumber": page,
      "pageCount": totalPages,
      "pageSize": pageSize,
      "totalItemCount": totalDocuments,
      "hasPrevious": page > 1,
      "hasNext": page < totalPages,
      "sorting": {
        sortBy: req.query.sortBy,
        type: sortType,
        options: SE_DEFAULT_SORTING_ITEMS
      },
      "data": dbResults
    }
  
    res.render("substance/search-paginated", {
      user: req.session.user,
      title: "Substance Search V2 Beta",
      result: paginatedResponse,
      filters: viewFilters,
      authentication: req.session.authentication,
      STANDARD_DATE_FORMAT
    });
  } catch (e) {
    console.log(e);
    next(e);
  }

};

exports.getPaginatedExport = async function (req, res, next) {
  try{
    let submissionfilter = {};

    const sortByItem = SE_DEFAULT_SORTING_ITEMS.find((item) => item.code === req.query.sortBy) ;
    const sortType = parseInt(req.query.sortType) || -1;
    let sort = {};

    if (sortByItem){
      sort[sortByItem.dbField] = sortType
    }else{
      sort = { 'payment.payment_received_at': -1 }
    }
  
    if (req.query.filter_masterclient && req.query.filter_masterclient.length > 2) {
      submissionfilter["company_data.masterclientcode"] = {
        $regex: req.query.filter_masterclient.replace(' ', '|'),
        $options: "i",
      };
    }
  
    if (req.query.filter_company && req.query.filter_company.length > 2) {
      submissionfilter["$or"] = [
        { "company_data.name": { $regex: req.query.filter_company.replace(' ', '|'), $options: "i" } },
        { "company_data.code": { $regex: req.query.filter_company.replace(' ', '|'), $options: "i" } },
      ];
    }
  
    if (req.query.filter_referral && req.query.filter_referral.length > 2) {
      submissionfilter["company_data.referral_office"] = {
        $regex: req.query.filter_referral,
        $options: "i",
      };
    }
    
    if (req.query.relevantActivities && req.query.relevantActivities.length) {
      if (req.query.relevantActivities !== ""){
        if (Array.isArray(req.query.relevantActivities)) {
          for (let relevantActivity of req.query.relevantActivities) {
            submissionfilter[`relevant_activities.${relevantActivity}.selected`] = true;
          }
        } else {
          submissionfilter[`relevant_activities.${req.query.relevantActivities}.selected`] = true;
        }
      }
    }
  
    if (req.query.resident_outside_bvi && req.query.resident_outside_bvi == "Yes") {
      req.query.relevantActivities = [];
      submissionfilter["tax_residency.resident_in_BVI"] = false;
    }

    if (req.query.submittedDateRange){
      // format range date is "YYYY-MM-DD - YYYY-MM-DD"
      const submittedDateRange = req.query.submittedDateRange.split("-").map((date) => date.trim()); 
      let startDate;
      let endDate;
      if (submittedDateRange.length > 1){
        startDate = moment(submittedDateRange[0], "MM/DD/YYYY").toDate();
        endDate = moment(submittedDateRange[1], "MM/DD/YYYY").toDate(); 
        req.query.submittedDateRange = { start: submittedDateRange[0], end: submittedDateRange[1] };
      }else{
        startDate = moment(submittedDateRange[0], "MM/DD/YYYY").toDate();
        endDate = moment(submittedDateRange[0], "MM/DD/YYYY").add(1, 'days').toDate();
        req.query.submittedDateRange = { start: submittedDateRange[0], end: submittedDateRange[0] };
      }
      submissionfilter["submitted_at"] = { $gte: startDate, $lte: endDate }
      
    }
  
    if (req.query.companyIncorporatedRange) {
      // format range date is "YYYY-MM-DD - YYYY-MM-DD"
      const companyIncorporatedRange = req.query.companyIncorporatedRange.split("-").map((date) => date.trim());
      let startDate;
      let endDate;
      console.log(companyIncorporatedRange)
      if (companyIncorporatedRange.length > 1){
        startDate = moment(companyIncorporatedRange[0], "MM/DD/YYYY").toDate();
        endDate = moment(companyIncorporatedRange[1], "MM/DD/YYYY").toDate();
        req.query.companyIncorporatedRange = { start: companyIncorporatedRange[0], end: companyIncorporatedRange[1] };
      }else{
        startDate = moment(companyIncorporatedRange[0], "MM/DD/YYYY").toDate();
        endDate = moment(companyIncorporatedRange[0], "MM/DD/YYYY").add(1, 'days').toDate();
        req.query.companyIncorporatedRange = { start: companyIncorporatedRange[0], end: companyIncorporatedRange[0] };
      }
      submissionfilter["company_data.incorporationdate"] = { $gte: startDate, $lte: endDate }
      
    }
  
    if (req.query.financialPeriodDateRange) {
      // format range date is "YYYY-MM-DD - YYYY-MM-DD"
      const financialPeriodDateRange = req.query.financialPeriodDateRange.split("-").map((date) => date.trim());
      let startDate;
      let endDate;
      if (financialPeriodDateRange.length > 1){
        startDate = moment(financialPeriodDateRange[0], "MM/DD/YYYY").toDate();
        endDate = moment(financialPeriodDateRange[1], "MM/DD/YYYY").toDate();
        req.query.financialPeriodDateRange = { start: financialPeriodDateRange[0], end: financialPeriodDateRange[1] };
      }else{
        startDate = moment(financialPeriodDateRange[0], "MM/DD/YYYY").toDate();
        endDate = moment(financialPeriodDateRange[0], "MM/DD/YYYY").add(1, 'days').toDate();
        req.query.financialPeriodDateRange = { start: financialPeriodDateRange[0], end: financialPeriodDateRange[0] };
      }
      submissionfilter["entity_details.financial_period_ends"] = { $gte: startDate, $lte: endDate }
  
    }

    if (req.query.filter_exported === "exported"){
      submissionfilter["exported_at"] = {
        "$exists": true
      }
    }
  
    if (req.query.filter_exported === "not-exported"){
      submissionfilter["exported_at"] = {
        "$exists": false
      }
    }

    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;
    const skip = (page - 1) * pageSize;
    const totalDocuments = await EntryModel.countDocuments({ status: "PAID", ...submissionfilter })
    const totalPages = Math.ceil(totalDocuments / pageSize);
  
   EntryModel.find({ status: "PAID", ...submissionfilter },
     {
      _id:1, 
      company_data:1, 
      status:1,
      entity_details:1,
      submitted_at:1, 
      exported_at:1,
      payment:1,
      reopened:1
    }
     )
      .skip(skip)
      .limit(pageSize)
      .sort(sort)
      .exec(async function (err, dbEntries) {
        if (err) return next(err);
  
        dbEntries.forEach((entry)=> {
          if (entry.reopened?.details.length > 0){
            entry.resubmitted_at = entry.reopened.details[entry.reopened.details.length - 1].resubmitted_at || entry.submitted_at;
          }
        });

        const paginatedResponse = {
          "pageNumber": page,
          "pageCount": totalPages,
          "pageSize": pageSize,
          "totalItemCount": totalDocuments,
          "hasPrevious": page > 1,
          "hasNext": page < totalPages,
          "sorting": {
            sortBy: req.query.sortBy,
            type: sortType,
            options: SE_DEFAULT_SORTING_ITEMS
          },
          "data": dbEntries
        }
        res.render("substance/export-paginated", {
          user: req.session.user,
          title: "Export Submissions",
          result: paginatedResponse,
          filters: req.query,
          authentication: req.session.authentication,
          STANDARD_DATE_FORMAT
        });
      });
    }catch(e){
      console.log(e);
      next(e);
    }
}

exports.downloadPdf = async function (req, res, next) {
  try {
    const entry = await EntryModel.findById(req.params.entryId);
    if (entry) {
      pdfController.generatePdf(entry, res);
    } else {
      const archivedEntry = await ArchivedEntryModel.findById(req.params.entryId);
      if (archivedEntry) {
        pdfController.generatePdf(archivedEntry, res);
      } else {
        throw new Error('Entry not found');
      }
    }
  } catch (err) {
    return next(err);
  }
}

exports.downloadSupportAttachments = async function (req, res, next) {
  try {
    const entryId = req.params.entryId;

    const containerClient = getContainerClient(
      process.env.AZURE_STORAGE_CONTAINER_SUBSTANCE_UPLOADS,
      process.env.AZURE_STORAGE_ACCOUNT,
      process.env.AZURE_STORAGE_ACCESS_KEY
    );

    const archive = archiver("zip", {
      zlib: { level: 9 },
    });

    EntryModel.findById(entryId, async function (err, entry) {
      if (!err) {
        // Supporting details
        if (entry.supporting_details && entry.supporting_details.support_attachments) {

          const value = entry.supporting_details.support_attachments;

          for (let evidence of value) {
            const blobClient = containerClient.getBlobClient(`${entryId}/${evidence.blobName}`);
            let stream = (await blobClient.download()).readableStreamBody;
            archive.append(stream, {
              name: `supporting_details/${evidence.originalname}`,
            });
          }
        }

        // Relevant activities
        let objectEntry = Object.entries(entry.toObject());
        for (const [entryKey, entryValue] of objectEntry) {
          if (
            entryKey === "banking_business" ||
            entryKey === "insurance_business" ||
            entryKey === "fund_management_business" ||
            entryKey === "finance_leasing_business" ||
            entryKey === "headquarters_business" ||
            entryKey === "shipping_business" ||
            entryKey === "holding_business" ||
            entryKey === "intellectual_property_business" ||
            entryKey === "service_centre_business"
          ) {
            if (entryValue.support_documents) {
              for (let evidence of entryValue.support_documents) {
                const blobClient = containerClient.getBlobClient(`${entryId}/${evidence.blobName}`);
                let stream = (await blobClient.download()).readableStreamBody;
                archive.append(stream, {
                  name: `relevant_activities/${entryKey}/${evidence.originalname}`,
                });
              }
            }
          }
        }

        archive.on("error", function (err) {
          throw err;
        });

        archive.pipe(res);
        archive.finalize();
        
      }
    });

  }catch(e){
    console.log(e);
    next(e);
  }

};

exports.getDashboard = async function (req, res) {
  try {
    console.log(new Date().toString() + "data loading");

    const containerClient = getContainerClient(
      process.env.AZURE_STORAGE_CONTAINER_SUBSTANCE_STATISTICS,
      process.env.AZURE_STORAGE_ACCOUNT,
      process.env.AZURE_STORAGE_ACCESS_KEY
    );

    const blobClient = containerClient.getBlobClient("statistics.json");
    const response = await blobClient.downloadToBuffer();
    const text = response.toString();
    res.render("substance/index", {
      user: req.session.user,
      title: "Substance Dashboard",
      allData: JSON.parse(text),
      authentication: req.session.authentication,
      STANDARD_DATETIME_FORMAT
    });
  } catch (error) {
    console.log(error);
    res.render('error', { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
  }
};

const getPendingPayments = function (req, res, next) {
  try{
    // get all submissions with status SUBMITTED and payment received at is empty
    let submissionfilter = {};

    if (req.body.filter_masterclient && req.body.filter_masterclient.length > 2) {
      submissionfilter["company_data.masterclientcode"] = {
        $regex: req.body.filter_masterclient.replace(' ', '|'),
        $options: "i",
      };
    }

    if (req.body.filter_company && req.body.filter_company.length > 2) {
      submissionfilter["$or"] = [
        { "company_data.name": { $regex: req.body.filter_company.replace(' ', '|'), $options: "i" } },
        { "company_data.code": { $regex: req.body.filter_company.replace(' ', '|'), $options: "i" } },
      ];
    }

    if (req.body.filter_referral && req.body.filter_referral.length > 2) {
      submissionfilter["company_data.referral_office"] = {
        $regex: req.body.filter_referral,
        $options: "i",
      };
    }

    if (req.body.relevant_activities && req.body.relevant_activities.length) {
      req.body.relevantActivities = {};
      if (Array.isArray(req.body.relevant_activities)) {
        for (let relevantActivity of req.body.relevant_activities) {
          submissionfilter[
            `relevant_activities.${relevantActivity}.selected`
          ] = true;
          req.body.relevantActivities[relevantActivity] = true;
        }
      } else {
        submissionfilter[
          `relevant_activities.${req.body.relevant_activities}.selected`
        ] = true;
        req.body.relevantActivities[req.body.relevant_activities] = true;
      }
    }

    if (req.body.filter_range_start) {
      submissionfilter["submitted_at"] = {
        $gte: req.body.filter_range_start,
        $lte: req.body.filter_range_end ? req.body.filter_range_end : new Date(),
      };
    } else if (req.body.filter_range_end) {
      submissionfilter["submitted_at"] = { $lte: req.body.filter_range_end };
    }


    let limit = 100;
    let isFiltering = false;
    if (Object.keys(submissionfilter).length) {
      limit = process.env.MAX_QUERY_SIZE ? Number(process.env.MAX_QUERY_SIZE) : 5000 ;
      isFiltering = true;
    }
    EntryModel.find({ status: "SUBMITTED", ...submissionfilter }).sort({ submitted_at: -1 })
      .limit(limit)
      .exec(async function (err, dbEntries) {
        if (err) return next(err);

        res.render("substance/pending-payments", { 
          title: "Pending Payments", 
          data: dbEntries, 
          filters: req.body, 
          authentication: req.session.authentication,
          showLimitAlert: dbEntries.length >= limit && isFiltering,
          STANDARD_DATE_FORMAT });
      });
  }catch(e){
    console.log(e);
    next(e);
  }

};

exports.getPendingPayments = getPendingPayments;

exports.getExportOverview = function (req, res, next) {
  try{
  let submissionfilter = {};

  if (req.body.filter_masterclient && req.body.filter_masterclient.length > 2) {
    submissionfilter["company_data.masterclientcode"] = {
      $regex: req.body.filter_masterclient.replace(' ', '|'),
      $options: "i",
    };
  }

  if (req.body.filter_company && req.body.filter_company.length > 2) {
    submissionfilter["$or"] = [
      { "company_data.name": { $regex: req.body.filter_company.replace(' ', '|'), $options: "i" } },
      { "company_data.code": { $regex: req.body.filter_company.replace(' ', '|'), $options: "i" } },
    ];
  }

  if (req.body.filter_referral && req.body.filter_referral.length > 2) {
    submissionfilter["company_data.referral_office"] = {
      $regex: req.body.filter_referral,
      $options: "i",
    };
  }

  if (req.body.relevant_activities && req.body.relevant_activities.length) {
    req.body.relevantActivities = {};
    if (Array.isArray(req.body.relevant_activities)) {
      for (let relevantActivity of req.body.relevant_activities) {
        submissionfilter[
          `relevant_activities.${relevantActivity}.selected`
        ] = true;
        req.body.relevantActivities[relevantActivity] = true;
      }
    } else {
      submissionfilter[
        `relevant_activities.${req.body.relevant_activities}.selected`
      ] = true;
      req.body.relevantActivities[req.body.relevant_activities] = true;
    }
  }

  if (req.body.resident_outside_bvi && req.body.resident_outside_bvi == "Yes") {
    req.body.relevantActivities = {};
    submissionfilter["tax_residency.resident_in_BVI"] = false;
  }


  if (req.body.filter_submitted_range_start) {
    submissionfilter["submitted_at"] = {
      $gte: req.body.filter_submitted_range_start,
      $lte: req.body.filter_submitted_range_end ? req.body.filter_submitted_range_end : new Date(),
    };
  } else if (req.body.filter_submitted_range_end) {
    submissionfilter["submitted_at"] = { $lte: req.body.filter_submitted_range_end };
  }

    if (req.body.filter_incorporated_range_start) {
    submissionfilter["company_data.incorporationdate"] = {
      $gte: req.body.filter_incorporated_range_start,
    };
  }
    if (req.body.filter_incorporated_range_end) {
    submissionfilter["company_data.incorporationdate"] = {
      ...submissionfilter["company_data.incorporationdate"],
      $lte: req.body.filter_incorporated_range_end
    };
  }

    if (req.body.filter_financial_period_range_start) {
    submissionfilter["entity_details.financial_period_ends"] = {
      $gte: req.body.filter_financial_period_range_start,
    };
  }
    if (req.body.filter_financial_period_range_end) {
    submissionfilter["entity_details.financial_period_ends"] = {
      ...submissionfilter["entity_details.financial_period_ends"],
      $lte: req.body.filter_financial_period_range_end
    };
  }

  if (req.body.filter_exported === "exported"){
    submissionfilter["exported_at"] = {
      "$exists": true
    }
  }

  if (req.body.filter_exported === "not-exported"){
    submissionfilter["exported_at"] = {
      "$exists": false
    }
  }


  let limit = 100;
  let isFiltering = false;
  if (Object.keys(submissionfilter).length) {
    limit = process.env.MAX_QUERY_SIZE ? Number(process.env.MAX_QUERY_SIZE) : 5000 ;
    isFiltering = true;
  }
  EntryModel.find({ status: "PAID", ...submissionfilter },
    {
      _id:1, 
      company_data:1, 
      status:1,
      entity_details:1,
      submitted_at:1, 
      exported_at:1,
      payment:1,
      reopened:1,
      tax_residency: 1,
      relevant_activities: 1,
      supporting_details: 1
    })
    .sort({ 'payment.payment_received_at': -1 })
    .limit(limit)
    .exec(async function (err, dbEntries) {
      if (err) return next(err);

      dbEntries.forEach((entry)=> {
        if (entry.reopened?.details.length > 0){
          entry.resubmitted_at = entry.reopened.details[entry.reopened.details.length - 1].resubmitted_at || entry.submitted_at;
        }

        const hasTaxResidencyEvidence =  entry.tax_residency?.evidence_provisional_treatment_non_residency?.length > 0 || 
        entry.tax_residency?.evidence_non_residency?.length > 0;

        const supportingDocuments =  entry.supporting_details?.support_attachments?.length > 0;
        let relevantActivityAttachments =  false;

        // Relevant activities
        let objectEntry = Object.entries(entry.toObject());
        for (const [entryKey, entryValue] of objectEntry) {
          if (
            entryKey === "banking_business" ||
            entryKey === "insurance_business" ||
            entryKey === "fund_management_business" ||
            entryKey === "finance_leasing_business" ||
            entryKey === "headquarters_business" ||
            entryKey === "shipping_business" ||
            entryKey === "holding_business" ||
            entryKey === "intellectual_property_business" ||
            entryKey === "service_centre_business"
          ) {
            relevantActivityAttachments = entryValue?.evidence_equipment?.length ||
              entryValue?.outsourcing_evidence?.length ||
              entryValue?.support_documents?.length ||
              entryValue?.tangible_assets_explanation_files?.length ||
              entryValue?.intangible_assets_decisions_files?.length ||
              entryValue?.intangible_assets_nature_files?.length ||
              entryValue?.intangible_assets_trading_nature_files?.length ||
              entryValue?.other_ciga_business_files?.length ||
              entryValue?.other_ciga_decisions_files?.length ||
              entryValue?.other_ciga_evidence_files?.length ||
              entryValue?.other_ciga_files?.length ||
              entryValue?.high_risk_ip_evidence?.length;
            }
        }
        entry.show_attachments_download = hasTaxResidencyEvidence || supportingDocuments || relevantActivityAttachments;
      });
      res.render("substance/export-overview", {
        title: "Export Submissions",
        data: dbEntries,
        filters: req.body,
        authentication: req.session.authentication,
        showLimitAlert: dbEntries.length >= limit && isFiltering,
        STANDARD_DATE_FORMAT
      });
    });
  }catch(e){
    console.log(e);
    next(e);
  }

};

exports.getImport = function (req, res) {
  res.render("substance/import", { user: req.session.user, title: "Import data" });
};

exports.doExport = function (req, res, next) {
  const filename = "submissions.csv";
  let entryIds = req.body["entryIds"].split(";");
  let exportedSubmissions = [];
  async.each(
    entryIds,
    function (rec, callback) {
      EntryModel.findById(rec, function (err, entry) {
        console.log("update " + rec);
        if (err) {
          return next(err);
        }
        if (entry == null) {
          //|| (entry.status != 'SAVED')) { // No results.
          err = new Error("Entry not found");
          err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
          return next(err);
        }

        updateEntryStatus(entry).then((updated) => {
          console.log("push entry ", updated);
          exportedSubmissions.push(createExportDocument(entry));
          callback();
        });
      });
    },
    function (err) {
      if(err){
        console.log(err);
        return next(err);
      }
      res.statusCode = httpConstants.HTTP_STATUS_OK;
      res.setHeader("Content-Type", "text/csv");
      res.setHeader("Content-Disposition", "attachment; filename=" + filename);
      res.csv(exportedSubmissions, true);
    }
  );

  function createExportDocument(entry) {
    return {
      company_code: entry.company_data.code,
      created_by: entry.created_by,
      financial_period_begins: entry.entity_details
        ? entry.entity_details.financial_period_begins
        : undefined,
      financial_period_ends: entry.entity_details
        ? entry.entity_details.financial_period_ends
        : undefined,
    };
  }

  function updateEntryStatus(entry) {
    return EntryModel.update(entry, {
      $set: { exported_at: new Date(new Date().toUTCString()) },
    });
  }
};

exports.doExportJSON = function (req, res, next) {
  try{
    const filename = "submissions.json";
    let entryIds = req.body["entryIds"].split(";");
    let exportedSubmissions = [];

    async.each(
      entryIds,
      function (rec, callback) {
        EntryModel.findById(rec, function (err, entry) {
          if (err) {
            return next(err);
          }
          if (entry == null) {
            //|| (entry.status != 'SAVED')) { // No results.
            err = new Error("Entry not found");
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
          }

          exportedSubmissions.push(JSON.stringify(entry));
          callback();
        });
      },
      function (err) {
        if (err) {
          return next(err);
        }
        res.statusCode = httpConstants.HTTP_STATUS_OK;
        res.setHeader("Content-Type", "text/json");
        res.setHeader("Content-Disposition", "attachment; filename=" + filename);

        res.send(exportedSubmissions);
      }
    );
  }catch(e){
    console.log(e);
    next(e);
  }

};

exports.markAsPaid = function (req, res, next) {
  let entryIds = req.body["entries"];
  let emailEntries = [];
  async.each(
    entryIds,
    function (rec, callback) {
      EntryModel.findById(rec, function (err, entry) {
        if (err) {
          return next(err);
        }
        if (entry == null) {
          //|| (entry.status != 'SAVED')) { // No results.
          err = new Error("Entry not found");
          err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
          return next(err);
        }
        if (entry.status == "SUBMITTED") {
          updateEntryStatus(entry, "PAID").then(() => {
            callback();
          });

          if (entry.supporting_details?.support_attachments?.length > 0 ||
            entry.banking_business?.support_documents?.length > 0 ||
            entry.insurance_business?.support_documents?.length > 0 ||
            entry.fund_management_business?.support_documents?.length > 0 ||
            entry.finance_leasing_business?.support_documents?.length > 0 ||
            entry.headquarters_business?.support_documents?.length > 0 ||
            entry.shipping_business?.support_documents?.length > 0 || 
            entry.holding_business?.support_documents?.length > 0 || 
            entry.intellectual_property_business?.support_documents?.length > 0 ||
            entry.service_centre_business?.support_documents?.length > 0) {
              emailEntries.push({
                "code": entry.company, 
                "financialPeriodStart": entry.entity_details?.financial_period_begins ? 
                  moment(entry.entity_details.financial_period_begins).format("YYYY-MM-DD") : "",
                "financialPeriodEnd": entry.entity_details?.financial_period_ends ?
                  moment(entry.entity_details.financial_period_ends).format("YYYY-MM-DD") : "",
            })
          }

        } else {
          callback();
        }
      });
    },
    async function (err) {
      if (err) {
        return next(err);
      }
      if(emailEntries.length > 0){
        let email = mailFormatController.generateClientSubmittedAttachmentsEmail(emailEntries);

        const result = await mailController.asyncSend(
          process.env.SUPER_USER_EMAIL_GROUP,
          'Trident Trust Client Notification',
          email.textString,
          email.htmlString
        );

        console.log(result)
      }
      res.json({ success: true });
    }
  );

  function  updateEntryStatus(entry, status) {
    const currentDate = new Date(new Date().toUTCString());
    entry.status = status;
    entry.payment = {
      payment_type: "WIRE TRANSFER",
      payment_reference:
        entry.company_data.masterclientcode +
        "-" +
        entry.company_data.code +
        "-" +
        currentDate.getFullYear() +
        (currentDate.getMonth() + 1).toString() +
        currentDate.getDate().toString(),
      payment_received_at: currentDate,
      batchpayment_id: "0",
      batchpayment_transactionId: req.session.user.name,
      batchpayment_code: "0",
    };
    return EntryModel.findOneAndUpdate(
      { _id: entry._id, company: entry.company },
      entry,
      {}
    );
  }
};

exports.saveSubmission = async function (req, res) {
  try {
    let submission = await EntryModel.findOne({ _id: ObjectId(req.body.id), company: req.body.company });
    let newStartDate;
    let newEndDate;

    if(!req.body.reason || req.body.reason === ""){
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please provide the reason to re-open the submission"
      });
    }
    else if(req.body.reason && req.body.reason.length < 50){
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please provide more information."
      });
    }

    if(req.body.changePeriodDate === null || req.body.changePeriodDate === undefined ){
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please make a selection to correct the financial period"
      });
    }


    if (req.body.changePeriodDate === true ){
      if (!req.body.newFinancialStartDate || !req.body.newFinancialEndDate){
        return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
          status: httpConstants.HTTP_STATUS_BAD_REQUEST,
          error: "Please provide the correct financial start/end dates"
        });
      }
      newStartDate = moment(req.body.newFinancialStartDate).utc().toDate();
      newEndDate = moment(req.body.newFinancialEndDate).utc().toDate();

      if (newEndDate < newStartDate) {
        return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
          status: httpConstants.HTTP_STATUS_BAD_REQUEST,
          error: "End of financial period must be greater than the start of the financial period"
        });
      }

      const maxDate = new Date(newStartDate);
      maxDate.setFullYear(newStartDate.getFullYear() + 1);

      if (newEndDate >= maxDate) {
        return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
          status: httpConstants.HTTP_STATUS_BAD_REQUEST,
          error: "End of financial period must be maximum 12 months after the start of the financial period"
        });
      }
    }


    const contentMessage =  'Dear Client,\n\n' +
      'Your request to re-open the completed economic substance submission for editing has been processed. The submission has reverted to "RE-OPEN" status in the submission overview.\n'+
      'To edit the submission, please select click the attached URL below. A note from your Trident Trust Officer can be viewed by clicking the information icon. Once you are finished editing, please ensure that you have submitted the amended submission prior to logging out or closing the browser.\n\n'+
      'Thank you,\n'+
      'Trident Economic Substance Team';

    if (submission) {
      let company = await CompanyModel.findOne({"masterclientcode": submission.company_data.masterclientcode, "code":req.body.company });
      submission = submission.toObject();
      if(company.hasITADate === true && company.approvedITAStartDate && company.approvedITAEndDate){
        submission.entity_details.financial_period_changed = moment(company.approvedITAStartDate).utc().format('YYYY-MM-DD') ===
          moment(submission.entity_details.financial_period_begins).utc().format('YYYY-MM-DD') &&
          moment(company.approvedITAEndDate).utc().format('YYYY-MM-DD') ===
          moment(submission.entity_details.financial_period_ends).utc().format('YYYY-MM-DD');

      }

      if (!submission.reopened) {
        submission.reopened = {
          details: [],
        };
      }
      submission.reopened.details.push({
        date_reopened: new Date(),
        reopened_by: req.session.user.username,
        reason: req.body.reason,
        change_financial_period_dates: req.body.changePeriodDate,
        old_start_date:req.body.changePeriodDate === true ? submission.entity_details.financial_period_begins : undefined,
        old_end_date:req.body.changePeriodDate === true ? submission.entity_details.financial_period_ends : undefined,
        new_start_date: req.body.changePeriodDate === true ? req.body.newFinancialStartDate : undefined,
        new_end_date: req.body.changePeriodDate === true ? req.body.newFinancialEndDate : undefined,
      });
      submission.status = "RE-OPEN";
      submission.currentStepForm =null;


      if( req.body.changePeriodDate  === true){
        submission.entity_details.financial_period_begins = req.body.newFinancialStartDate;
        submission.entity_details.financial_period_ends = req.body.newFinancialEndDate;
      }
      await EntryModel.findOneAndUpdate({ _id: req.body.id, company: req.body.company }, submission);
      const urls = [{
        url: `${process.env.CLIENTPORTAL_APP_HOST}/masterclients/${company.masterclientcode}/substance/companies/${company.code}/validate-submissions`,
        name: 'Company submissions',
      }];

      const emailSubject = "Economic Substance Request - " + company.code + "\r\n" +
        "Re-open submission - Change Required";

      await createAnnouncementForMCC(company.masterclientcode,
        "Economic Substance Request – Change required",
        emailSubject,
        contentMessage,
        urls,
        []);
      res.json({ success: true });
    } else {
      const archivedSubmission = await ArchivedEntryModel.findOne({ _id: ObjectId(req.body.id), company: req.body.company });
      if (!archivedSubmission) {
        throw new Error('Entry not found');
      } else {
        let company = await CompanyModel.findOne({ "masterclientcode": archivedSubmission.company_data.masterclientcode, "code":req.body.company });
        archivedSubmission.company_data.partitionkey = 'company';
        const newEntry = new EntryModel({
          _id: archivedSubmission._id,
          company: req.body.company,
          company_data: archivedSubmission.company_data,
          status: "RE-OPEN",
          created_by: archivedSubmission.created_by,
          version: archivedSubmission.version
        });
        await newEntry.save();
        const fullEntry = new EntryModel({ ...archivedSubmission.toObject() });

        if (!fullEntry.reopened) {
          fullEntry.reopened = {
            details: [],
          };
        }
        fullEntry.reopened.details.push({
          date_reopened: new Date(),
          reopened_by: req.session.user.username,
          reason: req.body.reason,
          changeFinancialPeriodDates: req.body.changePeriodDate,
          oldStartDate:req.body.changePeriodDate === true && ArchivedEntryModel.entity_details?.financial_period_begins ?
            ArchivedEntryModel.entity_details?.financial_period_begins : undefined,
          oldEndDate:req.body.changePeriodDate === true &&  ArchivedEntryModel.entity_details?.financial_period_ends  ?
            ArchivedEntryModel.entity_details?.financial_period_ends : undefined,
          newStartDate: req.body.changePeriodDate === true ? newStartDate : undefined,
          newEndDate: req.body.changePeriodDate === true ? newEndDate : undefined,
        });
        fullEntry.status ="RE-OPEN";
        fullEntry.currentStepForm = null;
        await EntryModel.findOneAndUpdate({ _id: req.body.id, company: req.body.company }, fullEntry);
        await ArchivedEntryModel.findOneAndDelete({ _id: req.body.id, company: req.body.company });
        const urls = [{
          url: `${process.env.CLIENTPORTAL_APP_HOST}/masterclients/${company.masterclientcode}/substance/companies/${company.code}/forms`,
          name: 'Company submissions',
        }];
        const emailSubject = "Economic Substance Request - " + company.code + ". \r\n" +
          "Re-open submission - Change Required";

        await createAnnouncementForMCC(company.masterclientcode,
          "Economic Substance Request – Change required",
          emailSubject,
          contentMessage,
          urls,
          []);
        res.json({ success: true });
      }
    }
  } catch (err) {
    console.log(err);
    res.json({ success: false });
  }
};

/**
 * Reset to saved multiple entries.
 * @function processBulkEntryResetToSaved
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the log file of the list of entries re-opened
 */
exports.processBulkEntryResetToSaved = async function (req, res) {
  try {

    if (!req.body.reason || req.body.reason === "") {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please provide the reason to re-open the submissions"
      });
    }
    else if (req.body.reason && req.body.reason.length < 50) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please provide more information in the note for the client."
      });
    }

    let submissions = await EntryModel.find({ _id: { "$in": req.body.entryIds } }, { _id: 1, company_data: 1, status: 1, reopened: 1 });

    if (submissions.length === 0){
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Submissions not found"
      });
    }

    const contentMessage = 'Dear Client,\n\n' +
      'Your request to re-open the completed economic substance submission for editing has been processed. The submission has reverted to "RE-OPEN" status in the submission overview.\n' +
      'To edit the submission, please select click the attached URL below. A note from your Trident Trust Officer can be viewed by clicking the information icon. Once you are finished editing, please ensure that you have submitted the amended submission prior to logging out or closing the browser.\n\n' +
      'Thank you,\n' +
      'Trident Economic Substance Team';

    let logfileRows = [["ID", "COMPANY", "DATE", "ACTION"]]

    for(let i=0; i < submissions.length; i++ ){
      const submission = submissions[i];

      if(submission.status !== "SUBMITTED" && submission.status !== "PAID"){
        logfileRows.push([submission._id.toString(), submission.company_data?.code || "",  new Date(), 'INVALID ACTION']);
        continue;
      }

      let reopenedObject = !submission.reopened?.details ? { details: [] } : submission.reopened;

      reopenedObject.details.push({
        date_reopened: new Date(),
        reopened_by: req.session.user.username,
        reason: req.body.reason,
        change_financial_period_dates: false,
        old_start_date:  undefined,
        old_end_date:  undefined,
        new_start_date:undefined,
        new_end_date:  undefined,
      });

      const updateValues = {
        currentStepForm: null,
        status: "RE-OPEN",
        reopened: reopenedObject
      }

      const updated = await EntryModel.findByIdAndUpdate(submission._id, updateValues, {new:true});

      if(updated){
        const urls = [{
          url: `${process.env.CLIENTPORTAL_APP_HOST}/masterclients/${submission.company_data.masterclientcode}/substance/companies/${submission.company_data.code}/validate-submissions`,
          name: 'Company submissions',
        }];

        const emailSubject = "Economic Substance Request - " + submission.company_data.code + "\r\n" +
          "Re-open submission - Change Required";

        await createAnnouncementForMCC(submission.company_data.masterclientcode,
          "Economic Substance Request – Change required",
          emailSubject,
          contentMessage,
          urls,
          []);
        logfileRows.push([submission._id.toString(), submission.company_data?.code || "", new Date(), 'SUCCESS']);
      }else{
        logfileRows.push([submission._id.toString(), submission.company_data?.code || "", new Date(), 'ERROR UPDATING']);
      }
    } 


    const workbook = xlsx.utils.book_new();
    const ws = xlsx.utils.aoa_to_sheet(logfileRows)
    xlsx.utils.book_append_sheet(workbook, ws, 'Sheet1');
    const xlsxData = xlsx.write(workbook, { bookType: 'xlsx', type: 'base64' });


    res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "The bulk reset to saved has been done successfully",
      data: {
        xlsxData: xlsxData,
        filename: "rts_bulk_log_" + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx'
      }
    });
  } catch (err) {
    console.log(err);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
};


exports.updateFinancialPeriod = async function (req, res) {
  try {
    let submission = await EntryModel.findOne({ _id: ObjectId(req.body.id), company: req.body.company });
    let newStartDate;
    let newEndDate;

    if (!req.body.newFinancialStartDate || !req.body.newFinancialEndDate){
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please select financial period start/end dates"
      });
    }
    newStartDate = moment(req.body.newFinancialStartDate, STANDARD_DATE_FORMAT).utc().toDate();
    newEndDate = moment(req.body.newFinancialEndDate, STANDARD_DATE_FORMAT).utc().toDate();

    if (newEndDate < newStartDate) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "End of financial period must be greater than the start of the financial period"
      });
    }

    if (submission) {
      submission = submission.toObject();
      const company = await CompanyModel.findOne({ "masterclientcode": submission.company_data.masterclientcode, "code": req.body.company });
      if(company.hasITADate === true && company.approvedITAStartDate && company.approvedITAEndDate){
        submission.entity_details.financial_period_changed = moment(company.approvedITAStartDate).utc().format('YYYY-MM-DD') ===
          moment(submission.entity_details.financial_period_begins).utc().format('YYYY-MM-DD') &&
          moment(company.approvedITAEndDate).utc().format('YYYY-MM-DD') ===
          moment(submission.entity_details.financial_period_ends).utc().format('YYYY-MM-DD');
      }

      if (!submission.financial_period_changes?.details) {
        submission.financial_period_changes = { details: [] };
      }

      submission.financial_period_changes.details.push({
        date_changed: new Date(),
        changed_by: req.session.user.username,
        old_start_date: submission.entity_details.financial_period_begins,
        old_end_date: submission.entity_details.financial_period_ends,
        new_start_date: req.body.newFinancialStartDate,
        new_end_date: req.body.newFinancialEndDate,
      });

      submission.entity_details.financial_period_begins = req.body.newFinancialStartDate;
      submission.entity_details.financial_period_ends = req.body.newFinancialEndDate;

      await EntryModel.findOneAndUpdate({ _id: req.body.id, company: req.body.company }, submission);

      res.json({ success: true });
    } else {
      const archivedSubmission = await ArchivedEntryModel.findOne({ _id: ObjectId(req.body.id), company: req.body.company });
      if (!archivedSubmission) {
        throw new Error('Entry not found');
      } 
      else {
        submission = archivedSubmission.toObject();
        const company = await CompanyModel.findOne({"masterclientcode": submission.company_data.masterclientcode, "code": submission.company_data.code });
        if(company.hasITADate === true && company.approvedITAStartDate && company.approvedITAEndDate){
          submission.entity_details.financial_period_changed = moment(company.approvedITAStartDate).utc().format('YYYY-MM-DD') ===
            moment(submission.entity_details.financial_period_begins).utc().format('YYYY-MM-DD') &&
            moment(company.approvedITAEndDate).utc().format('YYYY-MM-DD') ===
            moment(submission.entity_details.financial_period_ends).utc().format('YYYY-MM-DD');
        }

        if (!submission.financial_period_changes?.details) {
          submission.financial_period_changes = { details: [] } ;
        }

        submission.financial_period_changes.details.push({
          date_reopened: new Date(),
          reopened_by: req.session.user.username,
          old_start_date: submission.entity_details.financial_period_begins,
          old_end_date: submission.entity_details.financial_period_ends,
          new_start_date: req.body.newFinancialStartDate,
          new_end_date: req.body.newFinancialEndDate,
        });

        submission.entity_details.financial_period_begins = req.body.newFinancialStartDate;
        submission.entity_details.financial_period_ends = req.body.newFinancialEndDate;

        await ArchivedEntryModel.findOneAndUpdate({ _id: req.body.id, company: req.body.company }, submission);

        res.json({ success: true });
      }
    }
  } catch (err) {
    console.log(err);
    res.json({ success: false });
  }
};

exports.doExportEvidence = async function (req, res, next) {
  try {
    let entryIds = req.body["entryIds"].split(";");

    const containerClient = getContainerClient(
      process.env.AZURE_STORAGE_CONTAINER_SUBSTANCE_UPLOADS,
      process.env.AZURE_STORAGE_ACCOUNT,
      process.env.AZURE_STORAGE_ACCESS_KEY
    );
    const archive = archiver("zip", {
      zlib: { level: 9 },
    });

    let finished = 0;

    for (let id of entryIds) {
      EntryModel.findById(id, async function (err, entry) {
        if (!err) {
          archive.append("", { name: `${entry.company_data.code}/` })

          // Tax Residency Evidence
          if (entry.tax_residency && Object.keys(entry.tax_residency).length !== 0) {
            const tax_residency_keys = ["evidence_non_residency", "evidence_provisional_treatment_non_residency"];
            for (const key of tax_residency_keys) {
              const value = entry.tax_residency[key];

              if (value?.length > 0) {
                for (let evidence of value) {
                  const blobClient = containerClient.getBlobClient(`${id}/${evidence.blobName}`);
                  let stream = (await blobClient.download()).readableStreamBody;
                  archive.append(stream, {
                    name: `${entry.company_data.code}/${key}/${evidence.originalname}`,
                  });
                }
              }

            }
          }

          // Relevant activities
          let objectEntry = Object.entries(entry.toObject());
          for (const [entryKey, entryValue] of objectEntry) {
            if (
              entryKey === "banking_business" ||
              entryKey === "insurance_business" ||
              entryKey === "fund_management_business" ||
              entryKey === "finance_leasing_business" ||
              entryKey === "headquarters_business" ||
              entryKey === "shipping_business" ||
              entryKey === "holding_business" ||
              entryKey === "intellectual_property_business" ||
              entryKey === "service_centre_business"
            ) {
              let relevantActivity = Object.entries(entryValue);
              for (const [
                relevantActivityKey,
                relevantActivityValue,
              ] of relevantActivity) {
                if (
                  relevantActivityKey === "evidence_equipment" ||
                  relevantActivityKey === "outsourcing_evidence" ||
                  relevantActivityKey === "support_documents" ||
                  relevantActivityKey === "tangible_assets_explanation_files" ||
                  relevantActivityKey === "intangible_assets_decisions_files" ||
                  relevantActivityKey === "intangible_assets_nature_files" ||
                  relevantActivityKey === "intangible_assets_trading_nature_files" ||
                  relevantActivityKey === "other_ciga_business_files" ||
                  relevantActivityKey === "other_ciga_decisions_files" ||
                  relevantActivityKey === "other_ciga_evidence_files" ||
                  relevantActivityKey === "other_ciga_files" ||
                  relevantActivityKey === "high_risk_ip_evidence"
                ) {
                  for (let evidence of relevantActivityValue) {
                    const blobClient = containerClient.getBlobClient(`${id}/${evidence.blobName}`);
                    let stream = (await blobClient.download()).readableStreamBody;
                    archive.append(stream, {
                      name: `${entry.company_data.code}/relevant_activities/${entryKey}/${relevantActivityKey}/${evidence.originalname}`,
                    });
                  }
                }
              }
            }
          }

          if (entry.supporting_details?.support_attachments?.length > 0) {
            const noneEvidences = entry.supporting_details.support_attachments;
            for (let evidence of noneEvidences) {
              const blobClient = containerClient.getBlobClient(`${id}/${evidence.blobName}`);
              let stream = (await blobClient.download()).readableStreamBody;
              archive.append(stream, {
                name: `${entry.company_data.code}/relevant_activities/supporting_documents/${evidence.originalname}`,
              });
            }
          }

          finished++;
          if (finished === entryIds.length) {
            archive.on("error", function (err) {
              throw err;
            });

            archive.pipe(res);
            archive.finalize();
          }
        }
      });
    }
  }catch(e){
    console.log(e);
    next(e);
  }

};

exports.getImportEntries = function (req, res) {
  res.render("substance/import-entries", { title: "Import Entries" });
};

exports.saveImportEntries = function (req, res) {
  let data = req.body.importedData;
  const entries = [];
  //Format fields
  for (let entry of data.entries) {
    if (entry.error === "") {
      entries.push({
        company: entry.code?.trim(),
        company_data: entry.companyData,
        version: "2.0",
        created_by: req.session.user.username,
        status: "SUBMITTED",
        submitted_at: new Date(new Date().toUTCString()),
        submitted_by: req.session.user.username,
        tax_residency: {
          resident_in_BVI: true
        },
        entity_details: {
          financial_period_begins: entry.periodStart,
          financial_period_ends: entry.periodEnd,
          financial_period_changed: entry.periodChange === "Y",
        },
        relevant_activities: {
          none: {
            selected: true,
            part_of_financial_period: true,
            financial_periods: [
              {
                financial_period_begins: entry.periodStart,
                financial_period_ends: entry.periodEnd,
              },
            ],
          },
          banking_business: {
            selected: false,
            part_of_financial_period: false,
          },
          insurance_business: {
            selected: false,
            part_of_financial_period: false,
          },
          fund_management_business: {
            selected: false,
            part_of_financial_period: false,
          },
          finance_leasing_business: {
            selected: false,
            part_of_financial_period: false,
          },
          headquarters_business: {
            selected: false,
            part_of_financial_period: false,
          },
          shipping_business: {
            selected: false,
            part_of_financial_period: false,
          },
          holding_business: {
            selected: false,
            part_of_financial_period: false,
          },
          intellectual_property_business: {
            selected: false,
            part_of_financial_period: false,
          },
          service_centre_business: {
            selected: false,
            part_of_financial_period: false,
          },
        },
        confirmation: {
          confirmed: true,
          confirmed_authority: true,
          confirmed_conditions: true,
          confirmed_payment: true,
          user_fullname: req.session.user.username,
          user_phonenumber: "+6568186566",
          relation_to_entity: "Other (please specify)",
          relation_to_entity_other: "Authorized Agent",
        },
      });
    }
  }
  EntryModel.insertMany(entries, function (err) {
    if (!err) {
      res.json({ success: true, inserted: entries.length });
    } else {
      console.log(err);
      res.json({ success: false });
    }
  });
};

exports.processImportEntries = async function (req, res, next) {
  try{
    if (!req.files) {
      res.json({ error: "Files not found" });
      return;
    }
    const file = req.files.fileUploaded;
    const data = new Uint8Array(file.data);
    const workbook = xlsx.read(data, {
      type: "array",
      cellText: false,
      cellDates: true,
    });
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];

    const validationFields = {};
    const entries = [];
    const companies = [];

    for (let cell in worksheet) {
      const cellAsString = cell.toString();

      // SAVE TITLES FOR VALIDATION

      if (cellAsString === "A1") {
        validationFields.column1 = worksheet[cell].v;
      }
      if (cellAsString === "B1") {
        validationFields.column2 = worksheet[cell].v;
      }
      if (cellAsString === "C1") {
        validationFields.column3 = worksheet[cell].v;
      }
      if (cellAsString === "D1") {
        validationFields.column4 = worksheet[cell].v;
      }
      if (cellAsString === "E1") {
        validationFields.column5 = worksheet[cell].v;
      }

      if (cellAsString.substring(1) > 3) {
        if (!entries[cellAsString.substring(1) - 4]) {
          entries.push({});
        }
        if (cellAsString[0] === "A") {
          const code = worksheet[cell].v ? worksheet[cell].v.toString().trim() : "";
          entries[cellAsString.substring(1) - 4].code = code;
          companies.push(code);
        }
        if (cellAsString[0] === "B") {
          entries[cellAsString.substring(1) - 4].periodChange = worksheet[cell].v;
        }
        if (cellAsString[0] === "C") {
          entries[cellAsString.substring(1) - 4].periodStart = worksheet[cell].v;
        }
        if (cellAsString[0] === "D") {
          entries[cellAsString.substring(1) - 4].periodEnd = worksheet[cell].v;
        }
        if (cellAsString[0] === "E") {
          entries[cellAsString.substring(1) - 4].relevantActivity =
            worksheet[cell].v;
        }
      }
    }

    // Get DB data
    const entryList = await EntryModel.find(
      { company: { $in: companies } },
      { entity_details: 1, company: 1 }
    );
    const companyList = await CompanyModel.find({ code: { $in: companies } });

    // Validate format
    if (
      validationFields.column1 === "Entity Unique ID" &&
      validationFields.column2 === "Period Change (Y/N)" &&
      validationFields.column3 === "Financial Period Start Date (DD/MM/YYYY)" &&
      validationFields.column4 === "Financial Period End Date (DD/MM/YYYY)" &&
      validationFields.column5 === "Relevant Activity"
    ) {
      for (const entry of entries) {
        entry.error = "";
        // Validate required
        if (
          !(
            entry.code &&
            entry.periodChange &&
            entry.periodStart &&
            entry.periodEnd &&
            entry.relevantActivity
          )
        ) {
          entry.error += "Missing required data. ";
        }
        //Validate company

        entry.companyData = companyList.find(
          (company) => company.code === entry.code
        );

        if (!entry.companyData) {
          entry.error += "Company not found. ";
        }

        // Validate financial period
        if (entry.periodStart && entry.periodEnd) {
          if (entry.periodEnd <= entry.periodStart) {
            entry.error += "Invalid financial period. ";
          }
          let maxDate = moment
            .utc(new Date(entry.periodStart))
            .subtract(1, "day")
            .add(1, "year")
            .toDate();

          if (entry.periodEnd > maxDate) {
            entry.error += "Invalid financial period. ";
          }
        }

        //get all submissions for this company and check if there is any submission
        // - with a startdate between the start and end date of another submission
        // - with a enddate between the start and end date of another submission
        const companyEntries = entryList.filter(
          (entryItem) => entryItem.company === entry.code
        );
        for (let dbEntry of companyEntries) {
          if (dbEntry.entity_details) {
            if (
              entry.periodStart >=
              dbEntry.entity_details.financial_period_begins &&
              entry.periodStart < dbEntry.entity_details.financial_period_ends
            ) {
              entry.error += "Financial period is already submitted. ";
              break;
            }
            if (
              entry.periodEnd >= dbEntry.entity_details.financial_period_begins &&
              entry.periodEnd <= dbEntry.entity_details.financial_period_ends
            ) {
              entry.error += "Financial period is already submitted. ";
              break;
            }
          }
        }
      }
      res.json({ entries });
    } else {
      res.json({ error: "Invalid data template" });
    }
  }catch(e){
    console.log(e);
    next(e);
  }

};

/**
 * Create a new request information object for and entry and change entry status to INFORMATION REQUEST.
 * Called from Request information button in substance search view.
 * @function sendRequestInformation
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the response of the process.
 */
exports.sendRequestInformation = async function(req, res) {
  try {
    const entry = await EntryModel.findById(req.params.entryId);

    if(!entry){
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Entry not found."
      });
    }


    let currentPendingRfi = false;
    const requestedInformationList = entry.requested_information?.details || [];

    if (requestedInformationList.length > 0){
      currentPendingRfi = requestedInformationList.some((r) => r.status !== 'CANCELLED' && r.status !== "RETURNED")
    } 

    if (entry.status === "INFORMATION REQUEST" || currentPendingRfi === true){
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "There is an information request already pending."
      });
    }

    if (!req.body.requestComment || req.body.requestComment === ""){
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please specify the information that the client has to provide."
      });
    }

    if (!req.body.deadLineDate || req.body.deadLineDate === ""){
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please enter the request deadline."
      });
    }
    const rfiFiles = req.session.requestedTempRFIFiles && req.session.requestedTempRFIFiles[req.params.entryId] ?
      req.session.requestedTempRFIFiles[req.params.entryId] : [];

    let attachments = [];
    if(rfiFiles?.length > 3){
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "The maximum of documents to send in the request information is three, please remove one."
      });
    }
    else if(rfiFiles?.length > 0  && rfiFiles.length <= 3){
      console.log("rfiFiles ", rfiFiles);
      const fileTypeId = uuidv4();
      rfiFiles.forEach((f) => {
        f.fileTypeId =fileTypeId;

        attachments.push({
          fileId: f.fileId,
          fileTypeId: f.fileTypeId,
          fieldName: f.fieldname,
          originalName: f.originalname,
          encoding: f.encoding,
          mimeType: f.mimetype,
          blobName: f.blobName,
          container: f.container,
          blob: f.blob,
          blobType: f.blobType,
          size: f.size,
          etag:f.etag,
          url: f.url,
        });
      })

    }

    const newRequestInfo = {
      username: req.session.user.username,
      requested_at: new Date(),
      deadline_at: moment(req.body.deadLineDate, 'MM/DD/YYYY').utc().format('YYYY-MM-DD'),
      comment: req.body.requestComment,
      status: "REQUESTED",
      message_id: null,
      files: rfiFiles
    };

    // create announcement

    const content = `Dear Client,
    
Request for information - ${entry.company_data.name}

Please be advised that the International Tax Authority (ITA) is requesting additional information in relation to the economic substance submission which was filed for the Entity for the financial period ending ${moment(entry.entity_details.financial_period_ends).utc().format('MMMM DD, YYYY')}. 
The deadline for responding to the ITA’s request is ${moment(req.body.deadLineDate, 'MM/DD/YYYY').utc().format('MMMM DD, YYYY')}.

The details of the ITA request can be viewed in the Companies module, or you can reach it directly by clicking the attached URL below. 

Thank you,
Trident Economic Substance Team`;

    const urls = [{
      url: `${process.env.CLIENTPORTAL_APP_HOST}/masterclients/${entry.company_data.masterclientcode}/company-files`,
      name: 'Information requests',
    }];

    const emailSubject = "Economic Substance Request - " + entry.company_data.code + ". \r\n"+
      "Section 11 Notice to Produce Information - ITA Request";

    const newMessage = await createAnnouncementForMCC(
      entry.company_data.masterclientcode,
      "Economic Substance Request – ITA request",
      emailSubject,
      content,
      urls,
      attachments);

    newRequestInfo.message_id = newMessage._id;
    const requestedInfo = entry.requested_information?.details || [];
    requestedInfo.push(newRequestInfo);

    const newValues = {
      status: "INFORMATION REQUEST",
      requested_information: {details: requestedInfo},
      updatedAt: new Date()
    }; 

    await EntryModel.findByIdAndUpdate(entry._id, newValues);
    req.session.requestedTempRFIFiles = {};
    res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "The request information has been send successfully"
    });
  }catch (e) {
    console.log("err ", e);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
};

/**
 * Cancel the current request information object for substance entry.
 * Called from Cancel RFI button in substance search view.
 * @function cancelRequestInformation
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the response of the process.
 */
exports.cancelRequestInformation = async function(req, res) {
  try {
    const entry = await EntryModel.findById(req.params.entryId);

    if(!entry){
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Entry not found"
      });
    }

    if (!req.body.reason || req.body.reason === ""){
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
        status: httpConstants.HTTP_STATUS_BAD_REQUEST,
        error: "Please provide the reason for canceling the current information request"
      });
    }

    let updateValues= {
      status: entry.payment?.payment_received_at && entry.payment?.payment_reference ? "PAID" : "SUBMITTED",
      requested_information: entry.requested_information,
      client_returned_information: entry.client_returned_information?.details ? entry.client_returned_information : {details: []},

    };
    const requestedInformationList = entry.requested_information?.details || [];

    const requestedInfoIndex = requestedInformationList.findIndex((rfi) => rfi.status === "REQUESTED");
    if (requestedInfoIndex > -1){
      const requestedInfo = requestedInformationList[requestedInfoIndex];
      requestedInfo.status = "CANCELLED";
      const cancelInfo = {
        request_id:requestedInfo.id,
        username: req.session.user.username,
        returned_at: new Date(),
        comment: req.body.reason,
        is_canceled: true,
        files: []
      };
      requestedInformationList[requestedInfoIndex] = requestedInfo;


      updateValues.requested_information = { details: requestedInformationList};
      updateValues.client_returned_information.details.push(cancelInfo);

      await MessageModel.findOneAndDelete({_id: requestedInfo.message_id, status: "SCHEDULED"});

    }

    await EntryModel.findByIdAndUpdate(entry._id, updateValues);
    res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "The request information has been cancelled successfully"
    });
  }catch (e) {
    console.log("err ", e);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
};

/**
 * Get all the request information and client response for substance entry.
 * Called from Show Info button in substance search view.
 * @function getRequestInformation
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the entryId, company and request information data form the entry
 */
exports.getInformationDetails = async function(req, res) {
  try {
    const entry = await EntryModel.findById(req.params.entryId);

    if(!entry){
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Entry not found"
      });
    }

    let requestedInfo = entry.requested_information?.details || [];

    if (requestedInfo.length > 0 && entry.client_returned_information?.details?.length > 0){
      requestedInfo = requestedInfo.map((rfi) =>{
        const rfiData =rfi.toObject();
        rfiData.client_response = entry.client_returned_information.details.filter((clientResponse) => clientResponse.request_id === rfi.id);
        return rfiData;
      });

      console.log("requestedInfo ", requestedInfo)

      requestedInfo.sort((firstRFI,nextRFI)=>nextRFI.requested_at.getTime() - firstRFI.requested_at.getTime());
    }

    const reopenedInfo = entry.reopened && entry.reopened.details?.length > 0 ? entry.reopened.details : [];
    if (reopenedInfo.length > 0){
      reopenedInfo.sort((currentReopen,nextReopen)=>nextReopen.date_reopened.getTime() - currentReopen.date_reopened.getTime());

      const lastReopened = reopenedInfo[0];
      if (entry.status !== "RE-OPEN" && !lastReopened.resubmitted_at){
        lastReopened.resubmitted_at = entry.submitted_at;
        lastReopened.resubmitted_by = entry.submitted_by;
        reopenedInfo[0] = lastReopened;
      }

    }
    res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      entryData: {
        entryId: entry._id,
        company: entry.company,
        financialPeriodChangesInfo: entry.financial_period_changes?.details || [],
        requestedInfo,
        reopenedInfo
      }
    });
  }catch (e) {
    console.log("err ", e);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
};


/**
 * Create Message object in db for a new annoucement of MCC.
 * @function createAnnouncementForMCC
 * @param {string}  mcc Master client code to send new announcement.
 * @param {string}  subject Subject of the announcement in client portal.
 * @param {string}  emailSubject Subject of the announcement email.
 * @param {string}  content Body message of the announcement.
 * @param {array}  urls Url-Name object array to attach to the announcement.
 * @param {array}  files attachments object array to attach to the announcement.
 * @return MessageModel object with the new announcement data.
 */
async function createAnnouncementForMCC(mcc, subject, emailSubject, content, urls = [], files) {
  try {
    // create announcement
    const hoursPeriod = 60 * 60 * 1000;
    const currentDate = new Date();

    let newMessage = new MessageModel({
      _id: ObjectId(),
      subject: subject,
      emailSubject: emailSubject ? emailSubject : subject,
      content: content,
      sendToAll: false,
      masterClientCodes: mcc,
      scheduleMessage: false,
      scheduledAt: new Date(Math.ceil(currentDate.getTime() / hoursPeriod) * hoursPeriod),
      status: 'SCHEDULED',
      urls,
      files: files || []
    });

    await newMessage.save();
    return newMessage;
  }catch (e) {
    console.log(e);
    return null;
  }

}


/**
 * Render the view for import payments
 * @function getImportPayments
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return import-payment view
 */
exports.getImportPayments = function (req, res) {
  res.render("substance/import-payments", { title: "Import Substance Payments" });
};

/**
 * Read XLSX uploaded for import entry payments.
 * Format: column A: company code, column B: submission year (01/01/2020 or 2020)
 * @function processImportEntryPayments
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the entry payments processed
 */
exports.processBulkEntryPayments = async function (req, res) {
  try {
    if (!req.files) {
      res.json({ error: "Files not found" });
      return;
    }
    const file = req.files.fileUploaded;
    const data = new Uint8Array(file.data);
    const workbook = xlsx.read(data, {
      type: "array",
      cellText: false,
      cellDates: true,
    });
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];

    const validationFields = {};
    const entries = [];
    const companies = [];
    const availablePaymentYears = utils.getAvailablePaymentYearsList();

    for (let cell in worksheet) {
      const cellAsString = cell.toString();
      // SAVE TITLES FOR VALIDATION
      if (cellAsString === "A1") {
        validationFields.column1 = worksheet[cell].v;
      }
      if (cellAsString === "B1") {
        validationFields.column2 = worksheet[cell].v;
      }

      const rowNumber = Number(cellAsString.replace(/\D/g, ''));
      const dataStartRow = 2;
      const companyIndex = rowNumber - dataStartRow;
      const rowName = cellAsString.replace(/[0-9]/g, '');

      if (companyIndex >= 0 && worksheet[cell].v) {
        if (!entries[companyIndex]) {
          entries.push({
            code: '',
            year: '',
          })
        }
        if (rowName === "A") {
          const code = worksheet[cell].v ? worksheet[cell].v.toString().trim() : "";
          entries[companyIndex].code = code;
          companies.push(code);
        }
        if (rowName === "B") {
          const year = worksheet[cell].v;
          entries[companyIndex].year = year ? moment(year.toString().trim()).utc().format('YYYY') : undefined;
        }
      }
    }


    // Get DB data
    const entryList = await EntryModel.find(
      { company: { $in: companies } },
      { entity_details: 1, company: 1, payment:1 }
    );
    const uniqueCompanies = [...new Set(companies)];
    const companyList = await CompanyModel.find({ code: { $in: uniqueCompanies } });

    // Validate format
    if (validationFields.column1?.toLowerCase() === "company code" &&
      validationFields.column2?.toLowerCase()  === "year") {
      for (const entry of entries) {
        entry.error = "";
        // Validate required
        if (!(entry.code && entry.year)) {
          entry.error += "Missing required data. ";
        }
        //Validate company
        entry.companyData = companyList.find(
          (company) => company.code === entry.code
        );

        if (!entry.companyData) {
          entry.error += "Company not found. ";
          continue;
        }
        // Validate year
        if (!(moment(entry.year).utc().isValid())){
          entry.error += "Invalid date. ";
        }else{
          //get all submissions for this company and check if there is any submission
          // - with a enddate with same year

          const isValidYear = availablePaymentYears.includes(entry.year) === true;
          if(!isValidYear){
            entry.error += "Invalid available year for import the payment.";
          }else{
            const companyEntries = entryList.filter(
              (entryItem) => entryItem.company === entry.code
            );
            let existsSubmissionWithEndYearAndUnpaid = false;
            let alreadyPaidCount = 0;
            let entriesWithoutFinancialPeriod = companyEntries.length > 0 ?
              companyEntries.filter((c) => !c.entity_details?.financial_period_ends) : [];
            if (entriesWithoutFinancialPeriod.length > 0){
              entry.error +=  "The company has submissions with financial period dates empty or invalid."
            }else{
              for (let dbEntry of companyEntries) {
                if (dbEntry.entity_details && dbEntry.entity_details.financial_period_ends) {
                  if (moment(dbEntry.entity_details.financial_period_ends).utc().format('YYYY') === entry.year){

                    if(!dbEntry.payment?.payment_received_at){
                      existsSubmissionWithEndYearAndUnpaid = true;
                      break;
                    }else{
                      alreadyPaidCount +=1;
                    }
                  }
                }
              }

              if(!existsSubmissionWithEndYearAndUnpaid && alreadyPaidCount > 0){
                entry.error += "Submission with financial period end date in year " + entry.year + " is already paid. ";
              }
            }

          }


        }

      }
      res.json({ entries });
    } else {
      res.json({ error: "Invalid data template" });
    }
  }catch (e) {
    console.log("Error loading import payments: ", e)
    res.json({ error: "Error loading the import payments" });
  }

};

/**
 * Mark the processed entries as paid.
 * @function updateBulkEntryPayments
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the count of entries marks as paid and the count of errors
 */
exports.updateBulkEntryPayments = async function (req, res) {
  try {
    let data = req.body.importedData;
    let entriesUpdated = 0;
    let errorUpdating = [];
    let emailEntries = [];

    const companies = [...new Set(data.entries.map((e) => e.code))];
    console.log("Total companies to update payments : ", companies.length);
    const entryList = await EntryModel.find(
      { company: {$in: companies}, "payment.payment_received_at": null},
      {_id: 1, status: 1,company_data:1, entity_details: 1, company: 1,
        supporting_details: 1,
        banking_business: 1,
        insurance_business: 1,
        fund_management_business: 1,
        finance_leasing_business: 1,
        headquarters_business: 1,
        shipping_business: 1,
        holding_business: 1,
        intellectual_property_business: 1,
        service_centre_business: 1}
    );

    const availablePaymentYears = utils.getAvailablePaymentYearsList();


    for (let entry of data.entries) {
      try {
        if (entry.error === "") {
          let submissionsWithoutFinancialPeriod =0;
          const companyEntries =  entryList.filter((entryItem) => {
            if (entryItem.company === entry.code){
              if(entryItem.entity_details && entryItem.entity_details.financial_period_ends){
                if (moment(entryItem.entity_details.financial_period_ends).utc().format('YYYY') === entry.year){
                  return entryItem;
                }
              }else{
                submissionsWithoutFinancialPeriod++;

              }
            }
          });


          const isValidYear = availablePaymentYears.includes(entry.year) === true;
          if(!isValidYear){
            console.log("Is not possible import payment for company "+ entry.code+" because the year "+ entry.year + " is invalid.");
            errorUpdating.push({
              code: entry.code,
              year: entry.year,
              error: "Invalid available year for import the payment."
            });
            continue;
          }

          if(submissionsWithoutFinancialPeriod > 0){
            console.log("Entry for company " + entry.code + " year " + entry.year +" has invalid financial period dates.");
            errorUpdating.push({
              code: entry.code,
              year: entry.year,
              error: "The company has submissions with financial period dates empty or invalid."
            });
            continue;
          }


          if (companyEntries && companyEntries.length > 0){
            const currentDate = new Date(new Date().toUTCString());

            for(let companyEntry of companyEntries){

              const updatedData = {
                status:  companyEntry.status === "SUBMITTED" ? "PAID" : companyEntry.status,
                payment : {
                  payment_type: "WIRE TRANSFER",
                  payment_reference:
                    companyEntry.company_data.masterclientcode +
                    "-" +
                    companyEntry.company_data.code +
                    "-" +
                    currentDate.getFullYear() +
                    (currentDate.getMonth() + 1).toString() +
                    currentDate.getDate().toString(),
                  payment_received_at: currentDate,
                  batchpayment_id: "0",
                  batchpayment_transactionId: req.session.user.name,
                  batchpayment_code: "0",
                  amount: companyEntry.company_data.amount,
                }
              };
              await  EntryModel.findOneAndUpdate({ _id: companyEntry._id, company: companyEntry.company },  updatedData);

              if (companyEntry.supporting_details?.support_attachments?.length > 0 ||
                companyEntry.banking_business?.support_documents?.length > 0 ||
                companyEntry.insurance_business?.support_documents?.length > 0 ||
                companyEntry.fund_management_business?.support_documents?.length > 0 ||
                companyEntry.finance_leasing_business?.support_documents?.length > 0 ||
                companyEntry.headquarters_business?.support_documents?.length > 0 ||
                companyEntry.shipping_business?.support_documents?.length > 0 || 
                companyEntry.holding_business?.support_documents?.length > 0 || 
                companyEntry.intellectual_property_business?.support_documents?.length > 0 ||
                companyEntry.service_centre_business?.support_documents?.length > 0) {
                  emailEntries.push({
                    "code": companyEntry.company, 
                    "financialPeriodStart": companyEntry.entity_details?.financial_period_begins ? 
                      moment(companyEntry.entity_details.financial_period_begins).format("YYYY-MM-DD") : "",
                    "financialPeriodEnd": companyEntry.entity_details?.financial_period_ends ?
                      moment(companyEntry.entity_details.financial_period_ends).format("YYYY-MM-DD") : "",
                })
              }
            }
          }
          await CompanyModel.findOneAndUpdate({code: entry.code}, {
            "$addToSet": {"paymentYears": entry.year}, 
            "modifiedBy": req.session.user.username 
          });


          entriesUpdated += 1;

        }else{
          // the company was marked with error on import payment load process or was already paid for the year
          errorUpdating.push({
            code: entry.code,
            year: entry.year,
            error: entry.error
          });
        }
      }catch (e){
        console.error("Exception occurred for import payment of company "+ entry.code+ " and year " + entry.year + ": ", e);
        errorUpdating.push({
          code: entry.code,
          year: entry.year,
          error: "Unable to mark company paid on import due to error."
        });
      }
    }

    if(emailEntries.length > 0){
      let email = mailFormatController.generateClientSubmittedAttachmentsEmail(emailEntries);

      const result = await mailController.asyncSend(
        process.env.SUPER_USER_EMAIL_GROUP,
        'Trident Trust Client Notification',
        email.textString,
        email.htmlString
      );

      console.log(result)
    }
    
    if (errorUpdating.length > 0){
      console.log("Total companies with error: ", errorUpdating);
    }

    res.json({success: true, entriesUpdated: entriesUpdated, errorUpdating: errorUpdating});
  }catch (e) {
    console.log("Error importing payments: ", e);
    res.json({success: false, error: "Unexpected error has occurred saving the payments"});
  }

};

/**
 * Save in the session temporarily a information request document.
 * Called from Request information modal when upload a file in the drop zone.
 * @function saveTemporalRFIFile
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the result of the upload process.
 */
exports.saveTemporalRFIFile = async function (req, res) {
  try {
    let entry = await EntryModel.findById(req.params.entryId);

    if (!entry){
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        message: "Entry not found"
      });
    }

    let sessData = req.session;
    if (!sessData.requestedTempRFIFiles) {
      sessData.requestedTempRFIFiles = {}
    }

    let uploadedFiles = req.files['fileUploaded'];
    let newFiles = [];
    if (uploadedFiles && uploadedFiles.length > 0) {
      for (let i = 0; i < uploadedFiles.length; i++) {
        const itemToUpload = uploadedFiles[i];

        if (itemToUpload.mimetype !== 'application/pdf') {
          console.log("Incorrect file type");
          return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
            status: httpConstants.HTTP_STATUS_BAD_REQUEST,
            message: "Incorrect file type"
          });
        }
        uploadController.moveUploadSubstanceFile(req.params.entryId, itemToUpload, 'rfi-file',
          process.env.AZURE_STORAGE_CONTAINER_SUBSTANCE_UPLOADS).catch((reason => {
          if (reason) {
            console.log(reason);
            return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
              status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
              message: "Unexpected error uploading the document, please try again."
            });
          }
        }));
        let newFile = {
          fileId: uuidv4(),
          fileTypeId: null,
          fieldname: itemToUpload.fieldname.replace(/fileUploaded/i, 'rfi-file'),
          blob: itemToUpload.blob.replace(/fileUploaded/i, 'rfi-file'),
          blobName: itemToUpload.blobName.replace(/fileUploaded/i, 'rfi-file'),
          url: itemToUpload.url.replace(/fileUploaded/i, req.params.entryId + '/' + 'rfi-file'),
          originalname: itemToUpload.originalname,
          encoding: itemToUpload.encoding,
          mimetype: itemToUpload.mimetype,
          container: itemToUpload.container,
          blobType: itemToUpload.blobType,
          size: itemToUpload.size,
          etag: itemToUpload.etag
        };
        newFiles.push(newFile);
      }

      if (sessData.requestedTempRFIFiles && sessData.requestedTempRFIFiles[req.params.entryId]) {
        sessData.requestedTempRFIFiles[req.params.entryId] = [...sessData.requestedTempRFIFiles[req.params.entryId], ...newFiles];
      } else {
        sessData.requestedTempRFIFiles[req.params.entryId] = newFiles;
      }

    }
    return res.status(httpConstants.HTTP_STATUS_OK).json({status: httpConstants.HTTP_STATUS_OK, message: "File uploaded successfully"});

  } catch (error) {
    console.log("error: ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      message: "Internal server error."
    });
  }
};


/**
 * Return the list of the files uploaded and saved temporarily in the session for an entry RFI.
 * Called from Request information modal after upload a file to refresh the table list of files.
 * @function getTempRFIListFiles
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the list of files uploaded.
 */
exports.getTempRFIListFiles = async function (req, res) {
  try {
    let entry = await EntryModel.findById(req.params.entryId);

    if (!entry) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        message: "Entry not found"
      });
    }
    let filesToReturn = [];

    if (req.session && req.session.requestedTempRFIFiles && req.session.requestedTempRFIFiles[req.params.entryId]) {
      filesToReturn = req.session.requestedTempRFIFiles[req.params.entryId];
    }
    return res.status(httpConstants.HTTP_STATUS_OK).json({ status: httpConstants.HTTP_STATUS_OK, files: filesToReturn });

  } catch (error) {
    console.log("error: ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      message: "Internal server error."
    });
  }
};


/**
 * Delete a RFI file from an entry or the session requestedTempRFIFiles.
 * Called from delete file button in the request information modal.
 * @function deleteRFIFile
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the response of the delete process.
 */
exports.deleteRFIFile = async function (req, res) {
  try {
    let fileDeleted = false;
    let entry = await EntryModel.findById(req.params.entryId);
    if (!entry) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        message: "Entry not found"
      });
    }


    if (req.body.requestId){
      const requestedInformationList = entry.requested_information?.details || [];
      const rfiInfoIndex = requestedInformationList.length > 0 ? requestedInformationList.findIndex((requestInfo) =>
        requestInfo.id === req.params.requestId) : -1;

      if (rfiInfoIndex > -1){
        let rfiInfo = requestedInformationList[rfiInfoIndex];

        const fileIndex = rfiInfo.files?.length > 0 ? rfiInfo.files.findIndex((f) => f.fileId === req.body.fileId) : -1;
        if (fileIndex > -1) {
          rfiInfo.files.splice(fileIndex, 1);
          entry.requested_information.details[rfiInfoIndex] = rfiInfo;
          entry.markModified('requested_information');
          await entry.save();
          fileDeleted = true;
        }
      }
    }

    if (!fileDeleted && req.session && req.session.requestedTempRFIFiles){
      let tempFiles = req.session.requestedTempRFIFiles[req.params.entryId];
      if (tempFiles && tempFiles.length > 0) {
        const fileIndex = tempFiles.findIndex((f) => f.fileId && f.fileId === req.params.fileId);
        if (fileIndex > -1) {
          tempFiles.splice(fileIndex, 1);
          req.session.requestedTempRFIFiles[req.params.entryId] = tempFiles;
          fileDeleted = true;
        }
      }
    }


    if (fileDeleted){
      return res.json({status: httpConstants.HTTP_STATUS_OK, message: 'File deleted successfully'});
    }
    else{
      return res.json({status: httpConstants.HTTP_STATUS_BAD_REQUEST, message: 'Error deleting the file'});
    }

  } catch (error) {
    console.log("error: ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      message: 'Internal server error'});
  }
};

/**
 * Render the view for requests information
 * @function getRequestsInformationView
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @param {function} next HTTP request function to continue the request when the process fail.
 * @return request-information view
 */
exports.getRequestsInformationView = async function (req, res, next) {
  try {
    const filters = req.query;

    const rfi_filter_completed = !!(filters?.rfi_filter && filters.rfi_filter.includes('show_completed'));
    const rfi_filter_not_completed = !!(filters?.rfi_filter && filters.rfi_filter.includes('show_not_completed'));
    const rfi_filter_not_rfi_started = !!(filters?.rfi_filter && filters.rfi_filter.includes('show_not_rfi_started'));
    const rfi_show_past_deadline = !!(filters?.rfi_filter && filters.rfi_filter.includes('show_past_deadline'));

    let submissionfilter;

    if (rfi_filter_not_rfi_started === false){
      // search only submissions with request information pending/completed
      submissionfilter = { "requested_information.details": { "$exists": true, "$ne": [] } };
    }else{
      submissionfilter = { "payment.payment_received_at": { "$exists": true, "$ne": null } };
    }



    if (filters.filter_masterclient && filters.filter_masterclient.length > 2) {
      submissionfilter["company_data.masterclientcode"] = {
        $regex: filters.filter_masterclient,
        $options: "i",
      };
    }

    if (filters.filter_company && filters.filter_company.length > 2) {
      submissionfilter["company_data.name"] = {
        $regex: filters.filter_company,
        $options: "i",
      };
    }

    if (filters.filter_referral && filters.filter_referral.length > 2) {
      submissionfilter["company_data.referral_office"] = {
        $regex: filters.filter_referral,
        $options: "i",
      };
    }

    if (filters.relevant_activities && filters.relevant_activities.length) {
      filters.relevantActivities = {};
      if (Array.isArray(filters.relevant_activities)) {
        for (let relevantActivity of filters.relevant_activities) {
          submissionfilter[`relevant_activities.${relevantActivity}.selected`] = true;
          filters.relevantActivities[relevantActivity] = true;
        }
      } else {
        submissionfilter[`relevant_activities.${filters.relevant_activities}.selected`] = true;
        filters.relevantActivities[filters.relevant_activities] = true;
      }
    }

    if (filters.resident_outside_bvi && filters.resident_outside_bvi.toLowerCase() === "yes") {
      filters.relevantActivities = {};
      submissionfilter["tax_residency.resident_in_BVI"] = false;
    }

    if (filters.filter_submitted_range_start) {
      submissionfilter["submitted_at"] = {
        $gte: filters.filter_submitted_range_start,
        $lte: filters.filter_submitted_range_end ? filters.filter_submitted_range_end : new Date(),
      };
    } else if (filters.filter_submitted_range_end) {
      submissionfilter["submitted_at"] = { $lte: filters.filter_submitted_range_end };
    }

    if (filters.filter_incorporated_range_start) {
      submissionfilter["company_data.incorporationdate"] = {
        $gte: filters.filter_incorporated_range_start,
      };
    }
    if (filters.filter_incorporated_range_end) {
      submissionfilter["company_data.incorporationdate"] = {
        ...submissionfilter["company_data.incorporationdate"],
        $lte: filters.filter_incorporated_range_end
      };
    }

    if (filters.filter_financial_period_range_start) {
      submissionfilter["entity_details.financial_period_ends"] = {
        $gte: filters.filter_financial_period_range_start,
      };
    }
    if (filters.filter_financial_period_range_end) {
      submissionfilter["entity_details.financial_period_ends"] = {
        ...submissionfilter["entity_details.financial_period_ends"],
        $lte: filters.filter_financial_period_range_end
      };
    }

    let limit = 100;
    let isFiltering = false;
    if (Object.keys(filters).length > 0 && Object.keys(submissionfilter).length > 0) {
      limit = process.env.MAX_QUERY_SIZE ? Number(process.env.MAX_QUERY_SIZE) : 5000 ;
      isFiltering = true;
    }


    let entries = await EntryModel.find(submissionfilter, {
      _id: 1,
      created_by: 1,
      company_data: 1,
      company: 1,
      status: 1,
      createdAt: 1,
      entity_details: 1,
      requested_information: 1,
      client_returned_information: 1,
      reopened: 1,
      exported_at: 1
    }).sort({ createdAt: -1 }).limit(limit).exec();

    const archivedEntries = await ArchivedEntryModel.find(submissionfilter, {
      _id: 1,
      created_by: 1,
      company_data: 1,
      company: 1,
      status: 1,
      createdAt: 1,
      entity_details: 1,
      requested_information: 1,
      client_returned_information: 1,
      reopened: 1,
      exported_at: 1
    }).sort({ createdAt: -1 }).limit(limit).exec();
    entries = [ ...entries, ...archivedEntries ];

    let search_result = [];
    for (let idx = 0; idx < entries.length; idx++) {
      const requestedInformationList = entries[idx].requested_information?.details || [];
      const returnedInformationList = entries[idx].client_returned_information?.details || [];

      const lastRFI = requestedInformationList && requestedInformationList.length
        ? requestedInformationList[requestedInformationList.length - 1] : null;
      const lastRFIResponse = returnedInformationList && returnedInformationList.length
        ? returnedInformationList[returnedInformationList.length - 1] : null;


      const completed_at = lastRFI && lastRFI.status !== 'CANCELLED' && lastRFIResponse && lastRFIResponse.request_id === lastRFI.id ? lastRFIResponse.returned_at : null;
      let past_deadline = completed_at && ( lastRFIResponse.returned_at > lastRFI.deadline_at );

      let singleResult = {
        _id: entries[idx]._id,
        company: entries[idx].company,
        entity_name: entries[idx].company_data.name,
        code: entries[idx].company_data.code,
        masterclientcode: entries[idx].company_data.masterclientcode,
        financial_period_ends: entries[idx].entity_details
          ? entries[idx].entity_details.financial_period_ends
          : null,
        status: entries[idx].status,
        exported_at: entries[idx].exported_at || null,
        rfi_at: lastRFI && lastRFI.status !== 'CANCELLED' && lastRFI.requested_at ? lastRFI.requested_at : null,
        rfi_deadline_at: lastRFI && lastRFI.status !== 'CANCELLED' && lastRFI.deadline_at ? lastRFI.deadline_at : null,
        rfi_deadline_before_today: lastRFI && lastRFI.status !== 'CANCELLED' && !completed_at && lastRFI.deadline_at &&
          lastRFI.deadline_at <= moment().utc().toDate(),
        rfi_completed_at: completed_at,
        last_reminder: lastRFI && lastRFI.reminders?.length > 0 ? lastRFI.reminders[lastRFI.reminders.length -1 ] : null,
        show_info_details: (entries[idx].reopened?.details?.length > 0 ||
           entries[idx].requested_information?.details?.length > 0 ||
           entries[idx].financial_period_changes?.details?.length > 0),
        completed_with_past_deadline: past_deadline
      };
      search_result.push(singleResult);
    }

    if (search_result.length > 0){

      if(rfi_filter_completed && rfi_filter_not_completed && !rfi_filter_not_rfi_started){
          search_result = search_result.filter((r) => r.rfi_at !== null);
      }
      else if (rfi_filter_completed && !rfi_filter_not_completed && !rfi_filter_not_rfi_started){
          search_result = search_result.filter((r) =>  r.rfi_completed_at !== null);
      }
      else if (rfi_filter_completed  && rfi_filter_not_rfi_started && !rfi_filter_not_completed){
        search_result = search_result.filter((r) =>  r.rfi_completed_at !== null ||  r.rfi_at === null);
      }
      else if ( rfi_filter_not_completed && !rfi_filter_completed  && !rfi_filter_not_rfi_started){
          search_result = search_result.filter((r) => r.rfi_at && r.rfi_completed_at === null);
      }
      else if (rfi_filter_not_completed  && rfi_filter_not_rfi_started && !rfi_filter_completed){
        search_result = search_result.filter((r) =>  r.rfi_completed_at === null);
      }
      else if (rfi_filter_not_rfi_started && !rfi_filter_completed  && !rfi_filter_not_completed){
          search_result = search_result.filter((r) => r.rfi_at === null);
      }

      if (rfi_show_past_deadline){
        search_result = search_result.filter((r) => r.rfi_deadline_before_today === true || r.completed_with_past_deadline === true);
      }

      search_result.sort((a, b) => b.financial_period_ends - a.financial_period_ends);

    }

    res.render("substance/requests-information", {
      user: req.session.user,
      title: "Information Requests",
      result: search_result,
      filters: req.query,
      authentication: req.session.authentication,
      showLimitAlert: entries.length >= limit && isFiltering,
      STANDARD_DATE_FORMAT
    });

  }catch (e) {
    console.log("error ", e);
    const err = new Error("Internal server error");
    err.status = httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR;
    return next(err);
  }
};

/**
 * Generate the query to search on db and the filters object to return to the the view of substance search
 * @function parseSubstanceSearchQuery
 * @param {object}  filters filters object from view.
 * @return dbEntry, viewFilters
 */
function parseSubstanceSearchQuery(filters){
  const company = filters.company;
  const mcc = filters.masterclientcode;
  const referralOffice = filters.referralOffice;
  const status = filters.status || "ALL";
  let relevantActivities = filters.relevantActivities || "";

  const submittedDateRangeStart = filters.submittedDateRangeStart ? new Date(filters.submittedDateRangeStart) : null;
  const submittedDateRangeEnd = filters.submittedDateRangeEnd ? new Date(filters.submittedDateRangeEnd) : null;
  
  const incorporatedDateRangeStart = filters.incorporatedDateRangeStart ? new Date(filters.incorporatedDateRangeStart) : null;
  const incorporatedDateRangeEnd = filters.incorporatedDateRangeEnd ? new Date(filters.incorporatedDateRangeEnd) : null;

  const financialPeriodEndDateRangeStart = filters.financialPeriodEndDateRangeStart ? new Date(filters.financialPeriodEndDateRangeStart) : null;
  const financialPeriodEndDateRangeEnd = filters.financialPeriodEndDateRangeEnd ? new Date(filters.financialPeriodEndDateRangeEnd) : null;

  const paymentDateRangeStart = filters.paymentDateRangeStart ? new Date(filters.paymentDateRangeStart) : null;
  const paymentDateRangeEnd = filters.paymentDateRangeEnd ? new Date(filters.paymentDateRangeEnd) : null;


  const isOutsideBVI = filters.residentOutsideBvi;
  const filterAlreadySubmitted = filters.alreadySubmitted || "ALL";
  const allowReopen = filters.allowReopen || "ALL";


  let dbQuery = {}
  let viewFilters = filters;

  if (company && company.length > 2) {
    dbQuery["company_data.name"] = { $regex: company,  $options: "i"};
  }

  if (mcc && mcc.length > 2) {
    dbQuery["company_data.masterclientcode"] = { $regex: mcc, $options: "i"};
  }

  if (referralOffice && referralOffice.length > 2) {
    dbQuery["company_data.referral_office"] = { $regex: referralOffice, $options: "i" };
  }

  if (isOutsideBVI && isOutsideBVI == "YES") {
    relevantActivities = "";
    dbQuery["tax_residency.resident_in_BVI"] = false;
  }

  if (status !== "ALL") {
    if (Array.isArray(status)){
      dbQuery["status"] = { "$in": status } ;
      viewFilters.status = status
    }else{
      dbQuery["status"] = status;
      viewFilters.status = [status];
    }
   
  }

  if (allowReopen !== "ALL") {
    if (allowReopen === "YES") {
      dbQuery["status"] = { "$in": ["SUBMITTED", "PAID"] }
    }
    if (allowReopen === "NO") {
      dbQuery["status"] = { "$nin": ["SUBMITTED", "PAID"] }
    }
  }

  if (relevantActivities !== ""){
    if (Array.isArray(relevantActivities)) {
      for (let relevantActivity of relevantActivities) {
        dbQuery[`relevant_activities.${relevantActivity}.selected`] = true;
      }
      viewFilters.relevantActivities = relevantActivities;
    } else {
      dbQuery[`relevant_activities.${relevantActivities}.selected`] = true;
      viewFilters.relevantActivities = [relevantActivities];
    }
  }

  if (submittedDateRangeStart) {
    if(!dbQuery["submitted_at"]) {
      dbQuery["submitted_at"] = {};
    }
    dbQuery["submitted_at"]["$gte"] = submittedDateRangeStart;
  }
  if (submittedDateRangeEnd) {
    if(!dbQuery["submitted_at"]) {
      dbQuery["submitted_at"] = {};
    }
    dbQuery["submitted_at"]["$lte"] = submittedDateRangeEnd;
  }
  if (incorporatedDateRangeStart) {
    if(!dbQuery["company_data.incorporationdate"]) {
      dbQuery["company_data.incorporationdate"] = {};
    }
    dbQuery["company_data.incorporationdate"]["$gte"] = incorporatedDateRangeStart;
  }
  if (incorporatedDateRangeEnd) {
    if(!dbQuery["company_data.incorporationdate"]) {
      dbQuery["company_data.incorporationdate"] = {};
    }
    dbQuery["company_data.incorporationdate"]["$lte"] = incorporatedDateRangeEnd;
  }
  if (financialPeriodEndDateRangeStart) {
    if(!dbQuery["entity_details.financial_period_ends"]) {
      dbQuery["entity_details.financial_period_ends"] = {};
    }
    dbQuery["entity_details.financial_period_ends"]["$gte"] = financialPeriodEndDateRangeStart;
  }
  if (financialPeriodEndDateRangeEnd) {
    if(!dbQuery["entity_details.financial_period_ends"]) {
      dbQuery["entity_details.financial_period_ends"] = {};
    }
    dbQuery["entity_details.financial_period_ends"]["$lte"] = financialPeriodEndDateRangeEnd;
  }
  if (paymentDateRangeStart) {
    if(!dbQuery["payment.payment_received_at"]) {
      dbQuery["payment.payment_received_at"] = {};
    }
    dbQuery["payment.payment_received_at"]["$gte"] = paymentDateRangeStart;
  }
  if (paymentDateRangeEnd) {
    if(!dbQuery["payment.payment_received_at"]) {
      dbQuery["payment.payment_received_at"] = {};
    }
    dbQuery["payment.payment_received_at"]["$lte"] = paymentDateRangeEnd;
  }

  if (filterAlreadySubmitted !== "ALL"){
    if (filterAlreadySubmitted === "YES"){
      dbQuery["submitted_at"]["$exists"] = true;
      dbQuery["submitted_at"]["$ne"] = null;
    }

    if (filterAlreadySubmitted === "NO"){
      dbQuery["submitted_at"]["$in"] = [null, undefined];
    }
  }



  return { dbQuery, viewFilters};
}