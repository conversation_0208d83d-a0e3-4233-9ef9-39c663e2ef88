const multer = require('multer');
const multerAzureStorage = require('../classes/MulterAzureStorage');
const {
    filereview: FileReviewModel,
} = require('../models/filereview');
const NaturalPersonModel = require('../models/naturalperson');
const OrganizationModel = require('../models/organization');
const ClientReviewModel = require('../models/client');
const relationController = require('./relations/relationController');
const { v4: uuidv4 } = require('uuid');
const { renameBlob } = require('../utils/azureStorage');

exports.uploadFile = multer({
    storage: new multerAzureStorage({
        containerName: process.env.AZURE_STORAGE_CONTAINER_FILEREVIEW,
        accessKey: process.env.AZURE_STORAGE_ACCESS_KEY,
        accountName: process.env.AZURE_STORAGE_ACCOUNT,
    }),
});

exports.uploadSubstanceFile = multer({
    storage: new multerAzureStorage({
        containerName: process.env.AZURE_STORAGE_CONTAINER_SUBSTANCE_UPLOADS,
        accessKey: process.env.AZURE_STORAGE_ACCESS_KEY,
        accountName: process.env.AZURE_STORAGE_ACCOUNT,
    })
});

const doMoveUpload = async function (fileReview, file, fileType) {
    try {
      if (file) {
        const newName = fileReview._id + '/' + file.blobName.replace('fileUploaded', fileType);
        await renameBlob(
          process.env.AZURE_STORAGE_ACCOUNT,
          process.env.AZURE_STORAGE_ACCESS_KEY,
          process.env.AZURE_STORAGE_CONTAINER_FILEREVIEW, 
          file.blobName,
          newName
        );
      }
    } catch (error) {
        console.log("ERROR: ", error);
    }
};

exports.moveUpload = doMoveUpload;

exports.saveUpload = async function (req, res, next) {
    try {
        const review = await FileReviewModel.findById(req.params.id);
        if (!review) {
            console.log("error not found");
            const err = new Error('File Review not found');
            err.status = 404;
            return next(err);
        }

        let uploadedFiles = req.files['fileUploaded'];
        if (uploadedFiles && uploadedFiles.length > 0) {
            let fileReferences = [];
            uploadedFiles = uploadedFiles.map((itemToUpload) => {
                doMoveUpload(review, itemToUpload, req.body.fileType).catch((reason => {
                    if (reason) {
                        console.log(reason);
                    }
                }));
                const updatedItem = {
                    fileId: uuidv4(),
                    fileTypeId: req.body.rowIndex,
                    fieldName: itemToUpload.fieldname.replace(/fileUploaded/i, req.body.fileType),
                    blob: itemToUpload.blob.replace(/fileUploaded/i, req.body.fileType),
                    blobName: itemToUpload.blobName.replace(/fileUploaded/i, req.body.fileType),
                    url: itemToUpload.url.replace(/fileUploaded/i, req.params.id + '/' + req.body.fileType),
                    originalName: itemToUpload.originalname,
                    encoding: itemToUpload.encoding,
                    mimeType: itemToUpload.mimetype,
                    container: itemToUpload.container,
                    blobType: itemToUpload.blobType,
                    size: itemToUpload.size,
                    etag: itemToUpload.etag
                };
                fileReferences.push(updatedItem.fileId);
                return updatedItem;
            });

            if (req.body.fileGroup === 'standard') {
                const clientReview = await ClientReviewModel.findById(review.clientId);

                if (!clientReview) {
                    const err = new Error('Client Review not found');
                    err.status = 404;
                    return next(err);
                }
                clientReview.files = [...clientReview.files, ...uploadedFiles];
                await clientReview.save();

                const fileIndex = review.files.findIndex((file) => file.id === req.body.rowIndex);
                if (fileIndex !== -1) {
                    const reviewFile = review.files[fileIndex];
                    reviewFile.uploadFiles = [...reviewFile.uploadFiles, ...fileReferences];
                    reviewFile.present = true;
                    review.files.set(fileIndex, reviewFile);

                    await review.save();
                }
            }
        }
        return res.json({result: true});

    } catch (error) {
        console.log("error: ", error);
        return res.status(500).end();
    }



};

exports.deleteFile = async function (req, res) {
    try {
        let file;
        let selectedFileType;
        let fileIndex;
        if (req.body.type === "standard") {
            let review = await FileReviewModel.findById(req.body.reviewId);

            if (!review){
                return res.status(400).end();
            }
            const clientReview = await ClientReviewModel.findById(review.clientId);

            fileIndex = review.files.findIndex((file) => file.id === req.body.rowId);
            if (fileIndex > -1){
                const fileToDelete = review.files[fileIndex];
                const indexFileToDelete = fileToDelete.uploadFiles.findIndex((f) => f === req.body.fileId);
                if (indexFileToDelete > -1){
                    fileToDelete.uploadFiles.splice(indexFileToDelete, 1);
                }
                review.files.set(fileIndex, fileToDelete);
                await review.save();
            }

            fileIndex = clientReview.files.findIndex((file) => file.fileId === req.body.fileId);
            if (fileIndex > -1){
                clientReview.files.splice(fileIndex, 1);
                await clientReview.save();
            }

        } else {
            if (req.body.reviewId){
                file = req.body.type === "natural" ? await NaturalPersonModel.findById(req.body.reviewId) :
                    await OrganizationModel.findById(req.body.reviewId);
                if (file) {

                    selectedFileType = file[req.body.group];

                    if (selectedFileType) {
                        const fileIndex = selectedFileType.files.findIndex((file) => file.id === req.body.rowId);

                        if (fileIndex > -1){
                            const fileToDelete = selectedFileType.files[fileIndex];
                            const indexFileToDelete = fileToDelete.uploadFiles.findIndex((f) => f.fileId === req.body.fileId);
                            if (indexFileToDelete > -1){
                                fileToDelete.uploadFiles.splice(indexFileToDelete, 1);

                                file[req.body.group].files.set(fileIndex, fileToDelete);
                                await file.save();
                            }
                        }
                    }

                }
            }


            if (req.body.deleteTempFiles) {
                const tempFilesGroup = relationController.getTempUploadFiles(req.session, req.body.group);
                if(tempFilesGroup){
                    const tempFiles = tempFilesGroup[req.body.row] ? tempFilesGroup[req.body.row] : [];
                    if (tempFiles.length){
                        const fileIndex = tempFiles.find((uploadFile) => uploadFile.fileId === req.body.fileId);
                        if (fileIndex !== -1) {
                            tempFiles.splice(fileIndex, 1);
                            relationController.setTempUploadFiles(req, req.body.group, tempFilesGroup);
                        }
                    }
                }
            }
        }

        return res.json({result: true});
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};

exports.moveUploadSubstanceFile = async function(id, file, fileType, container) {
    try {
      if (file) {
        const newName = id + '/' + file.blobName.replace('fileUploaded', fileType);
        await renameBlob(
          process.env.AZURE_STORAGE_ACCOUNT,
          process.env.AZURE_STORAGE_ACCESS_KEY,
          container, 
          file.blobName,
          newName
        );
      }
    } catch (error) {
        console.log("ERROR: ", error);
        return error;
    }
};
