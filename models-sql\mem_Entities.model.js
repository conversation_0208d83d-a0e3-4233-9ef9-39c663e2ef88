const moment = require('moment');

module.exports = (sequelize, Sequelize) => {
    const Entities = sequelize.define(
        'mem_Entities',
        {
            ClientCode: {
                type: Sequelize.STRING(10),
                allowNull: true
            },
            ClientName: {
                type: Sequelize.STRING(356),
                allowNull: true
            },
            ClientUniqueNr: {
                type: Sequelize.INTEGER,
                allowNull: false
            },
            EntityCode: {
                type: Sequelize.STRING(10),
                allowNull: false
            },
            EntityName: {
                type: Sequelize.STRING(356),
                allowNull: true
            },
            EntityUniqueNr: {
                type: Sequelize.INTEGER,
                allowNull: false,
                primaryKey: true
            },
            IncorporationNumber: {
                type: Sequelize.STRING(30),
                allowNull: true
            },
            IncorporationDate: {
                type: Sequelize.DATE,
                allowNull: true,
                get() {
                    const d = this.getDataValue('IncorporationDate');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            JurisdictionCode: {
                type: Sequelize.STRING(10),
                allowNull: true
            },
            Jurisdiction: {
                type: Sequelize.STRING(50),
                allowNull: true
            },
            EntityTypeCode: {
                type: Sequelize.STRING(10),
                allowNull: true
            },
            EntityType: {
                type: Sequelize.STRING(50),
                allowNull: true
            },
            EntityLegacyID: {
                type: Sequelize.STRING(30),
                allowNull: true
            },
            ProductionOffice: {
                type: Sequelize.STRING(10),
                allowNull: true
            },
            EntityStatusCode: {
                type: Sequelize.STRING(4),
                allowNull: true
            },
            EntityStatus: {
                type: Sequelize.STRING(50),
                allowNull: true
            },
            EntitySubStatusCode: {
                type: Sequelize.STRING(10),
                allowNull: true
            },
            EntitySubStatus: {
                type: Sequelize.STRING(50),
                allowNull: true
            },
            STXCode: {
                type: Sequelize.STRING(20),
                allowNull: true
            },
            STXName: {
                type: Sequelize.STRING(255),
                allowNull: true
            },
            STXTicker: {
                type: Sequelize.STRING(100),
                allowNull: true
            },
            STXJurisdictionCode: {
                type: Sequelize.STRING(10),
                allowNull: true
            },
            STXJurisdiction: {
                type: Sequelize.STRING(50),
                allowNull: true
            },
            STXRegulatorCode: {
                type: Sequelize.STRING(100),
                allowNull: true
            },
            STXRegulator: {
                type: Sequelize.STRING(35),
                allowNull: true
            },
            STXReg: {
                type: Sequelize.STRING(35),
                allowNull: true
            },
            STXExchng: {
                type: Sequelize.STRING(35),
                allowNull: true
            },
            STXListingDate: {
                type: Sequelize.DATE,
                allowNull: true,
                get() {
                    const d = this.getDataValue('STXListingDate');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            BusRegNr: {
                type: Sequelize.STRING(35),
                allowNull: true
            },
            BusRegTypeCode: {
                type: Sequelize.STRING(4),
                allowNull: true
            },
            BusRegType: {
                type: Sequelize.STRING(50),
                allowNull: true
            },
            BusRegStartDate: {
                type: Sequelize.DATE,
                allowNull: true,
                get() {
                    const d = this.getDataValue('BusRegStartDate');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            BusRegEndDate: {
                type: Sequelize.DATE,
                allowNull: true,
                get() {
                    const d = this.getDataValue('BusRegEndDate');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            }
        },
        {
            sequelize,
            tableName: 'mem_Entities',
            schema: 'dbo',
            timestamps: false
        }
    );

    return Entities;
};
