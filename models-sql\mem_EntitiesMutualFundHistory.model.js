const moment = require('moment');

module.exports = (sequelize, Sequelize) => {
    const mem_EntitiesMutualFundHistory = sequelize.define(
        'mem_EntitiesMutualFundHistory',
        {
            Id: {
                type: Sequelize.INTEGER,
                allowNull: false,
                primaryKey: true,
                autoIncrement: true
            },
            ClientCode: {
                type: Sequelize.STRING(10),
                allowNull: true
            },
            ClientName: {
                type: Sequelize.STRING(356),
                allowNull: true
            },
            ClientUniqueNr: {
                type: Sequelize.INTEGER,
                allowNull: false
            },
            EntityCode: {
                type: Sequelize.STRING(10),
                allowNull: false
            },
            EntityName: {
                type: Sequelize.STRING(356),
                allowNull: true
            },
            EntityUniqueNr: {
                type: Sequelize.INTEGER,
                allowNull: false
            },
            IncorporationNumber: {
                type: Sequelize.STRING(30),
                allowNull: true
            },
            IncorporationDate: {
                type: Sequelize.DATE,
                allowNull: true,
                get() {
                    const d = this.getDataValue('IncorporationDate');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            JurisdictionCode: {
                type: Sequelize.STRING(10),
                allowNull: true
            },
            Jurisdiction: {
                type: Sequelize.STRING(50),
                allowNull: true
            },
            EntityTypeCode: {
                type: Sequelize.STRING(10),
                allowNull: true
            },
            EntityType: {
                type: Sequelize.STRING(50),
                allowNull: true
            },
            EntityLegacyID: {
                type: Sequelize.STRING(30),
                allowNull: true
            },
            ProductionOffice: {
                type: Sequelize.STRING(10),
                allowNull: true
            },
            EntityStatusCode: {
                type: Sequelize.STRING(4),
                allowNull: true
            },
            EntityStatus: {
                type: Sequelize.STRING(50),
                allowNull: true
            },
            EntitySubStatusCode: {
                type: Sequelize.STRING(10),
                allowNull: true
            },
            EntitySubStatus: {
                type: Sequelize.STRING(50),
                allowNull: true
            },
            BusRegNr: {
                type: Sequelize.STRING(35),
                allowNull: true
            },
            BusRegTypeCode: {
                type: Sequelize.STRING(4),
                allowNull: true
            },
            BusRegType: {
                type: Sequelize.STRING(50),
                allowNull: true
            },
            BusRegStartDate: {
                type: Sequelize.DATE,
                allowNull: true,
                get() {
                    const d = this.getDataValue('BusRegStartDate');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            BusRegEndDate: {
                type: Sequelize.DATE,
                allowNull: true,
                get() {
                    const d = this.getDataValue('BusRegEndDate');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            UpdateRequestDate: {
                type: Sequelize.DATE,
                allowNull: true,
                get() {
                    const d = this.getDataValue('UpdateRequestDate');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            ConfirmedDate: {
                type: Sequelize.DATE,
                allowNull: true,
                get() {
                    const d = this.getDataValue('ConfirmedDate');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            Status: {
                type: Sequelize.STRING(50),
                allowNull: true
            },
            UserEmail: {
                type: Sequelize.STRING(255),
                allowNull: true
            },
            TypeOfUpdateRequest: {
                type: Sequelize.STRING(255),
                allowNull: true
            },
            UpdateRequestComments: {
                type: Sequelize.STRING(2500),
                allowNull: true
            },
            CreatedAt: {
                type: Sequelize.DATE,
                allowNull: true,
                get() {
                    const d = this.getDataValue('CreatedAt');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            UpdatedAt: {
                type: Sequelize.DATE,
                allowNull: true,
                get() {
                    const d = this.getDataValue('UpdatedAt');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            }
        },
        {
            sequelize,
            tableName: 'mem_EntitiesMutualFundHistory',
            schema: 'dbo',
            timestamps: false
        }
    );

    return mem_EntitiesMutualFundHistory;
};
