const moment = require('moment');

module.exports = (sequelize, Sequelize) => {
    const ShareholdersHistory = sequelize.define(
        'mem_ShareholdersHistory',
        {
            Id: {
                type: Sequelize.INTEGER,
                allowNull: false,
                primaryKey: true,
                autoIncrement: true
            },
            UniqueRelationID: Sequelize.STRING(42),
            ClientCode: Sequelize.STRING(10),
            ClientName: Sequelize.STRING(356),
            ClientUniqueNr: {
                type: Sequelize.INTEGER,
                allowNull: false
            },
            EntityCode: {
                type: Sequelize.STRING(10),
                allowNull: false
            },
            EntityName: Sequelize.STRING(356),
            EntityUniqueNr: {
                type: Sequelize.INTEGER,
                allowNull: false
            },
            EntityLegacyID: Sequelize.STRING(30),
            RelationType: {
                type: Sequelize.STRING(20),
                allowNull: false
            },
            SHCode: Sequelize.STRING(10),
            SHName: Sequelize.STRING(356),
            SHUniqueNr: Sequelize.INTEGER,
            SHFileTypeCode: Sequelize.STRING(1),
            SHFileType: Sequelize.STRING(50),
            MemberTypeCode: {
                type: Sequelize.STRING(20),
                allowNull: false
            },
            MemberType: {
                type: Sequelize.STRING(255),
                allowNull: false
            },
            MemberCode: Sequelize.STRING(10),
            MemberUniqueNr: Sequelize.INTEGER,
            MemberName: Sequelize.STRING(356),
            MemberFileTypeCode: Sequelize.STRING(1),
            MemberFileType: Sequelize.STRING(50),
            MemberDateStart: {
                type: Sequelize.DATE,
                get() {
                    const d = this.getDataValue('MemberDateStart');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            MemberDateEnd: {
                type: Sequelize.DATE,
                get() {
                    const d = this.getDataValue('MemberDateEnd');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            ShareTypeCode: Sequelize.STRING(2),
            ShareClassName: Sequelize.STRING(100),
            SHVotingRights: Sequelize.STRING(16),
            ShareIssueDate: {
                type: Sequelize.DATE,
                get() {
                    const d = this.getDataValue('ShareIssueDate');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            SHCertNr: Sequelize.STRING(10),
            NrOfShares: Sequelize.DECIMAL,
            SHAddress: {
                type: Sequelize.TEXT,
                allowNull: false
            },
            BenOwnerCode: Sequelize.STRING(10),
            BenOwner: Sequelize.STRING(356),
            BenOwnerCertNr: Sequelize.STRING(10),
            ShareholderID: Sequelize.STRING(100),
            UpdateRequestDate: {
                type: Sequelize.DATE,
                get() {
                    const d = this.getDataValue('UpdateRequestDate');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            ConfirmedDate: {
                type: Sequelize.DATE,
                get() {
                    const d = this.getDataValue('ConfirmedDate');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            Status: Sequelize.STRING(50),
            UserEmail: Sequelize.STRING(255),
            TypeOfUpdateRequest: Sequelize.STRING(255),
            UpdateRequestComments: Sequelize.STRING(2500),
            CreatedAt: {
                type: Sequelize.DATE,
                get() {
                    const d = this.getDataValue('CreatedAt');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            UpdatedAt: {
                type: Sequelize.DATE,
                get() {
                    const d = this.getDataValue('UpdatedAt');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            hasNomineeArrangement: Sequelize.BOOLEAN
        },
        {
            sequelize,
            tableName: 'mem_ShareholdersHistory',
            schema: 'dbo',
            timestamps: false
        }
    );

    ShareholdersHistory.associate = models => {
        ShareholdersHistory.belongsTo(models.mem_MemberProfiles, {
            foreignKey: 'MemberUniqueNr',
            targetKey: 'MFUniqueNr'
        });
    };

    return ShareholdersHistory;
};
