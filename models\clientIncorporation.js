const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

const fileSchema = new mongoose.Schema({
  fileId: { type: String, required: true, default: uuidv4 },
  fileTypeId: { type: String, required: false },
  fieldName: { type: String, required: true },
  originalName: { type: String, required: true },
  encoding: { type: String, required: false },
  mimeType: { type: String, required: true },
  blobName: { type: String, required: true },
  container: { type: String, required: true },
  blob: { type: String, required: true },
  blobType: { type: String, required: true },
  size: { type: String, required: false },
  etag: { type: String, required: false },
  url: { type: String, required: true },
});

const commentSchema = new mongoose.Schema({
  username: { type: String, required: true },
  comment: { type: String, required: false },
  date: { type: Date, required: true },
  status: { type: String, required: false },
});

const requestInformationSchema = new mongoose.Schema({
  username: { type: String, required: false },
  requireProvideFiles: { type: Boolean, required: false },
  requestedAt: { type: Date, required: false },
  comment: { type: String, required: false },
  answer: {type: String, required: false},
  files: [fileSchema]
});

const returnedInformationSchema = new mongoose.Schema({
  username: { type: String, required: false },
  returnedAt: { type: Date, required: false },
  comment: { type: String, required: false },
  files: [fileSchema]
});


const assetSchema = new mongoose.Schema({
  type: { type: String, required: true },
  registrationNumber: { type: String, required: false },
  jurisdictionOfRegistration: { type: String, required: false },
  details: { type: String, required: false },
  nameOfInstitution: { type: String, required: false },
  addressOfInstitution: { type: String, required: false },
  nameOfBank: { type: String, required: false },
  addressOfBank: { type: String, required: false },
  nameOfTrust: { type: String, required: false },
  realEstateType: { type: String, required: false },
  location: { type: String, required: false },
});

const invoiceItemSchema = new mongoose.Schema({
  title: { type: String, required: false },
  description: { type: String, required: false },
  value: { type: Number, required: false },
});

const paymentSchema = new mongoose.Schema({
  total: { type: Number, required: false },
  fees: [invoiceItemSchema],
  disbursements : [invoiceItemSchema],
  paidAt: { type: Date, required: false, index: true },
  type: { type: String, required: false },
  reference: { type: String, required: false },
  batchpaymentId: { type: String, required: false },
  batchpaymentTransactionId: { type: String, required: false },
  batchpaymentCode: { type: String, required: false },
});

const fundSchema = new mongoose.Schema({
  type: { type: String, required: true },
  nameOfFinancialInstitution: { type: String, required: false },
  details: { type: String, required: false },
  profit: { type: String, required: false },
  nameOfSubsidiary: { type: String, required: false },
  jurisdictionOfOperation: { type: String, required: false },
});

const fileListSchema = new mongoose.Schema({
  // step 1
  structureChartFiles: [fileSchema],
  // step 7
  passportFiles: [fileSchema],
  addressProofFiles: [fileSchema],
  otherDeclarationFiles: [fileSchema],
  officialIncorporationFiles: [fileSchema],
});

const nameReservationSchema = new mongoose.Schema({
  approved: { type: Boolean, required: false },
  approvedName: { type: String, required: false },
  approvedAt: {type: Date, required:false},
  declinedAt: {type: Date, required:false},
  suggestions: { type: [String], required: false },
});


const ClientIncorporationSchema = new mongoose.Schema(
  {
    // Step 1
    name: { type: String, required: false, max: 100 },
    type: { type: String, required: false },
    typeSpecialInstructions: { type: String, required: false },
    companyCode: { type: String, required: false },
    incorporationNr: { type: String, required: false },
    partOfGroup: { type: Boolean, required: false },
    // Step 3
    principalBusinessActivity: { type: String, required: false },
    principalBusinessActivityOther: { type: String, required: false },
    activityJurisdiction: { type: String, required: false },
    taxResidence: { type: String, required: false },
    taxResidencyJurisdiction: { type: String, required: false },
    // Step 4
    ownAssets: { type: Boolean, required: false },
    assets: [assetSchema],
    estimated_value_of_assets: { type: Number, required: false }, 
    funds: [fundSchema],
    bankAccountOwner: { type: String, required: false },
    bankName: { type: String, required: false },
    bankAddress: { type: String, required: false },
    // Step 5
    records: {
      recordHolder: { type: String, required: false },
      primaryAddress: { type: String, required: false },
      secondaryAddress: { type: String, required: false },
      email: { type: String, required: false },
      state: { type: String, required: false },
      city: { type: String, required: false },
      postalCode: { type: String, required: false },
      operationCountry: { type: String, required: false },
    },
    // Step 6
    requestAdditionalServices: { type: Boolean, required: false },
    additionalServices: [String],
    sibaLicence: { type: String, required: false },
    trustLicence: { type: String, required: false },
    otherServices: { type: String, required: false },
    // Step 7
    declaration: {
      // Declarations 1,2 and 3
      information: { type: Boolean, required: false },
      assets: { type: Boolean, required: false },
      termsAndConditions: { type: Boolean, required: false },
      name: { type: String, required: false },
      date: { type: Date, required: false },
      relationToEntity: { type: String, required: false },
      relationToEntityOther: { type: String, required: false },
      phone: { type: String, required: false },
    },
    // General
    submittedAt: { type: Date, required: false },
    submittedBy: {type: String, required: false},
    createdBy: {type: String, required: false},
    status: { type: String, required: false },
    version: { type: String, required: true, default: '2.0' },
    approvedName: { type: Boolean, required: false, default: false },
    nameReservationStatus: { type: String, required: false },
    nameReservationInfo: {type: nameReservationSchema, required: false},
    masterClientCode: { type: String, required: false, max: 100 },
    payment: { type: paymentSchema, required: false },
    invoiceNumber: { Type: String, required: false },
    invoiceExportDate: { Type: Date, required: false },
    incorporationStatus: { type: String, required: false },
    companyId: { type: mongoose.Schema.Types.ObjectId, ref: 'Company' },
    requestInformation: [requestInformationSchema],
    clientReturnedInformation: [returnedInformationSchema],
    user: {
      username: { type: String, required: false },
      name: { type: String, required: false },
      dateAssigned: { type: Date, required: false },
    },
    previousOfficer: { type: String, required: false },
    lastRequestInformationDate:  { type: Date, required: false },
    lastReturnedInformationDate: { type: Date, required: false },
    pendingElectronicIds: { type: Boolean, required: false, default: false },
    relations: [Object] ,
    comments: [commentSchema],
    partitionkey: { type: String, required: false },
    files: fileListSchema,
    exportedBy: { type: String, required: false },
  }, {
    timestamps: { createdAt : 'createdAt', updatedAt : 'updatedAt',exportedAt: 'exportedAt'}
  }
);


//Export model
module.exports = mongoose.model('companyincorporationapplications', ClientIncorporationSchema);
