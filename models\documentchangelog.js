const mongoose = require('mongoose');
const { CompanyMapperSchema } = require('./mappings/companyLabelsMapping');
const { getObjectMappingFieldByFullPath } = require('../utils/schemaUtils');
const UpdatedFieldSchema = new mongoose.Schema(
  {
    fieldPath: { type: String, required: true },
    oldValue: { type: mongoose.Schema.Types.Mixed, required: false },
    newValue: { type: mongoose.Schema.Types.Mixed, required: false },
  }
);

const DocumentChangesLogsSchema = new mongoose.Schema(
  {
    documentId: { type: mongoose.Schema.Types.ObjectId, required: true },
    collectionName: { type: String, required: true },
    modifiedValues: [UpdatedFieldSchema],
    modifiedBy: { type: String, required: false },
  },
  {
    timestamps: true
  }
);


DocumentChangesLogsSchema.methods.getModifiedValuesMapped = function () {
  
  if(this.collectionName === "companies"){
    const mappedCompanySchema = this.modifiedValues.map((item) => {
      let mappingField = getObjectMappingFieldByFullPath(CompanyMapperSchema, item.fieldPath);
      const itemLabel = mappingField ? mappingField.label : item.fieldPath;
      return { 
        fieldPath: item.fieldPath, 
        label: itemLabel,
        oldValue: item.oldValue,
        newValue: item.newValue
      };
    })
    return mappedCompanySchema
  }
  return this.modifiedValues
}

//Export model
module.exports = mongoose.model('documentchanges', DocumentChangesLogsSchema);