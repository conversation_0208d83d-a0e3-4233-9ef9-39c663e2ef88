const mongoose = require('mongoose');
const naturalPersonSchema = require('./naturalperson');
const organizationSchema = require('./organization');
const clientSchema = require('./client');
const ObjectId = require('mongoose').Types.ObjectId;


// // File Review Schema
const fileReviewArchiveSchema = new mongoose.Schema({
    fileReviewId: {type: ObjectId, ref: 'filereview', required: true},
    client: {type: clientSchema.schema, required: true},
    naturalRelations: [naturalPersonSchema.schema],
    organizationRelations: [organizationSchema.schema],
    positions: [naturalPersonSchema.schema],
    updatedAt: {type: Date, required: true},
    createdAt: {type: Date, required: true},
    partitionkey: {type: String, required: true},
});

module.exports = mongoose.model('filereviewarchive', fileReviewArchiveSchema);
