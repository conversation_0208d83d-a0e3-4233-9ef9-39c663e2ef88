const mongoose = require('mongoose');


const ImportProcessInfoSchema = new mongoose.Schema({
  status: { type: String, required: true, default: "PENDING" },
  fileName: { type: String, required: true },
  importedBy: { type: String, required: false },
  importFilePath: { type: String, required: false },
  resultFilePath: { type: String, required: false },
  totalRows: { type: String, required: false, default: 0 },
  successRows: { type: Number, required: false, default: 0 },
  errorRows: { type: Number, required: false, default: 0 },
  retries: { type: Number, required: false, default: 0 },
  partitionkey: { type: String, required: true, default: "importprocessinfo" },
},
  {
    timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' }
  }
);
//Export model
module.exports = mongoose.model('importprocessinfos', ImportProcessInfoSchema);
