const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

const fileSchema = new mongoose.Schema({
  fileId: { type: String, required: true, default: uuidv4 },
  fileTypeId: { type: String, required: false },
  fieldName: { type: String, required: true },
  originalName: { type: String, required: true },
  encoding: { type: String, required: true },
  mimeType: { type: String, required: true },
  blobName: { type: String, required: true },
  container: { type: String, required: true },
  blob: { type: String, required: true },
  blobType: { type: String, required: true },
  size: { type: String, required: true },
  etag: { type: String, required: true },
  url: { type: String, required: true },
});

const urlSchema = new mongoose.Schema({
  url: { type: String, required: true },
  name: { type: String, required: true },
});

const MessageSchema = new mongoose.Schema(
  {
    subject: { type: String, required: true },
    emailSubject:{ type: String, required: false },
    content: { type: String, required: true },
    sendToAll: { type: Boolean, required: false },
    scheduleMessage: { type: Boolean, required: false },
    masterClientCodes: [String],
    scheduledAt: { type: Date, required: false },
    status: { type: String, required: true, max: 100 },
    files: [fileSchema],
    important: { type: Boolean, required: false },
    urls: [urlSchema]
  },
  {
    timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' }
  }
);

//Export model
module.exports = mongoose.model('messages', MessageSchema);
