const CompanyModel = require("../models/company").schema;
const FinancialReportModel = require('../models/financialreport');
const xlsx = require('xlsx');
const moment = require('moment');
const dotenv = require('dotenv');
const fs = require('fs');
const mongoose = require('mongoose');

dotenv.config();

async function runScript() {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });

    const data = new Uint8Array(fs.readFileSync('scripts/lp-companies-data.xlsx'));
    const workbook = xlsx.read(data, {
      type: "array",
      cellText: false,
      cellDates: true,
      sheetStubs: true,
    });

    const result = await createReportsFromExcel(workbook);
    console.log('Script executed successfully', result);
  } catch (error) {
    console.error('Error in the script:', error);
  } finally {
    mongoose.disconnect();
  }
}

function getReportRows(workbook) {
  try {
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const reports = [];

    for (let cell in worksheet) {
      const cellAsString = cell.toString();
      const rowNumber = Number(cellAsString.replace(/\D/g, ''));
      const dataStartRow = 2;
      const reportIndex = rowNumber - dataStartRow;
      const rowName = cellAsString.replace(/[0-9]/g, '');

      if (reportIndex >= 0 && worksheet[cell].v) {
        if (!reports[reportIndex]) {
          reports.push({
            companyCode: '',
            startDate: '',
            endDate: '',
          });
        }
        if (rowName === "A") {
          reports[reportIndex].companyCode = worksheet[cell].v.toString();
        }
        if (rowName === "B") {
          const startDate = worksheet[cell].v;
          reports[reportIndex].startDate = startDate ? new Date(moment(startDate).format('YYYY-MM-DD')) : undefined;
        }
        if (rowName === "C") {
          const endDate = worksheet[cell].v;
          reports[reportIndex].endDate = endDate ? new Date(moment(endDate).format('YYYY-MM-DD')) : undefined;
        }
      }
    }
    return reports;
  } catch (e) {
    console.log("Error processing xlsx data: ", e);
    return [];
  }
}

async function createReportsFromExcel(workbook) {
  try {
    let updateLog = [['Company Code', 'Start Date', 'End Date', 'Update Date', 'Action']];
    const reportsData = getReportRows(workbook);

    console.log("Reports to create: ", reportsData.length);

    for (let i = 0; i < reportsData.length; i++) {
      console.log('Processing ' + (i + 1) + ' from ' + reportsData.length);

      const reportData = reportsData[i];

      try {
        if (!reportData.companyCode || !reportData.startDate || !reportData.endDate) {
          updateLog.push([reportData.companyCode, reportData.startDate, reportData.endDate, new Date(), 'ERROR: Missing required data']);
          continue;
        }

        const company = await CompanyModel.findOne({ code: reportData.companyCode });

        if (!company) {
          updateLog.push([reportData.companyCode, reportData.startDate, reportData.endDate, new Date(), 'ERROR: Company not found']);
          continue;
        }

        const reportId = await createNewReport(company, reportData.startDate, reportData.endDate);

        if (reportId) {
          updateLog.push([reportData.companyCode, reportData.startDate, reportData.endDate, new Date(), 'SUCCESS']);
        } else {
          updateLog.push([reportData.companyCode, reportData.startDate, reportData.endDate, new Date(), 'ERROR: Failed to create report']);
        }

      } catch (error) {
        console.error('Error:', error.message);
        updateLog.push([reportData.companyCode, reportData.startDate, reportData.endDate, new Date(), 'ERROR PROCESSING']);
      }
    }

    console.log("Reports processed: ", updateLog.length - 1);

    const filename = 'create_reports_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
    const logWorkbook = xlsx.utils.book_new();
    const logWorksheet1 = xlsx.utils.aoa_to_sheet(updateLog);

    xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'reports ' + moment.utc().format('YYYY-MM-DD'));
    xlsx.writeFile(logWorkbook, filename);

    return { "success": true, "totalRows": updateLog.length - 1 };
  } catch (e) {
    console.log("Error ", e);
    return { "success": false, error: e };
  }
}

async function createNewReport(company, startDate, endDate) {
  try {
    let report = new FinancialReportModel({
      masterClientCode: company.masterclientcode,
      companyData: company,
      status: "SAVED",
      createdBy: "self created",
      createdAt: new Date(),
      currency: "USD",
      financialPeriod: {
        start: startDate,
        end: endDate,
      },
      files: {
        exemptEvidenceFiles: [],
        copyResolutionFiles: [],
        accountingRecordFiles: [],
        annualReturnFiles: []
      },
      version: "2.0"
    });

    await report.save();
    console.log("Report created for company: ", company.code);
    return report._id;
  } catch (e) {
    console.log("Error creating new report: ", e);
    return null;
  }
}

runScript();