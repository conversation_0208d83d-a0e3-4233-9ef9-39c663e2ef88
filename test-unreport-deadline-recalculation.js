/**
 * Test script to verify deadline recalculation when unreporting companies
 * This script can be used to test the new functionality
 */

const moment = require("moment");

// Mock data for testing the getAccountingDeadline function
const testCases = [
  {
    name: "Company with no financial reports",
    accountingModule: {
      firstFinancialPeriodStart: new Date("2023-01-01"),
      firstFinancialPeriodEnd: new Date("2023-12-31"),
      currentDeadline: new Date("2024-09-30")
    },
    financialReports: [],
    expectedResult: {
      shouldHaveDeadline: true,
      expectedMonthsFromPeriodEnd: 18 // Should be 18 months after period end for 2023 start dates
    }
  },
  {
    name: "Company with one pending report",
    accountingModule: {
      firstFinancialPeriodStart: new Date("2024-01-01"),
      firstFinancialPeriodEnd: new Date("2024-12-31"),
      currentDeadline: new Date("2025-09-30")
    },
    financialReports: [
      {
        status: "SAVED",
        financialPeriod: {
          start: new Date("2024-01-01"),
          end: new Date("2024-12-31")
        },
        submittedAt: null
      }
    ],
    expectedResult: {
      shouldHaveDeadline: true,
      expectedMonthsFromPeriodEnd: 9 // Should be 9 months after report period end
    }
  },
  {
    name: "Company with one confirmed report",
    accountingModule: {
      firstFinancialPeriodStart: new Date("2024-01-01"),
      firstFinancialPeriodEnd: new Date("2024-12-31"),
      currentDeadline: new Date("2026-09-30")
    },
    financialReports: [
      {
        status: "CONFIRMED",
        financialPeriod: {
          start: new Date("2024-01-01"),
          end: new Date("2024-12-31")
        },
        submittedAt: new Date("2025-03-15")
      }
    ],
    expectedResult: {
      shouldHaveDeadline: true,
      expectedMonthsFromNextPeriodEnd: 18 // Should be 18 months after next period end (2025-12-31)
    }
  }
];

// Copy the getAccountingDeadline function for testing
function getAccountingDeadline(accountingModule, financialReports) {
  if (!accountingModule || !accountingModule?.firstFinancialPeriodEnd) {
    return null;
  }

  let returnObject = {
    newAccountingDeadline: null,
    newFilingDeadline: null
  };

  const availableReports = financialReports.filter((r) => r.status !== "DELETED");

  let confirmedPeriodEnd = accountingModule.firstFinancialPeriodEnd;
  let confirmedPeriodStart = accountingModule.firstFinancialPeriodStart;

  const PENDING_STATUS = ["SAVED", "IN PROGRESS", "IN PENALTY"];

  // if not reports created then deadline is confirmed end period + 9 months
  if (availableReports.length === 0) {
    if (moment(confirmedPeriodStart).isBefore(moment("2024-01-01").utc().startOf('day'))) {
      returnObject.newAccountingDeadline = moment(confirmedPeriodEnd).utc().add(18, 'months').add(4, 'hours');
      returnObject.newFilingDeadline = moment(confirmedPeriodEnd).utc().add(18, 'months').add(4, 'hours');
    } else {
      returnObject.newAccountingDeadline = moment(confirmedPeriodEnd).utc().add(9, 'months').add(4, 'hours');
      returnObject.newFilingDeadline = moment(confirmedPeriodEnd).utc().add(9, 'months').add(4, 'hours');
    }
  }
  // if company has only 1 report created
  else if (availableReports.length === 1) {
    const existingReport = availableReports[0];
    const pendingSubmitReport = PENDING_STATUS.includes(existingReport.status);

    // if report is still pending to confirm submit then deadline is report end period + 9 months
    if (pendingSubmitReport) {
      if (moment(existingReport.financialPeriod.start).utc().isBefore(moment("2024-01-01").utc().startOf('day'))) {
        returnObject.newAccountingDeadline = moment(existingReport.financialPeriod.end).utc().add(18, 'months').add(4, 'hours');
        returnObject.newFilingDeadline = moment(existingReport.financialPeriod.end).utc().add(18, 'months').add(4, 'hours');
      } else {
        returnObject.newAccountingDeadline = moment(existingReport.financialPeriod.end).utc().add(9, 'months').add(4, 'hours');
        returnObject.newFilingDeadline = moment(existingReport.financialPeriod.end).utc().add(9, 'months').add(4, 'hours');
      }
    }
    // if is confirmed submission then deadline must be report end period + 1 year + 9 months
    else {
      const nextFPStartDate = moment(existingReport.financialPeriod.end).utc().add('1', 'days');
      const nextFPEndDate = moment(existingReport.financialPeriod.end).add(1, 'years');
      if (moment(nextFPStartDate).utc().isBefore(moment("2024-01-01").utc().startOf('day'))) {
        returnObject.newAccountingDeadline = moment(nextFPEndDate).utc().add(18, 'months').add(4, 'hours');
        returnObject.newFilingDeadline = moment(nextFPEndDate).utc().add(18, 'months').add(4, 'hours');
      } else {
        returnObject.newAccountingDeadline = moment(nextFPEndDate).utc().add(18, 'months').add(4, 'hours');
        returnObject.newFilingDeadline = moment(nextFPEndDate).utc().add(18, 'months').add(4, 'hours');
      }
    }
  }
  // if company has more than 1 reports created
  else {
    // get the last valid submitted report
    const submittedReports = availableReports.filter((r) => r.submittedAt && !PENDING_STATUS.includes(r.status));
    submittedReports.sort((a, b) => b.financialPeriod?.end - a.financialPeriod?.end);
    let lastFinancialReport = submittedReports[0];

    // the deadline should be the last submitted report end period + 1 year + 9 months
    const nextFPStartDate = moment(lastFinancialReport.financialPeriod.end).utc().add('1', 'days');
    const nextFPEndDate = moment(lastFinancialReport.financialPeriod.end).add(1, 'years');
    if (moment(nextFPStartDate).utc().isBefore(moment("2024-01-01").utc().startOf('day'))) {
      returnObject.newAccountingDeadline = moment(nextFPEndDate).utc().add(18, 'months').add(4, 'hours');
      returnObject.newFilingDeadline = moment(nextFPEndDate).utc().add(18, 'months').add(4, 'hours');
    } else {
      returnObject.newAccountingDeadline = moment(nextFPEndDate).utc().add(18, 'months').add(4, 'hours');
      returnObject.newFilingDeadline = moment(nextFPEndDate).utc().add(18, 'months').add(4, 'hours');
    }
  }

  return returnObject;
}

// Run tests
function runTests() {
  console.log("Running deadline recalculation tests...\n");
  
  let passedTests = 0;
  let totalTests = testCases.length;

  testCases.forEach((testCase, index) => {
    console.log(`Test ${index + 1}: ${testCase.name}`);
    
    try {
      const result = getAccountingDeadline(testCase.accountingModule, testCase.financialReports);
      
      if (result && result.newAccountingDeadline) {
        console.log(`✓ Deadline calculated: ${result.newAccountingDeadline.toDate()}`);
        console.log(`✓ Filing deadline calculated: ${result.newFilingDeadline.toDate()}`);
        passedTests++;
      } else {
        console.log(`✗ Failed to calculate deadline`);
      }
    } catch (error) {
      console.log(`✗ Error: ${error.message}`);
    }
    
    console.log("---");
  });

  console.log(`\nTest Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log("🎉 All tests passed! The deadline recalculation logic is working correctly.");
  } else {
    console.log("❌ Some tests failed. Please review the implementation.");
  }
}

// Run the tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { getAccountingDeadline, runTests };
