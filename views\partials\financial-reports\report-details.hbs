<div class="col-lg-12 step1" id="step1v3">
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-12">
                    <h4>BVI Business Companies / Limited Partnerships - Annual Financial Returns</h4>
                    <span>
                        BVI Business Companies are required to file an annual return with their Registered Agent with effect from 1 January
                        2023 and Limited Partnerships from 1 January 2025.

                        <br>
                        Your company financial period starts <span class="finPrdStartText">{{formatDate report.financialPeriod.start "DD MMMM YYYY"}}</span>
                        and ends <span class="finPrdEndText">{{formatDate report.financialPeriod.end "DD MMMM YYYY"}}</span>.
                        <br>
                        <span class="font-weight-bold">Additional details and supporting templates can be found in our <span id="pdfDownloads" class="text-danger underline cursor-pointer"> Financial Return Library <i class="fa fa-info-circle " title="BVI Annual Return Requirements"></i></span
                        > </span>
                    </span>

                </div>
            </div>
            <br>
            <!-- FIELDS FOR SUBMISSIONS VERSION 3.0 -->


            <!-- 1.1 -->
            <div class="row">
                <div class="col-md-8 ">
                    <div class="form-group ">
                        <label class="cursor-default">1.1 Is your company an exempt company?</label>
                    </div>
                </div>
                <div class="col-md-4 text-right ">
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" class="custom-control-input" id="isExemptCompanyYes" name="isExemptCompany"
                            value="YES" {{#ifEquals report.reportDetails.isExemptCompany true}} checked {{/ifEquals}}>
                        <label class="custom-control-label" for="isExemptCompanyYes">
                            Yes
                        </label>
                    </div>
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" class="custom-control-input" id="isExemptCompanyNo" name="isExemptCompany"
                            value="NO" {{#ifEquals report.reportDetails.isExemptCompany false}} checked {{/ifEquals}}>
                        <label class="custom-control-label" for="isExemptCompanyNo">
                            No
                        </label>
                    </div>
                </div>
            </div>

            <!--1.2.1-->
            <div id="isExemptCompanyYesRows" {{#ifEquals report.reportDetails.isExemptCompany true}}
                    class="d-block" {{else}} class="hide-element" {{/ifEquals}}>
                <div class="row" id="exemptCompanyTypeRow" >
                    <div class="col-md-12">
                        <div class="form-group">
                            <select name="exemptCompanyType" id="exemptCompanyType" class="form-control w-100"
                                data-toggle="select2" required>
                                <option value="" hidden>Select an option</option>
                                <option {{#ifEquals report.reportDetails.exemptCompanyType "in-liquidation" }} selected {{/ifEquals}}
                                    value="in-liquidation">
                                    The Company is in liquidation
                                </option>
                                <option {{#ifEquals report.reportDetails.exemptCompanyType "stock-exchange" }} selected {{/ifEquals}}
                                    value="stock-exchange">
                                    The Company is listed on a stock exchange
                                </option>
                                <option {{#ifEquals report.reportDetails.exemptCompanyType "is-regulated" }} selected {{/ifEquals}}
                                    value="is-regulated">
                                    The Company is regulated under a financial services legislation and provides financial statements to
                                    the Financial Services Commission in accordance with the requirements of that financial services
                                    legislation
                                </option>
                                <option {{#ifEquals report.reportDetails.exemptCompanyType "tax-return-filed" }} selected {{/ifEquals}}
                                    value="tax-return-filed">
                                    The Company files its annual tax return to the Inland Revenue Department accompanied by the
                                    Company's financial statements
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div id="exemptCompanyTypeRows" class="hide-element" >
                    <div class="row ">
                        <div class="col-md-8">
                            <div class="form-group  ">
                                <label class="cursor-default" id="exemptEvidenceFileLabel">Provide evidence:</label>
                            </div>
                        </div>
                        <div class="col-md-4 text-right">
                            <button type="button" data-toggle="modal" data-target="#upload-modal"
                                data-mcc="{{masterClientCode}}" data-company-code="{{report.companyData.code}}"
                                data-report-id="{{ report._id }}" data-file-group="Supporting documents" data-file-max="3"
                                data-field="exemptEvidenceFiles" class="btn width-xl{{#if report.files.exemptEvidenceFiles}}
                                uploaded-files-btn{{else}}
                                upload-files-btn {{/if}}">
                
                                {{#if report.files.exemptEvidenceFiles}}
                                Modify
                                {{else}}
                                Upload
                                {{/if}}
                            </button>
                        </div>
                    </div>
                    <br>
                
                    <div class="row ">
                        <div class="col-md-12 ">
                            <div class="form-group">
                                <label class="cursor-default" id="exemptMotivationLabel">
                                    Please provide a motivation for your choice.
                                </label>
                            </div>
                        </div>
                    </div>
                
                    <div class="row ">
                        <div class="col-md-12 ">
                            <textarea class="form-control" type="text" id="exemptCompanyExplanation" rows="2" placeholder="..."
                                name="exemptCompanyExplanation">{{report.reportDetails.exemptCompanyExplanation}}</textarea>
                        </div>
                    </div>
                
                
                </div>
            </div>


            <!-- 1.2 -->
            <div id="isExemptCompanyNoRows" {{#ifEquals report.reportDetails.isExemptCompany false }}  class="d-block" {{else}} class="hide-element" {{/ifEquals}}>
                <div class="row question3" >
                    <div class="col-12">
                        <label class="cursor-default">
                            1.2 Select the service that you would like to avail (one at a time)
                        </label>
                    </div>
                </div>
                
                <div class="row question3" >
                    <div class="col-12 ">
                        {{#ifContains '1.2.1' services}}
                        <div class="custom-control custom-radio ">
                            <input type="radio" class="custom-control-input" id="serviceTypeSelfComplete" name="serviceType" required
                                value="self-service-complete" {{#ifEquals report.reportDetails.serviceType 'self-service-complete' }}
                                checked {{/ifEquals}}>
                            <label class="custom-control-label" for="serviceTypeSelfComplete">
                                1.2.1 Self Service: Complete the Company's Annual Return in the prescribed format <i
                                    class="fa fa-info-circle" aria-hidden="true" data-toggle="tooltip"
                                    data-title="The Company's annual financial return balances are available. The prescribed format is available for clients to complete. Once completed, the portal stores and files the annual return."></i>
                            </label>
                        </div>
                        {{/ifContains}}

                        {{#ifContains '1.2.2' services}}
                        <div class="custom-control custom-radio ">
                            <input type="radio" class="custom-control-input" id="serviceTypeSelfPrepare" name="serviceType" required
                                value="self-service-prepare" {{#ifEquals report.reportDetails.serviceType 'self-service-prepare' }}
                                checked {{/ifEquals}}>
                            <label class="custom-control-label" for="serviceTypeSelfPrepare">
                                1.2.2 Self Service: Prepare the Company's Annual Return using the Trident Accounting Portal <i
                                    class="fa fa-info-circle" aria-hidden="true" data-toggle="tooltip"
                                    data-title="Use the accounting portal to prepare the annual return. Ensure that the Company's financial records are at hand to complete the report."></i>
                            </label>
                        </div>
                        {{/ifContains}}
                
                        {{#ifContains '1.2.3' services}}
                        <div class="custom-control custom-radio ">
                            <input type="radio" class="custom-control-input" id="serviceTypeTridentComplete" name="serviceType" required
                                value="trident-service-complete" {{#ifEquals report.reportDetails.serviceType 'trident-service-complete'
                                }} checked {{/ifEquals}}>
                            <label class="custom-control-label" for="serviceTypeTridentComplete">
                                1.2.3 Trident Service: Reformatting existing annual accounts, to the prescribed format, and submission
                                (starting at
                                $350) <i class="fa fa-info-circle" aria-hidden="true" data-toggle="tooltip"
                                    data-title="Ensure that the Company's financial accounts (Financial statements, management accounts etc.) are available and in pdf format."></i>
                            </label>
                        </div>
                        {{/ifContains}}
                
                        {{#ifContains '1.2.4' services}}
                        <div class="custom-control custom-radio ">
                            <input type="radio" class="custom-control-input" id="serviceTypeTridentDrop" name="serviceType"
                                value="trident-service-drop" {{#ifEquals report.reportDetails.serviceType 'trident-service-drop' }}
                                checked {{/ifEquals}}>
                            <label class="custom-control-label" for="serviceTypeTridentDrop">
                                1.2.4 Trident Service: Preparation of the accounts including a statement of Income and Expenses, and
                                submission of the
                                annual return (starting at $600) <i class="fa fa-info-circle" aria-hidden="true" data-toggle="tooltip"
                                    data-title="Ensure that the Company's accounting records are available in pdf format."></i>
                            </label>
                        </div>
                        {{/ifContains}}
                    </div>
                
                    <input hidden type="text" id="serviceTypeSelfCompleteAmount"
                        value='{{report.companyData.accountingRecordsModule.selfServiceCompleteAnnualReturnAmount}}'>
                    <input hidden type="text" id="serviceTypeSelfPrepareAmount"
                        value='{{report.companyData.accountingRecordsModule.selfServicePrepareAnnualReturnAmount}}'>
                    <input hidden type="text" id="serviceTypeTridentCompleteAmount"
                        value='{{report.companyData.accountingRecordsModule.tridentServiceCompleteAnnualReturnAmount}}'>
                    <input hidden type="text" id="serviceTypeDropAmount"
                        value='{{report.companyData.accountingRecordsModule.tridentServiceDropAccountingRecordsAmount}}'>
                </div>
                
                <div class="row question3" id="firstYearOperationRow" >
                    <div class="col-md-8">
                        <div class="form-group ">
                            <label class="cursor-default">
                                1.3 Is this the company's first year of operation?
                                <i class="fa fa-info-circle" aria-hidden="true" data-toggle="tooltip"
                                    data-title="The Company has just been incorporated and this is its first year of operation."></i>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4 text-right">
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input" id="isFirstYearOperationYes" name="isFirstYearOperation"
                                value="YES" data-is-first-report="{{report.isFirstReport}}" required {{#ifEquals report.reportDetails.isFirstYearOperation true}} checked {{/ifEquals}}
                                {{#ifEquals report.reportDetails.isThereFinancialYearChange true}} disabled {{/ifEquals}}>
                            <label class="custom-control-label" for="isFirstYearOperationYes">Yes</label>
                        </div>
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input" id="isFirstYearOperationNo" required
                                name="isFirstYearOperation" value="NO" data-is-first-report="{{report.isFirstReport}}"
                                {{#ifEquals report.reportDetails.isFirstYearOperation false}} checked {{/ifEquals}} {{#ifCond report.reportDetails.isFirstYearOperation '==' null }} {{#unless
                                report.isFirstReport}} checked {{/unless}} {{/ifCond}}>
                            <label class="custom-control-label" for="isFirstYearOperationNo">No</label>
                        </div>
                    </div>
                </div>
                
                <div class="row " id="changeFinancialYearRow" >
                    <div class="col-md-8">
                        <div class="form-group ">
                            <label class="cursor-default">
                                1.4 Is there a change of financial year?
                                <i class="fa fa-info-circle" aria-hidden="true" data-toggle="tooltip"
                                    data-title="The Company changed its financial year/period. The change of financial year/period should be supported by a signed Director's resolution stating the changes of the financial year/period and the approval to adopt the changes. A copy of this resolution must be uploaded to proceed."></i>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4 text-right">
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input" id="isThereFinancialYearChangeYes"
                                name="isThereFinancialYearChange"  value="YES"  {{#if
                                report.isFirstReport}} disabled {{/if}}
                                {{#ifEquals report.reportDetails.isThereFinancialYearChange true}} checked {{/ifEquals}}>
                            <label class="custom-control-label" for="isThereFinancialYearChangeYes">Yes</label>
                        </div>
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input" id="isThereFinancialYearChangeNo"
                                name="isThereFinancialYearChange"  value="NO" {{#if
                                report.isFirstReport}} disabled {{/if}} 
                                {{#ifEquals report.reportDetails.isThereFinancialYearChange false}} checked {{/ifEquals}}>
                            <label class="custom-control-label" for="isThereFinancialYearChangeNo">No</label>
                        </div>
                    </div>
                </div>
                
                <!-- UPLOAD COPY OF RESOLUTION FILES (IF IS THERE A CHANGE OF FINANCIAL YEAR IS YES) -->
                <div id="uploadCopyResolutionRow" {{#ifCond report.reportDetails.isThereFinancialYearChange '!==' true}} class="hide-element" {{/ifCond}}>
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group  ml-3">
                                <label class="cursor-default">Upload a PDF copy of the Director(s) resolution duly signed and dated
                            </div>
                        </div>
                        <div class="col-md-4 text-right">
                            <button type="button" data-toggle="modal" data-target="#upload-modal"
                                data-mcc="{{masterClientCode}}" data-company-code="{{report.companyData.code}}"
                                data-report-id="{{ report._id }}" data-file-group="Copy of the Resolution signed" data-file-max="3"
                                data-field="copyResolutionFiles" class="btn width-xl{{#if report.files.copyResolutionFiles}}
                                uploaded-files-btn{{else}}
                                upload-files-btn {{/if}}">
                
                                {{#if report.files.copyResolutionFiles}}
                                Modify
                                {{else}}
                                Upload
                                {{/if}}
                            </button>
                        </div>
                    </div>
                
                </div>
                
                <!-- 1.5 -->
                <div class="fiscalYearDiv {{#ifCond report.reportDetails.isThereFinancialYearChange '!==' true}} hide-element {{/ifCond}}">
                    <div class="col-12">
                        <label class="cursor-default">
                            1.4.1 Interim Financial Period
                        </label>
                    </div>
                </div>
                
                
                <div id="financialPeriodRows" class="fiscalYearDiv {{#ifCond report.reportDetails.isThereFinancialYearChange '!==' true}} hide-element {{/ifCond}}">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group ml-2 ">
                                <label class="cursor-default">
                                    <span id="fiscalYearText">******* Please confirm and select the interim financial period of the annual return</span>
                                    <i class="fa fa-info-circle" aria-hidden="true" data-toggle="tooltip" id="financialPeriodInfo"
                                        data-title="Please note that this can be any twelve (12) consecutive month period (e.g, 1 January - 31 December; 1 April - 31 March)"></i>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 pl-0">
                            <div class="row justify-content-end">
                                <div class="custom-control-inline">
                                    <label class="mr-2" for="financialPeriodFrom">From:</label>
                                    <input class="form-control startPeriodDate" type="date" disabled
                                        value="{{formatDate report.financialPeriod.start "YYYY-MM-DD"}}">
                                    <input class="form-control" type="text" name="financialPeriod[start]" hidden
                                        value="{{formatDate report.financialPeriod.start "YYYY-MM-DD"}}">
                                </div>
                                <div class="custom-control-inline">
                                    <label class="mr-2" for="financialPeriodTo">To:</label>
                                    <input class="form-control endPeriodDate" type="date" id="financialPeriodTo"
                                        placeholder="YYYY-MM-DD" name="financialPeriod[end]" min='{{formatDate report.financialPeriod.start "YYYY-MM-DD"}}' max='{{formatDate report.financialPeriod.end "YYYY-MM-DD"}}'
                                        value="{{formatDate report.financialPeriod.end "YYYY-MM-DD"}}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="uploadAnnualRow" class="hide-element" >
                    <div class="row ">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label class="cursor-default">1.5 Upload ready accounts and supporting resolution <a href="/ResolutionofDirectorsAFR.docx">(template)</a>:</label>
                            </div>
                        </div>
                        <div class="col-md-4 text-right">
                            <button type="button"  data-toggle="modal" data-target="#upload-modal"
                                data-mcc="{{masterClientCode}}" data-company-code="{{report.companyData.code}}"
                                data-report-id="{{ report._id }}" data-file-group="Annual Return Files" data-file-max="3"
                                data-field="annualReturnFiles" class="btn width-xl{{#if report.files.annualReturnFiles}}
                                uploaded-files-btn {{else}}
                                upload-files-btn {{/if}}">
                
                                {{#if report.files.annualReturnFiles}}
                                Modify
                                {{else}}
                                Upload
                                {{/if}}
                            </button>
                        </div>
                    </div>
                
                </div>
                
                <div id="uploadAccountingRow" class="hide-element" >
                    <div class="row ">
                        <div class="col-md-8">
                            <div class="form-group ">
                                <label class="cursor-default">1.5 Upload Accounting Records and resolution  <a href="/ResolutionofDirectorsAFR.docx">(template)</a>:
                                    <i class="fa fa-info-circle" aria-hidden="true" data-toggle="tooltip" id="financialPeriodInfo"
                                        data-title="Accounting Records is defined as documents in respect of the legal person's or legal arrangement's assets and liabilities, the receipts and expenditure, sales, 
                                                purchases and other transactions to which the legal person and legal arrangment is a party to. It implies that accounting records (including the underlying documents), 
                                                can take on many forms and includes: (a) bank statements, (b) receipts, (c) invoices, (d) vouchers, (e) title documents, (f) contracts and agreements, (g) ledgers, and 
                                                (h) any other documentation underpinning a transaction."></i>
                                </label>
                
                            </div>
                        </div>
                        <div class="col-md-4 text-right">
                            <button type="button" data-toggle="modal" data-target="#upload-modal"
                                data-mcc="{{masterClientCode}}" data-company-code="{{report.companyData.code}}"
                                data-report-id="{{ report._id }}" data-file-group="Accounting Record files" data-file-max="3"
                                data-field="accountingRecordFiles" class="btn width-xl solid royal-blue{{#if report.files.accountingRecordFiles}}
                                uploaded-files-btn{{else}}
                                upload-files-btn{{/if}}">
                
                                {{#if report.files.accountingRecordFiles}}
                                Modify
                                {{else}}
                                Upload
                                {{/if}}
                            </button>
                        </div>
                    </div>
                
                </div>
                
                <div id="assistanceTextRow" class="hide-element" >
                    <div class="row pl-2">
                        <div class="col-md-12">
                            <div class="form-group ">
                                <label class="cursor-default">
                                    SPECIFY ASSISTANCE REQUIRED
                                </label>
                            </div>
                        </div>
                    </div>
                
                    <div class="row pl-2">
                        <div class="col-md-12 ">
                            <textarea class="form-control" type="text" id="assistanceRequired" rows="2" name="assistanceRequired"
                                placeholder="Confirm here the assistance required including any specifics like reporting currency (default reporting currency is in US Dollars).">{{report.reportDetails.assistanceRequired}}</textarea>
                        </div>
                    </div>
                </div>
                
                <div class="row" id="startFinReportRow" class="hide-element">
                    <div class="col-md-10  ">
                        <div class="form-group ">
                            <label class="mb-2 cursor-default" id="startFinReportlbl">
                                Let's start preparing your Financial Summary/Reports.
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="row" id="startFinReportRow2" class="hide-element">
                    <div class="col-md-10 ">
                        <div class="form-group ">
                            <label class="mb-2 text-bold font-weight-bold cursor-default font-italic">
                                Kindly note that the default reporting currency in this portal is in US Dollars (USD).
                            </label>
                        </div>
                    </div>
                </div>
            </div>


            

            <div class="row mt-2">
                <div class="col-md-12">
                    <div class="progress">
                        <div class="progress-bar width-14" role="progressbar" aria-valuenow="1" id="progressBarDiv"
                            aria-valuemin="1" aria-valuemax="1">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="/views-js/partials/financial-reports/report-details.js"></script>
